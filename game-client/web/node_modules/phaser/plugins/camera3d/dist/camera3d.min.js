var Camera3DPlugin=function(e){var n={};function s(t){if(n[t])return n[t].exports;var i=n[t]={i:t,l:!1,exports:{}};return e[t].call(i.exports,i,i.exports,s),i.l=!0,i.exports}return s.m=e,s.c=n,s.d=function(t,i,e){s.o(t,i)||Object.defineProperty(t,i,{enumerable:!0,get:e})},s.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},s.t=function(i,t){if(1&t&&(i=s(i)),8&t)return i;if(4&t&&"object"==typeof i&&i&&i.__esModule)return i;var e=Object.create(null);if(s.r(e),Object.defineProperty(e,"default",{enumerable:!0,value:i}),2&t&&"string"!=typeof i)for(var n in i)s.d(e,n,function(t){return i[t]}.bind(null,n));return e},s.n=function(t){var i=t&&t.__esModule?function(){return t.default}:function(){return t};return s.d(i,"a",i),i},s.o=function(t,i){return Object.prototype.hasOwnProperty.call(t,i)},s.p="",s(s.s=42)}([function(t,i){function r(t,i,e,n){for(var s in i)if(i.hasOwnProperty(s)){var r=(u=i,c=s,p=l=void 0,p=(f=e)?u[c]:Object.getOwnPropertyDescriptor(u,c),!f&&p.value&&"object"==typeof p.value&&(p=p.value),!(!p||!((l=p).get&&"function"==typeof l.get||l.set&&"function"==typeof l.set))&&(void 0===p.enumerable&&(p.enumerable=!0),void 0===p.configurable&&(p.configurable=!0),p));if(!1!==r){if(o=(n||t).prototype,h=s,a=void 0,(a=Object.getOwnPropertyDescriptor(o,h))&&(a.value&&"object"==typeof a.value&&(a=a.value),!1===a.configurable)){if(d.ignoreFinals)continue;throw new Error("cannot override final property '"+s+"', set Class.ignoreFinals = true to skip")}Object.defineProperty(t.prototype,s,r)}else t.prototype[s]=i[s]}var o,h,a,u,c,f,l,p}function o(t,i){if(i){Array.isArray(i)||(i=[i]);for(var e=0;e<i.length;e++)r(t,i[e].prototype||i[e])}}function d(t){var i,e,n;if((t=t||{}).initialize){if("function"!=typeof t.initialize)throw new Error("initialize must be a function");n=t.initialize,delete t.initialize}else{n=t.Extends?(e=t.Extends,function(){e.apply(this,arguments)}):function(){}}t.Extends?(n.prototype=Object.create(t.Extends.prototype),n.prototype.constructor=n,i=t.Extends,delete t.Extends):n.prototype.constructor=n;var s=null;return t.Mixins&&(s=t.Mixins,delete t.Mixins),o(n,s),r(n,t,!0,i),n}d.extend=r,d.mixin=o,d.ignoreFinals=!1,t.exports=d},function(t,i,e){var n=e(0),s=e(18),r=new n({initialize:function(t,i){this.x=0,this.y=0,"object"==typeof t?(this.x=t.x||0,this.y=t.y||0):(void 0===i&&(i=t),this.x=t||0,this.y=i||0)},clone:function(){return new r(this.x,this.y)},copy:function(t){return this.x=t.x||0,this.y=t.y||0,this},setFromObject:function(t){return this.x=t.x||0,this.y=t.y||0,this},set:function(t,i){return void 0===i&&(i=t),this.x=t,this.y=i,this},setTo:function(t,i){return this.set(t,i)},setToPolar:function(t,i){return null==i&&(i=1),this.x=Math.cos(t)*i,this.y=Math.sin(t)*i,this},equals:function(t){return this.x===t.x&&this.y===t.y},fuzzyEquals:function(t,i){return s(this.x,t.x,i)&&s(this.y,t.y,i)},angle:function(){var t=Math.atan2(this.y,this.x);return t<0&&(t+=2*Math.PI),t},setAngle:function(t){return this.setToPolar(t,this.length())},add:function(t){return this.x+=t.x,this.y+=t.y,this},subtract:function(t){return this.x-=t.x,this.y-=t.y,this},multiply:function(t){return this.x*=t.x,this.y*=t.y,this},scale:function(t){return isFinite(t)?(this.x*=t,this.y*=t):(this.x=0,this.y=0),this},divide:function(t){return this.x/=t.x,this.y/=t.y,this},negate:function(){return this.x=-this.x,this.y=-this.y,this},distance:function(t){var i=t.x-this.x,e=t.y-this.y;return Math.sqrt(i*i+e*e)},distanceSq:function(t){var i=t.x-this.x,e=t.y-this.y;return i*i+e*e},length:function(){var t=this.x,i=this.y;return Math.sqrt(t*t+i*i)},setLength:function(t){return this.normalize().scale(t)},lengthSq:function(){var t=this.x,i=this.y;return t*t+i*i},normalize:function(){var t=this.x,i=this.y,e=t*t+i*i;return 0<e&&(e=1/Math.sqrt(e),this.x=t*e,this.y=i*e),this},normalizeRightHand:function(){var t=this.x;return this.x=-1*this.y,this.y=t,this},normalizeLeftHand:function(){var t=this.x;return this.x=this.y,this.y=-1*t,this},dot:function(t){return this.x*t.x+this.y*t.y},cross:function(t){return this.x*t.y-this.y*t.x},lerp:function(t,i){void 0===i&&(i=0);var e=this.x,n=this.y;return this.x=e+i*(t.x-e),this.y=n+i*(t.y-n),this},transformMat3:function(t){var i=this.x,e=this.y,n=t.val;return this.x=n[0]*i+n[3]*e+n[6],this.y=n[1]*i+n[4]*e+n[7],this},transformMat4:function(t){var i=this.x,e=this.y,n=t.val;return this.x=n[0]*i+n[4]*e+n[12],this.y=n[1]*i+n[5]*e+n[13],this},reset:function(){return this.x=0,this.y=0,this},limit:function(t){var i=this.length();return i&&t<i&&this.scale(t/i),this},reflect:function(t){return t=t.clone().normalize(),this.subtract(t.scale(2*this.dot(t)))},mirror:function(t){return this.reflect(t).negate()},rotate:function(t){var i=Math.cos(t),e=Math.sin(t);return this.set(i*this.x-e*this.y,e*this.x+i*this.y)}});r.ZERO=new r,r.RIGHT=new r(1,0),r.LEFT=new r(-1,0),r.UP=new r(0,-1),r.DOWN=new r(0,1),r.ONE=new r(1,1),t.exports=r},function(t,i){var e={PI2:2*Math.PI,TAU:.5*Math.PI,EPSILON:1e-6,DEG_TO_RAD:Math.PI/180,RAD_TO_DEG:180/Math.PI,RND:null,MIN_SAFE_INTEGER:Number.MIN_SAFE_INTEGER||-9007199254740991,MAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER||9007199254740991};t.exports=e},function(t,i,e){var n=new(e(0))({initialize:function(t,i,e){this.x=0,this.y=0,this.z=0,"object"==typeof t?(this.x=t.x||0,this.y=t.y||0,this.z=t.z||0):(this.x=t||0,this.y=i||0,this.z=e||0)},up:function(){return this.x=0,this.y=1,this.z=0,this},min:function(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this},max:function(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this},clone:function(){return new n(this.x,this.y,this.z)},addVectors:function(t,i){return this.x=t.x+i.x,this.y=t.y+i.y,this.z=t.z+i.z,this},crossVectors:function(t,i){var e=t.x,n=t.y,s=t.z,r=i.x,o=i.y,h=i.z;return this.x=n*h-s*o,this.y=s*r-e*h,this.z=e*o-n*r,this},equals:function(t){return this.x===t.x&&this.y===t.y&&this.z===t.z},copy:function(t){return this.x=t.x,this.y=t.y,this.z=t.z||0,this},set:function(t,i,e){return"object"==typeof t?(this.x=t.x||0,this.y=t.y||0,this.z=t.z||0):(this.x=t||0,this.y=i||0,this.z=e||0),this},setFromMatrixPosition:function(t){return this.fromArray(t.val,12)},setFromMatrixColumn:function(t,i){return this.fromArray(t.val,4*i)},fromArray:function(t,i){return void 0===i&&(i=0),this.x=t[i],this.y=t[i+1],this.z=t[i+2],this},add:function(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z||0,this},addScalar:function(t){return this.x+=t,this.y+=t,this.z+=t,this},addScale:function(t,i){return this.x+=t.x*i,this.y+=t.y*i,this.z+=t.z*i||0,this},subtract:function(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z||0,this},multiply:function(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z||1,this},scale:function(t){return isFinite(t)?(this.x*=t,this.y*=t,this.z*=t):(this.x=0,this.y=0,this.z=0),this},divide:function(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z||1,this},negate:function(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this},distance:function(t){var i=t.x-this.x,e=t.y-this.y,n=t.z-this.z||0;return Math.sqrt(i*i+e*e+n*n)},distanceSq:function(t){var i=t.x-this.x,e=t.y-this.y,n=t.z-this.z||0;return i*i+e*e+n*n},length:function(){var t=this.x,i=this.y,e=this.z;return Math.sqrt(t*t+i*i+e*e)},lengthSq:function(){var t=this.x,i=this.y,e=this.z;return t*t+i*i+e*e},normalize:function(){var t=this.x,i=this.y,e=this.z,n=t*t+i*i+e*e;return 0<n&&(n=1/Math.sqrt(n),this.x=t*n,this.y=i*n,this.z=e*n),this},dot:function(t){return this.x*t.x+this.y*t.y+this.z*t.z},cross:function(t){var i=this.x,e=this.y,n=this.z,s=t.x,r=t.y,o=t.z;return this.x=e*o-n*r,this.y=n*s-i*o,this.z=i*r-e*s,this},lerp:function(t,i){void 0===i&&(i=0);var e=this.x,n=this.y,s=this.z;return this.x=e+i*(t.x-e),this.y=n+i*(t.y-n),this.z=s+i*(t.z-s),this},applyMatrix3:function(t){var i=this.x,e=this.y,n=this.z,s=t.val;return this.x=s[0]*i+s[3]*e+s[6]*n,this.y=s[1]*i+s[4]*e+s[7]*n,this.z=s[2]*i+s[5]*e+s[8]*n,this},applyMatrix4:function(t){var i=this.x,e=this.y,n=this.z,s=t.val,r=1/(s[3]*i+s[7]*e+s[11]*n+s[15]);return this.x=(s[0]*i+s[4]*e+s[8]*n+s[12])*r,this.y=(s[1]*i+s[5]*e+s[9]*n+s[13])*r,this.z=(s[2]*i+s[6]*e+s[10]*n+s[14])*r,this},transformMat3:function(t){var i=this.x,e=this.y,n=this.z,s=t.val;return this.x=i*s[0]+e*s[3]+n*s[6],this.y=i*s[1]+e*s[4]+n*s[7],this.z=i*s[2]+e*s[5]+n*s[8],this},transformMat4:function(t){var i=this.x,e=this.y,n=this.z,s=t.val;return this.x=s[0]*i+s[4]*e+s[8]*n+s[12],this.y=s[1]*i+s[5]*e+s[9]*n+s[13],this.z=s[2]*i+s[6]*e+s[10]*n+s[14],this},transformCoordinates:function(t){var i=this.x,e=this.y,n=this.z,s=t.val,r=i*s[0]+e*s[4]+n*s[8]+s[12],o=i*s[1]+e*s[5]+n*s[9]+s[13],h=i*s[2]+e*s[6]+n*s[10]+s[14],a=i*s[3]+e*s[7]+n*s[11]+s[15];return this.x=r/a,this.y=o/a,this.z=h/a,this},transformQuat:function(t){var i=this.x,e=this.y,n=this.z,s=t.x,r=t.y,o=t.z,h=t.w,a=h*i+r*n-o*e,u=h*e+o*i-s*n,c=h*n+s*e-r*i,f=-s*i-r*e-o*n;return this.x=a*h+f*-s+u*-o-c*-r,this.y=u*h+f*-r+c*-s-a*-o,this.z=c*h+f*-o+a*-r-u*-s,this},project:function(t){var i=this.x,e=this.y,n=this.z,s=t.val,r=s[0],o=s[1],h=s[2],a=s[3],u=s[4],c=s[5],f=s[6],l=s[7],p=s[8],d=s[9],y=s[10],x=s[11],v=s[12],m=s[13],g=s[14],M=1/(i*a+e*l+n*x+s[15]);return this.x=(i*r+e*u+n*p+v)*M,this.y=(i*o+e*c+n*d+m)*M,this.z=(i*h+e*f+n*y+g)*M,this},projectViewMatrix:function(t,i){return this.applyMatrix4(t).applyMatrix4(i)},unprojectViewMatrix:function(t,i){return this.applyMatrix4(t).applyMatrix4(i)},unproject:function(t,i){var e=t.x,n=t.y,s=t.z,r=t.w,o=this.x-e,h=r-this.y-1-n,a=this.z;return this.x=2*o/s-1,this.y=2*h/r-1,this.z=2*a-1,this.project(i)},reset:function(){return this.x=0,this.y=0,this.z=0,this}});n.ZERO=new n,n.RIGHT=new n(1,0,0),n.LEFT=new n(-1,0,0),n.UP=new n(0,-1,0),n.DOWN=new n(0,1,0),n.FORWARD=new n(0,0,1),n.BACK=new n(0,0,-1),n.ONE=new n(1,1,1),t.exports=n},function(t,i){t.exports=function(t,i,e){return Math.max(i,Math.min(e,t))}},function(t,i,e){var n=e(0),s=e(13),r=new n({initialize:function(t,i){void 0===t&&(t=0),void 0===i&&(i=t),this.type=s.POINT,this.x=t,this.y=i},setTo:function(t,i){return void 0===t&&(t=0),void 0===i&&(i=t),this.x=t,this.y=i,this}});t.exports=r},function(t,i,e){var n=e(0),s=e(3),r=new n({initialize:function(t){this.val=new Float32Array(16),t?this.copy(t):this.identity()},clone:function(){return new r(this)},set:function(t){return this.copy(t)},setValues:function(t,i,e,n,s,r,o,h,a,u,c,f,l,p,d,y){var x=this.val;return x[0]=t,x[1]=i,x[2]=e,x[3]=n,x[4]=s,x[5]=r,x[6]=o,x[7]=h,x[8]=a,x[9]=u,x[10]=c,x[11]=f,x[12]=l,x[13]=p,x[14]=d,x[15]=y,this},copy:function(t){var i=t.val;return this.setValues(i[0],i[1],i[2],i[3],i[4],i[5],i[6],i[7],i[8],i[9],i[10],i[11],i[12],i[13],i[14],i[15])},fromArray:function(t){return this.setValues(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15])},zero:function(){return this.setValues(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)},transform:function(t,i,e){var n=a.fromQuat(e).val,s=i.x,r=i.y,o=i.z;return this.setValues(n[0]*s,n[1]*s,n[2]*s,0,n[4]*r,n[5]*r,n[6]*r,0,n[8]*o,n[9]*o,n[10]*o,0,t.x,t.y,t.z,1)},xyz:function(t,i,e){this.identity();var n=this.val;return n[12]=t,n[13]=i,n[14]=e,this},scaling:function(t,i,e){this.zero();var n=this.val;return n[0]=t,n[5]=i,n[10]=e,n[15]=1,this},identity:function(){return this.setValues(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)},transpose:function(){var t=this.val,i=t[1],e=t[2],n=t[3],s=t[6],r=t[7],o=t[11];return t[1]=t[4],t[2]=t[8],t[3]=t[12],t[4]=i,t[6]=t[9],t[7]=t[13],t[8]=e,t[9]=s,t[11]=t[14],t[12]=n,t[13]=r,t[14]=o,this},getInverse:function(t){return this.copy(t),this.invert()},invert:function(){var t=this.val,i=t[0],e=t[1],n=t[2],s=t[3],r=t[4],o=t[5],h=t[6],a=t[7],u=t[8],c=t[9],f=t[10],l=t[11],p=t[12],d=t[13],y=t[14],x=t[15],v=i*o-e*r,m=i*h-n*r,g=i*a-s*r,M=e*h-n*o,w=e*a-s*o,_=n*a-s*h,A=u*d-c*p,O=u*y-f*p,z=u*x-l*p,E=c*y-f*d,T=c*x-l*d,b=f*x-l*y,P=v*b-m*T+g*E+M*z-w*O+_*A;return P?(P=1/P,this.setValues((o*b-h*T+a*E)*P,(n*T-e*b-s*E)*P,(d*_-y*w+x*M)*P,(f*w-c*_-l*M)*P,(h*z-r*b-a*O)*P,(i*b-n*z+s*O)*P,(y*g-p*_-x*m)*P,(u*_-f*g+l*m)*P,(r*T-o*z+a*A)*P,(e*z-i*T-s*A)*P,(p*w-d*g+x*v)*P,(c*g-u*w-l*v)*P,(o*O-r*E-h*A)*P,(i*E-e*O+n*A)*P,(d*m-p*M-y*v)*P,(u*M-c*m+f*v)*P)):this},adjoint:function(){var t=this.val,i=t[0],e=t[1],n=t[2],s=t[3],r=t[4],o=t[5],h=t[6],a=t[7],u=t[8],c=t[9],f=t[10],l=t[11],p=t[12],d=t[13],y=t[14],x=t[15];return this.setValues(o*(f*x-l*y)-c*(h*x-a*y)+d*(h*l-a*f),-(e*(f*x-l*y)-c*(n*x-s*y)+d*(n*l-s*f)),e*(h*x-a*y)-o*(n*x-s*y)+d*(n*a-s*h),-(e*(h*l-a*f)-o*(n*l-s*f)+c*(n*a-s*h)),-(r*(f*x-l*y)-u*(h*x-a*y)+p*(h*l-a*f)),i*(f*x-l*y)-u*(n*x-s*y)+p*(n*l-s*f),-(i*(h*x-a*y)-r*(n*x-s*y)+p*(n*a-s*h)),i*(h*l-a*f)-r*(n*l-s*f)+u*(n*a-s*h),r*(c*x-l*d)-u*(o*x-a*d)+p*(o*l-a*c),-(i*(c*x-l*d)-u*(e*x-s*d)+p*(e*l-s*c)),i*(o*x-a*d)-r*(e*x-s*d)+p*(e*a-s*o),-(i*(o*l-a*c)-r*(e*l-s*c)+u*(e*a-s*o)),-(r*(c*y-f*d)-u*(o*y-h*d)+p*(o*f-h*c)),i*(c*y-f*d)-u*(e*y-n*d)+p*(e*f-n*c),-(i*(o*y-h*d)-r*(e*y-n*d)+p*(e*h-n*o)),i*(o*f-h*c)-r*(e*f-n*c)+u*(e*h-n*o))},determinant:function(){var t=this.val,i=t[0],e=t[1],n=t[2],s=t[3],r=t[4],o=t[5],h=t[6],a=t[7],u=t[8],c=t[9],f=t[10],l=t[11],p=t[12],d=t[13],y=t[14],x=t[15];return(i*o-e*r)*(f*x-l*y)-(i*h-n*r)*(c*x-l*d)+(i*a-s*r)*(c*y-f*d)+(e*h-n*o)*(u*x-l*p)-(e*a-s*o)*(u*y-f*p)+(n*a-s*h)*(u*d-c*p)},multiply:function(t){var i=this.val,e=i[0],n=i[1],s=i[2],r=i[3],o=i[4],h=i[5],a=i[6],u=i[7],c=i[8],f=i[9],l=i[10],p=i[11],d=i[12],y=i[13],x=i[14],v=i[15],m=t.val,g=m[0],M=m[1],w=m[2],_=m[3];return i[0]=g*e+M*o+w*c+_*d,i[1]=g*n+M*h+w*f+_*y,i[2]=g*s+M*a+w*l+_*x,i[3]=g*r+M*u+w*p+_*v,g=m[4],M=m[5],w=m[6],_=m[7],i[4]=g*e+M*o+w*c+_*d,i[5]=g*n+M*h+w*f+_*y,i[6]=g*s+M*a+w*l+_*x,i[7]=g*r+M*u+w*p+_*v,g=m[8],M=m[9],w=m[10],_=m[11],i[8]=g*e+M*o+w*c+_*d,i[9]=g*n+M*h+w*f+_*y,i[10]=g*s+M*a+w*l+_*x,i[11]=g*r+M*u+w*p+_*v,g=m[12],M=m[13],w=m[14],_=m[15],i[12]=g*e+M*o+w*c+_*d,i[13]=g*n+M*h+w*f+_*y,i[14]=g*s+M*a+w*l+_*x,i[15]=g*r+M*u+w*p+_*v,this},multiplyLocal:function(t){var i=this.val,e=t.val;return this.setValues(i[0]*e[0]+i[1]*e[4]+i[2]*e[8]+i[3]*e[12],i[0]*e[1]+i[1]*e[5]+i[2]*e[9]+i[3]*e[13],i[0]*e[2]+i[1]*e[6]+i[2]*e[10]+i[3]*e[14],i[0]*e[3]+i[1]*e[7]+i[2]*e[11]+i[3]*e[15],i[4]*e[0]+i[5]*e[4]+i[6]*e[8]+i[7]*e[12],i[4]*e[1]+i[5]*e[5]+i[6]*e[9]+i[7]*e[13],i[4]*e[2]+i[5]*e[6]+i[6]*e[10]+i[7]*e[14],i[4]*e[3]+i[5]*e[7]+i[6]*e[11]+i[7]*e[15],i[8]*e[0]+i[9]*e[4]+i[10]*e[8]+i[11]*e[12],i[8]*e[1]+i[9]*e[5]+i[10]*e[9]+i[11]*e[13],i[8]*e[2]+i[9]*e[6]+i[10]*e[10]+i[11]*e[14],i[8]*e[3]+i[9]*e[7]+i[10]*e[11]+i[11]*e[15],i[12]*e[0]+i[13]*e[4]+i[14]*e[8]+i[15]*e[12],i[12]*e[1]+i[13]*e[5]+i[14]*e[9]+i[15]*e[13],i[12]*e[2]+i[13]*e[6]+i[14]*e[10]+i[15]*e[14],i[12]*e[3]+i[13]*e[7]+i[14]*e[11]+i[15]*e[15])},premultiply:function(t){return this.multiplyMatrices(t,this)},multiplyMatrices:function(t,i){var e=t.val,n=i.val,s=e[0],r=e[4],o=e[8],h=e[12],a=e[1],u=e[5],c=e[9],f=e[13],l=e[2],p=e[6],d=e[10],y=e[14],x=e[3],v=e[7],m=e[11],g=e[15],M=n[0],w=n[4],_=n[8],A=n[12],O=n[1],z=n[5],E=n[9],T=n[13],b=n[2],P=n[6],F=n[10],R=n[14],S=n[3],C=n[7],I=n[11],D=n[15];return this.setValues(s*M+r*O+o*b+h*S,a*M+u*O+c*b+f*S,l*M+p*O+d*b+y*S,x*M+v*O+m*b+g*S,s*w+r*z+o*P+h*C,a*w+u*z+c*P+f*C,l*w+p*z+d*P+y*C,x*w+v*z+m*P+g*C,s*_+r*E+o*F+h*I,a*_+u*E+c*F+f*I,l*_+p*E+d*F+y*I,x*_+v*E+m*F+g*I,s*A+r*T+o*R+h*D,a*A+u*T+c*R+f*D,l*A+p*T+d*R+y*D,x*A+v*T+m*R+g*D)},translate:function(t){return this.translateXYZ(t.x,t.y,t.z)},translateXYZ:function(t,i,e){var n=this.val;return n[12]=n[0]*t+n[4]*i+n[8]*e+n[12],n[13]=n[1]*t+n[5]*i+n[9]*e+n[13],n[14]=n[2]*t+n[6]*i+n[10]*e+n[14],n[15]=n[3]*t+n[7]*i+n[11]*e+n[15],this},scale:function(t){return this.scaleXYZ(t.x,t.y,t.z)},scaleXYZ:function(t,i,e){var n=this.val;return n[0]=n[0]*t,n[1]=n[1]*t,n[2]=n[2]*t,n[3]=n[3]*t,n[4]=n[4]*i,n[5]=n[5]*i,n[6]=n[6]*i,n[7]=n[7]*i,n[8]=n[8]*e,n[9]=n[9]*e,n[10]=n[10]*e,n[11]=n[11]*e,this},makeRotationAxis:function(t,i){var e=Math.cos(i),n=Math.sin(i),s=1-e,r=t.x,o=t.y,h=t.z,a=s*r,u=s*o;return this.setValues(a*r+e,a*o-n*h,a*h+n*o,0,a*o+n*h,u*o+e,u*h-n*r,0,a*h-n*o,u*h+n*r,s*h*h+e,0,0,0,0,1)},rotate:function(t,i){var e=this.val,n=i.x,s=i.y,r=i.z,o=Math.sqrt(n*n+s*s+r*r);if(Math.abs(o)<1e-6)return this;n*=o=1/o,s*=o,r*=o;var h=Math.sin(t),a=Math.cos(t),u=1-a,c=e[0],f=e[1],l=e[2],p=e[3],d=e[4],y=e[5],x=e[6],v=e[7],m=e[8],g=e[9],M=e[10],w=e[11],_=e[12],A=e[13],O=e[14],z=e[15],E=n*n*u+a,T=s*n*u+r*h,b=r*n*u-s*h,P=n*s*u-r*h,F=s*s*u+a,R=r*s*u+n*h,S=n*r*u+s*h,C=s*r*u-n*h,I=r*r*u+a;return this.setValues(c*E+d*T+m*b,f*E+y*T+g*b,l*E+x*T+M*b,p*E+v*T+w*b,c*P+d*F+m*R,f*P+y*F+g*R,l*P+x*F+M*R,p*P+v*F+w*R,c*S+d*C+m*I,f*S+y*C+g*I,l*S+x*C+M*I,p*S+v*C+w*I,_,A,O,z)},rotateX:function(t){var i=this.val,e=Math.sin(t),n=Math.cos(t),s=i[4],r=i[5],o=i[6],h=i[7],a=i[8],u=i[9],c=i[10],f=i[11];return i[4]=s*n+a*e,i[5]=r*n+u*e,i[6]=o*n+c*e,i[7]=h*n+f*e,i[8]=a*n-s*e,i[9]=u*n-r*e,i[10]=c*n-o*e,i[11]=f*n-h*e,this},rotateY:function(t){var i=this.val,e=Math.sin(t),n=Math.cos(t),s=i[0],r=i[1],o=i[2],h=i[3],a=i[8],u=i[9],c=i[10],f=i[11];return i[0]=s*n-a*e,i[1]=r*n-u*e,i[2]=o*n-c*e,i[3]=h*n-f*e,i[8]=s*e+a*n,i[9]=r*e+u*n,i[10]=o*e+c*n,i[11]=h*e+f*n,this},rotateZ:function(t){var i=this.val,e=Math.sin(t),n=Math.cos(t),s=i[0],r=i[1],o=i[2],h=i[3],a=i[4],u=i[5],c=i[6],f=i[7];return i[0]=s*n+a*e,i[1]=r*n+u*e,i[2]=o*n+c*e,i[3]=h*n+f*e,i[4]=a*n-s*e,i[5]=u*n-r*e,i[6]=c*n-o*e,i[7]=f*n-h*e,this},fromRotationTranslation:function(t,i){var e=t.x,n=t.y,s=t.z,r=t.w,o=e+e,h=n+n,a=s+s,u=e*o,c=e*h,f=e*a,l=n*h,p=n*a,d=s*a,y=r*o,x=r*h,v=r*a;return this.setValues(1-(l+d),c+v,f-x,0,c-v,1-(u+d),p+y,0,f+x,p-y,1-(u+l),0,i.x,i.y,i.z,1)},fromQuat:function(t){var i=t.x,e=t.y,n=t.z,s=t.w,r=i+i,o=e+e,h=n+n,a=i*r,u=i*o,c=i*h,f=e*o,l=e*h,p=n*h,d=s*r,y=s*o,x=s*h;return this.setValues(1-(f+p),u+x,c-y,0,u-x,1-(a+p),l+d,0,c+y,l-d,1-(a+f),0,0,0,0,1)},frustum:function(t,i,e,n,s,r){var o=1/(i-t),h=1/(n-e),a=1/(s-r);return this.setValues(2*s*o,0,0,0,0,2*s*h,0,0,(i+t)*o,(n+e)*h,(r+s)*a,-1,0,0,r*s*2*a,0)},perspective:function(t,i,e,n){var s=1/Math.tan(t/2),r=1/(e-n);return this.setValues(s/i,0,0,0,0,s,0,0,0,0,(n+e)*r,-1,0,0,2*n*e*r,0)},perspectiveLH:function(t,i,e,n){return this.setValues(2*e/t,0,0,0,0,2*e/i,0,0,0,0,-n/(e-n),1,0,0,e*n/(e-n),0)},ortho:function(t,i,e,n,s,r){var o=0===(o=t-i)?o:1/o,h=0===(h=e-n)?h:1/h,a=0===(a=s-r)?a:1/a;return this.setValues(-2*o,0,0,0,0,-2*h,0,0,0,0,2*a,0,(t+i)*o,(n+e)*h,(r+s)*a,1)},lookAtRH:function(t,i,e){var n=this.val;return c.subVectors(t,i),0===c.getLengthSquared()&&(c.z=1),c.normalize(),o.crossVectors(e,c),0===o.getLengthSquared()&&(1===Math.abs(e.z)?c.x+=1e-4:c.z+=1e-4,c.normalize(),o.crossVectors(e,c)),o.normalize(),h.crossVectors(c,o),n[0]=o.x,n[1]=o.y,n[2]=o.z,n[4]=h.x,n[5]=h.y,n[6]=h.z,n[8]=c.x,n[9]=c.y,n[10]=c.z,this},lookAt:function(t,i,e){var n=t.x,s=t.y,r=t.z,o=e.x,h=e.y,a=e.z,u=i.x,c=i.y,f=i.z;if(Math.abs(n-u)<1e-6&&Math.abs(s-c)<1e-6&&Math.abs(r-f)<1e-6)return this.identity();var l=n-u,p=s-c,d=r-f,y=1/Math.sqrt(l*l+p*p+d*d),x=h*(d*=y)-a*(p*=y),v=a*(l*=y)-o*d,m=o*p-h*l;(y=Math.sqrt(x*x+v*v+m*m))?(x*=y=1/y,v*=y,m*=y):m=v=x=0;var g=p*m-d*v,M=d*x-l*m,w=l*v-p*x;return(y=Math.sqrt(g*g+M*M+w*w))?(g*=y=1/y,M*=y,w*=y):w=M=g=0,this.setValues(x,g,l,0,v,M,p,0,m,w,d,0,-(x*n+v*s+m*r),-(g*n+M*s+w*r),-(l*n+p*s+d*r),1)},yawPitchRoll:function(t,i,e){this.zero(),a.zero(),u.zero();var n=this.val,s=a.val,r=u.val,o=Math.sin(e),h=Math.cos(e);return n[10]=1,n[15]=1,n[0]=h,n[1]=o,n[4]=-o,n[5]=h,o=Math.sin(i),h=Math.cos(i),s[0]=1,s[15]=1,s[5]=h,s[10]=h,s[9]=-o,s[6]=o,o=Math.sin(t),h=Math.cos(t),r[5]=1,r[15]=1,r[0]=h,r[2]=-o,r[8]=o,r[10]=h,this.multiplyLocal(a),this.multiplyLocal(u),this},setWorldMatrix:function(t,i,e,n,s){return this.yawPitchRoll(t.y,t.x,t.z),a.scaling(e.x,e.y,e.z),u.xyz(i.x,i.y,i.z),this.multiplyLocal(a),this.multiplyLocal(u),n&&this.multiplyLocal(n),s&&this.multiplyLocal(s),this},multiplyToMat4:function(t,i){var e=this.val,n=t.val,s=e[0],r=e[1],o=e[2],h=e[3],a=e[4],u=e[5],c=e[6],f=e[7],l=e[8],p=e[9],d=e[10],y=e[11],x=e[12],v=e[13],m=e[14],g=e[15],M=n[0],w=n[1],_=n[2],A=n[3],O=n[4],z=n[5],E=n[6],T=n[7],b=n[8],P=n[9],F=n[10],R=n[11],S=n[12],C=n[13],I=n[14],D=n[15];return i.setValues(M*s+w*a+_*l+A*x,w*r+w*u+_*p+A*v,_*o+w*c+_*d+A*m,A*h+w*f+_*y+A*g,O*s+z*a+E*l+T*x,O*r+z*u+E*p+T*v,O*o+z*c+E*d+T*m,O*h+z*f+E*y+T*g,b*s+P*a+F*l+R*x,b*r+P*u+F*p+R*v,b*o+P*c+F*d+R*m,b*h+P*f+F*y+R*g,S*s+C*a+I*l+D*x,S*r+C*u+I*p+D*v,S*o+C*c+I*d+D*m,S*h+C*f+I*y+D*g)},fromRotationXYTranslation:function(t,i,e){var n=i.x,s=i.y,r=i.z,o=Math.sin(t.x),h=Math.cos(t.x),a=Math.sin(t.y),u=Math.cos(t.y),c=n,f=s,l=r,p=-o,d=0-p*a,y=0-h*a,x=p*u,v=h*u;return e||(c=u*n+a*r,f=d*n+h*s+x*r,l=y*n+o*s+v*r),this.setValues(u,d,y,0,0,h,o,0,a,x,v,0,c,f,l,1)},getMaxScaleOnAxis:function(){var t=this.val,i=t[0]*t[0]+t[1]*t[1]+t[2]*t[2],e=t[4]*t[4]+t[5]*t[5]+t[6]*t[6],n=t[8]*t[8]+t[9]*t[9]+t[10]*t[10];return Math.sqrt(Math.max(i,e,n))}}),a=new r,u=new r,o=new s,h=new s,c=new s;t.exports=r},function(t,i){t.exports=function(){}},function(t,i,e){var s=e(44),r=e(12);t.exports=function(t,i,e){var n=r(t,i,null);if(null===n)return e;if(Array.isArray(n))return s.RND.pick(n);if("object"==typeof n){if(n.hasOwnProperty("randInt"))return s.RND.integerInRange(n.randInt[0],n.randInt[1]);if(n.hasOwnProperty("randFloat"))return s.RND.realInRange(n.randFloat[0],n.randFloat[1])}else if("function"==typeof n)return n(i);return n}},function(t,i){t.exports=function(t,i){return Math.random()*(i-t)+t}},function(t,i){t.exports=function(t,i,e){var n=e-i;return i+((t-i)%n+n)%n}},function(t,i,e){var n=new(e(0))({initialize:function(t,i,e,n){this.x=0,this.y=0,this.z=0,this.w=0,"object"==typeof t?(this.x=t.x||0,this.y=t.y||0,this.z=t.z||0,this.w=t.w||0):(this.x=t||0,this.y=i||0,this.z=e||0,this.w=n||0)},clone:function(){return new n(this.x,this.y,this.z,this.w)},copy:function(t){return this.x=t.x,this.y=t.y,this.z=t.z||0,this.w=t.w||0,this},equals:function(t){return this.x===t.x&&this.y===t.y&&this.z===t.z&&this.w===t.w},set:function(t,i,e,n){return"object"==typeof t?(this.x=t.x||0,this.y=t.y||0,this.z=t.z||0,this.w=t.w||0):(this.x=t||0,this.y=i||0,this.z=e||0,this.w=n||0),this},add:function(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z||0,this.w+=t.w||0,this},subtract:function(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z||0,this.w-=t.w||0,this},scale:function(t){return this.x*=t,this.y*=t,this.z*=t,this.w*=t,this},length:function(){var t=this.x,i=this.y,e=this.z,n=this.w;return Math.sqrt(t*t+i*i+e*e+n*n)},lengthSq:function(){var t=this.x,i=this.y,e=this.z,n=this.w;return t*t+i*i+e*e+n*n},normalize:function(){var t=this.x,i=this.y,e=this.z,n=this.w,s=t*t+i*i+e*e+n*n;return 0<s&&(s=1/Math.sqrt(s),this.x=t*s,this.y=i*s,this.z=e*s,this.w=n*s),this},dot:function(t){return this.x*t.x+this.y*t.y+this.z*t.z+this.w*t.w},lerp:function(t,i){void 0===i&&(i=0);var e=this.x,n=this.y,s=this.z,r=this.w;return this.x=e+i*(t.x-e),this.y=n+i*(t.y-n),this.z=s+i*(t.z-s),this.w=r+i*(t.w-r),this},multiply:function(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z||1,this.w*=t.w||1,this},divide:function(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z||1,this.w/=t.w||1,this},distance:function(t){var i=t.x-this.x,e=t.y-this.y,n=t.z-this.z||0,s=t.w-this.w||0;return Math.sqrt(i*i+e*e+n*n+s*s)},distanceSq:function(t){var i=t.x-this.x,e=t.y-this.y,n=t.z-this.z||0,s=t.w-this.w||0;return i*i+e*e+n*n+s*s},negate:function(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this.w=-this.w,this},transformMat4:function(t){var i=this.x,e=this.y,n=this.z,s=this.w,r=t.val;return this.x=r[0]*i+r[4]*e+r[8]*n+r[12]*s,this.y=r[1]*i+r[5]*e+r[9]*n+r[13]*s,this.z=r[2]*i+r[6]*e+r[10]*n+r[14]*s,this.w=r[3]*i+r[7]*e+r[11]*n+r[15]*s,this},transformQuat:function(t){var i=this.x,e=this.y,n=this.z,s=t.x,r=t.y,o=t.z,h=t.w,a=h*i+r*n-o*e,u=h*e+o*i-s*n,c=h*n+s*e-r*i,f=-s*i-r*e-o*n;return this.x=a*h+f*-s+u*-o-c*-r,this.y=u*h+f*-r+c*-s-a*-o,this.z=c*h+f*-o+a*-r-u*-s,this},reset:function(){return this.x=0,this.y=0,this.z=0,this.w=0,this}});n.prototype.sub=n.prototype.subtract,n.prototype.mul=n.prototype.multiply,n.prototype.div=n.prototype.divide,n.prototype.dist=n.prototype.distance,n.prototype.distSq=n.prototype.distanceSq,n.prototype.len=n.prototype.length,n.prototype.lenSq=n.prototype.lengthSq,t.exports=n},function(t,i){t.exports=function(t,i,e){if(t&&"number"!=typeof t){if(t.hasOwnProperty(i))return t[i];if(-1===i.indexOf("."))return e;for(var n=i.split("."),s=t,r=e,o=0;o<n.length;o++){if(!s.hasOwnProperty(n[o])){r=e;break}r=s[n[o]],s=s[n[o]]}return r}return e}},function(t,i){t.exports={CIRCLE:0,ELLIPSE:1,LINE:2,POINT:3,POLYGON:4,RECTANGLE:5,TRIANGLE:6}},function(t,i){t.exports={SKIP_CHECK:-1,NORMAL:0,ADD:1,MULTIPLY:2,SCREEN:3,OVERLAY:4,DARKEN:5,LIGHTEN:6,COLOR_DODGE:7,COLOR_BURN:8,HARD_LIGHT:9,SOFT_LIGHT:10,DIFFERENCE:11,EXCLUSION:12,HUE:13,SATURATION:14,COLOR:15,LUMINOSITY:16,ERASE:17,SOURCE_IN:18,SOURCE_OUT:19,SOURCE_ATOP:20,DESTINATION_OVER:21,DESTINATION_IN:22,DESTINATION_OUT:23,DESTINATION_ATOP:24,LIGHTER:25,COPY:26,XOR:27}},function(t,i){t.exports=function(t){return 0<=(t%=2*Math.PI)?t:t+2*Math.PI}},function(t,i,e){var n=e(10);t.exports=function(t){return n(t,-Math.PI,Math.PI)}},function(t,i,e){var n=e(10);t.exports=function(t){return n(t,-180,180)}},function(t,i){t.exports=function(t,i,e){return void 0===e&&(e=1e-4),Math.abs(t-i)<e}},function(t,i,e){var n=e(20);t.exports=function(t,i){return n(t)/n(i)/n(t-i)}},function(t,i){t.exports=function(t){if(0===t)return 1;for(var i=t;--t;)i*=t;return i}},function(t,i){t.exports=function(t,i,e,n,s){var r=.5*(n-i),o=.5*(s-e),h=t*t;return(2*e-2*n+r+o)*(t*h)+(-3*e+3*n-2*r-o)*h+r*t+e}},function(t,i){t.exports=function(t,i,e){return(i-t)*e+t}},function(t,i){t.exports=function(t,i,e){return t<=i?0:e<=t?1:(t=(t-i)/(e-i))*t*(3-2*t)}},function(t,i){t.exports=function(t,i,e){return(t=Math.max(0,Math.min(1,(t-i)/(e-i))))*t*t*(t*(6*t-15)+10)}},function(t,i,e){var n=e(2);t.exports=function(t){return t*n.DEG_TO_RAD}},function(t,i){t.exports=function(t,i){void 0===i&&(i=1);var e=2*Math.random()*Math.PI,n=2*Math.random()-1,s=Math.sqrt(1-n*n)*i;return t.x=Math.cos(e)*s,t.y=Math.sin(e)*s,t.z=n*i,t}},function(t,i){t.exports=function(t,i){return void 0===i&&(i=1),t.x=(2*Math.random()-1)*i,t.y=(2*Math.random()-1)*i,t.z=(2*Math.random()-1)*i,t.w=(2*Math.random()-1)*i,t}},function(t,i){t.exports=function(t,i,e,n){var s=Math.cos(n),r=Math.sin(n),o=t.x-i,h=t.y-e;return t.x=o*s-h*r+i,t.y=o*r+h*s+e,t}},function(t,i,e){var y=e(1);t.exports=function(t,i,e,n,s,r,o,h){void 0===h&&(h=new y);var a=Math.sin(s),u=Math.cos(s),c=u*r,f=a*r,l=-a*o,p=u*o,d=1/(c*p+l*-f);return h.x=p*d*t+-l*d*i+(n*l-e*p)*d,h.y=c*d*i+-f*d*t+(-n*c+e*f)*d,h}},function(t,i,e){var n=new(e(0))({initialize:function(t){this.val=new Float32Array(9),t?this.copy(t):this.identity()},clone:function(){return new n(this)},set:function(t){return this.copy(t)},copy:function(t){var i=this.val,e=t.val;return i[0]=e[0],i[1]=e[1],i[2]=e[2],i[3]=e[3],i[4]=e[4],i[5]=e[5],i[6]=e[6],i[7]=e[7],i[8]=e[8],this},fromMat4:function(t){var i=t.val,e=this.val;return e[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[4],e[4]=i[5],e[5]=i[6],e[6]=i[8],e[7]=i[9],e[8]=i[10],this},fromArray:function(t){var i=this.val;return i[0]=t[0],i[1]=t[1],i[2]=t[2],i[3]=t[3],i[4]=t[4],i[5]=t[5],i[6]=t[6],i[7]=t[7],i[8]=t[8],this},identity:function(){var t=this.val;return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,this},transpose:function(){var t=this.val,i=t[1],e=t[2],n=t[5];return t[1]=t[3],t[2]=t[6],t[3]=i,t[5]=t[7],t[6]=e,t[7]=n,this},invert:function(){var t=this.val,i=t[0],e=t[1],n=t[2],s=t[3],r=t[4],o=t[5],h=t[6],a=t[7],u=t[8],c=u*r-o*a,f=-u*s+o*h,l=a*s-r*h,p=i*c+e*f+n*l;return p?(p=1/p,t[0]=c*p,t[1]=(-u*e+n*a)*p,t[2]=(o*e-n*r)*p,t[3]=f*p,t[4]=(u*i-n*h)*p,t[5]=(-o*i+n*s)*p,t[6]=l*p,t[7]=(-a*i+e*h)*p,t[8]=(r*i-e*s)*p,this):null},adjoint:function(){var t=this.val,i=t[0],e=t[1],n=t[2],s=t[3],r=t[4],o=t[5],h=t[6],a=t[7],u=t[8];return t[0]=r*u-o*a,t[1]=n*a-e*u,t[2]=e*o-n*r,t[3]=o*h-s*u,t[4]=i*u-n*h,t[5]=n*s-i*o,t[6]=s*a-r*h,t[7]=e*h-i*a,t[8]=i*r-e*s,this},determinant:function(){var t=this.val,i=t[0],e=t[1],n=t[2],s=t[3],r=t[4],o=t[5],h=t[6],a=t[7],u=t[8];return i*(u*r-o*a)+e*(-u*s+o*h)+n*(a*s-r*h)},multiply:function(t){var i=this.val,e=i[0],n=i[1],s=i[2],r=i[3],o=i[4],h=i[5],a=i[6],u=i[7],c=i[8],f=t.val,l=f[0],p=f[1],d=f[2],y=f[3],x=f[4],v=f[5],m=f[6],g=f[7],M=f[8];return i[0]=l*e+p*r+d*a,i[1]=l*n+p*o+d*u,i[2]=l*s+p*h+d*c,i[3]=y*e+x*r+v*a,i[4]=y*n+x*o+v*u,i[5]=y*s+x*h+v*c,i[6]=m*e+g*r+M*a,i[7]=m*n+g*o+M*u,i[8]=m*s+g*h+M*c,this},translate:function(t){var i=this.val,e=t.x,n=t.y;return i[6]=e*i[0]+n*i[3]+i[6],i[7]=e*i[1]+n*i[4]+i[7],i[8]=e*i[2]+n*i[5]+i[8],this},rotate:function(t){var i=this.val,e=i[0],n=i[1],s=i[2],r=i[3],o=i[4],h=i[5],a=Math.sin(t),u=Math.cos(t);return i[0]=u*e+a*r,i[1]=u*n+a*o,i[2]=u*s+a*h,i[3]=u*r-a*e,i[4]=u*o-a*n,i[5]=u*h-a*s,this},scale:function(t){var i=this.val,e=t.x,n=t.y;return i[0]=e*i[0],i[1]=e*i[1],i[2]=e*i[2],i[3]=n*i[3],i[4]=n*i[4],i[5]=n*i[5],this},fromQuat:function(t){var i=t.x,e=t.y,n=t.z,s=t.w,r=i+i,o=e+e,h=n+n,a=i*r,u=i*o,c=i*h,f=e*o,l=e*h,p=n*h,d=s*r,y=s*o,x=s*h,v=this.val;return v[0]=1-(f+p),v[3]=u+x,v[6]=c-y,v[1]=u-x,v[4]=1-(a+p),v[7]=l+d,v[2]=c+y,v[5]=l-d,v[8]=1-(a+f),this},normalFromMat4:function(t){var i=t.val,e=this.val,n=i[0],s=i[1],r=i[2],o=i[3],h=i[4],a=i[5],u=i[6],c=i[7],f=i[8],l=i[9],p=i[10],d=i[11],y=i[12],x=i[13],v=i[14],m=i[15],g=n*a-s*h,M=n*u-r*h,w=n*c-o*h,_=s*u-r*a,A=s*c-o*a,O=r*c-o*u,z=f*x-l*y,E=f*v-p*y,T=f*m-d*y,b=l*v-p*x,P=l*m-d*x,F=p*m-d*v,R=g*F-M*P+w*b+_*T-A*E+O*z;return R?(R=1/R,e[0]=(a*F-u*P+c*b)*R,e[1]=(u*T-h*F-c*E)*R,e[2]=(h*P-a*T+c*z)*R,e[3]=(r*P-s*F-o*b)*R,e[4]=(n*F-r*T+o*E)*R,e[5]=(s*T-n*P-o*z)*R,e[6]=(x*O-v*A+m*_)*R,e[7]=(v*w-y*O-m*M)*R,e[8]=(y*A-x*w+m*g)*R,this):null}});t.exports=n},function(t,i,e){var n=e(0),s=e(30),r=e(7),o=e(3),h=new Int8Array([1,2,0]),a=new Float32Array([0,0,0]),u=new o(1,0,0),c=new o(0,1,0),f=new o,l=new s,p=new n({initialize:function(t,i,e,n){this.onChangeCallback=r,this.set(t,i,e,n)},x:{get:function(){return this._x},set:function(t){this._x=t,this.onChangeCallback(this)}},y:{get:function(){return this._y},set:function(t){this._y=t,this.onChangeCallback(this)}},z:{get:function(){return this._z},set:function(t){this._z=t,this.onChangeCallback(this)}},w:{get:function(){return this._w},set:function(t){this._w=t,this.onChangeCallback(this)}},copy:function(t){return this.set(t)},set:function(t,i,e,n,s){return void 0===s&&(s=!0),"object"==typeof t?(this._x=t.x||0,this._y=t.y||0,this._z=t.z||0,this._w=t.w||0):(this._x=t||0,this._y=i||0,this._z=e||0,this._w=n||0),s&&this.onChangeCallback(this),this},add:function(t){return this._x+=t.x,this._y+=t.y,this._z+=t.z,this._w+=t.w,this.onChangeCallback(this),this},subtract:function(t){return this._x-=t.x,this._y-=t.y,this._z-=t.z,this._w-=t.w,this.onChangeCallback(this),this},scale:function(t){return this._x*=t,this._y*=t,this._z*=t,this._w*=t,this.onChangeCallback(this),this},length:function(){var t=this.x,i=this.y,e=this.z,n=this.w;return Math.sqrt(t*t+i*i+e*e+n*n)},lengthSq:function(){var t=this.x,i=this.y,e=this.z,n=this.w;return t*t+i*i+e*e+n*n},normalize:function(){var t=this.x,i=this.y,e=this.z,n=this.w,s=t*t+i*i+e*e+n*n;return 0<s&&(s=1/Math.sqrt(s),this._x=t*s,this._y=i*s,this._z=e*s,this._w=n*s),this.onChangeCallback(this),this},dot:function(t){return this.x*t.x+this.y*t.y+this.z*t.z+this.w*t.w},lerp:function(t,i){void 0===i&&(i=0);var e=this.x,n=this.y,s=this.z,r=this.w;return this.set(e+i*(t.x-e),n+i*(t.y-n),s+i*(t.z-s),r+i*(t.w-r))},rotationTo:function(t,i){var e=t.x*i.x+t.y*i.y+t.z*i.z;return e<-.999999?(f.copy(u).cross(t).length()<1e-6&&f.copy(c).cross(t),f.normalize(),this.setAxisAngle(f,Math.PI)):.999999<e?this.set(0,0,0,1):(f.copy(t).cross(i),this._x=f.x,this._y=f.y,this._z=f.z,this._w=1+e,this.normalize())},setAxes:function(t,i,e){var n=l.val;return n[0]=i.x,n[3]=i.y,n[6]=i.z,n[1]=e.x,n[4]=e.y,n[7]=e.z,n[2]=-t.x,n[5]=-t.y,n[8]=-t.z,this.fromMat3(l).normalize()},identity:function(){return this.set(0,0,0,1)},setAxisAngle:function(t,i){i*=.5;var e=Math.sin(i);return this.set(e*t.x,e*t.y,e*t.z,Math.cos(i))},multiply:function(t){var i=this.x,e=this.y,n=this.z,s=this.w,r=t.x,o=t.y,h=t.z,a=t.w;return this.set(i*a+s*r+e*h-n*o,e*a+s*o+n*r-i*h,n*a+s*h+i*o-e*r,s*a-i*r-e*o-n*h)},slerp:function(t,i){var e=this.x,n=this.y,s=this.z,r=this.w,o=t.x,h=t.y,a=t.z,u=t.w,c=e*o+n*h+s*a+r*u;c<0&&(c=-c,o=-o,h=-h,a=-a,u=-u);var f,l,p=1-i,d=i;return 1e-6<1-c&&(f=Math.acos(c),l=Math.sin(f),p=Math.sin((1-i)*f)/l,d=Math.sin(i*f)/l),this.set(p*e+d*o,p*n+d*h,p*s+d*a,p*r+d*u)},invert:function(){var t=this.x,i=this.y,e=this.z,n=this.w,s=t*t+i*i+e*e+n*n,r=s?1/s:0;return this.set(-t*r,-i*r,-e*r,n*r)},conjugate:function(){return this._x=-this.x,this._y=-this.y,this._z=-this.z,this.onChangeCallback(this),this},rotateX:function(t){t*=.5;var i=this.x,e=this.y,n=this.z,s=this.w,r=Math.sin(t),o=Math.cos(t);return this.set(i*o+s*r,e*o+n*r,n*o-e*r,s*o-i*r)},rotateY:function(t){t*=.5;var i=this.x,e=this.y,n=this.z,s=this.w,r=Math.sin(t),o=Math.cos(t);return this.set(i*o-n*r,e*o+s*r,n*o+i*r,s*o-e*r)},rotateZ:function(t){t*=.5;var i=this.x,e=this.y,n=this.z,s=this.w,r=Math.sin(t),o=Math.cos(t);return this.set(i*o+e*r,e*o-i*r,n*o+s*r,s*o-n*r)},calculateW:function(){var t=this.x,i=this.y,e=this.z;return this.w=-Math.sqrt(1-t*t-i*i-e*e),this},setFromEuler:function(t,i){var e=t.x/2,n=t.y/2,s=t.z/2,r=Math.cos(e),o=Math.cos(n),h=Math.cos(s),a=Math.sin(e),u=Math.sin(n),c=Math.sin(s);switch(t.order){case"XYZ":this.set(a*o*h+r*u*c,r*u*h-a*o*c,r*o*c+a*u*h,r*o*h-a*u*c,i);break;case"YXZ":this.set(a*o*h+r*u*c,r*u*h-a*o*c,r*o*c-a*u*h,r*o*h+a*u*c,i);break;case"ZXY":this.set(a*o*h-r*u*c,r*u*h+a*o*c,r*o*c+a*u*h,r*o*h-a*u*c,i);break;case"ZYX":this.set(a*o*h-r*u*c,r*u*h+a*o*c,r*o*c-a*u*h,r*o*h+a*u*c,i);break;case"YZX":this.set(a*o*h+r*u*c,r*u*h+a*o*c,r*o*c-a*u*h,r*o*h-a*u*c,i);break;case"XZY":this.set(a*o*h-r*u*c,r*u*h-a*o*c,r*o*c+a*u*h,r*o*h+a*u*c,i)}return this},setFromRotationMatrix:function(t){var i,e=t.val,n=e[0],s=e[4],r=e[8],o=e[1],h=e[5],a=e[9],u=e[2],c=e[6],f=e[10],l=n+h+f;return 0<l?(i=.5/Math.sqrt(l+1),this.set((c-a)*i,(r-u)*i,(o-s)*i,.25/i)):h<n&&f<n?(i=2*Math.sqrt(1+n-h-f),this.set(.25*i,(s+o)/i,(r+u)/i,(c-a)/i)):f<h?(i=2*Math.sqrt(1+h-n-f),this.set((s+o)/i,.25*i,(a+c)/i,(r-u)/i)):(i=2*Math.sqrt(1+f-n-h),this.set((r+u)/i,(a+c)/i,.25*i,(o-s)/i)),this},fromMat3:function(t){var i,e,n,s,r=t.val,o=r[0]+r[4]+r[8];return 0<o?(s=Math.sqrt(o+1),this.w=.5*s,s=.5/s,this._x=(r[7]-r[5])*s,this._y=(r[2]-r[6])*s,this._z=(r[3]-r[1])*s):(i=0,r[4]>r[0]&&(i=1),r[8]>r[3*i+i]&&(i=2),e=h[i],n=h[e],s=Math.sqrt(r[3*i+i]-r[3*e+e]-r[3*n+n]+1),a[i]=.5*s,s=.5/s,a[e]=(r[3*e+i]+r[3*i+e])*s,a[n]=(r[3*n+i]+r[3*i+n])*s,this._x=a[0],this._y=a[1],this._z=a[2],this._w=(r[3*n+e]-r[3*e+n])*s),this.onChangeCallback(this),this}});t.exports=p},function(t,i,e){var n=e(3),s=e(6),r=e(31),o=new s,h=new r,a=new n;t.exports=function(t,i,e){return h.setAxisAngle(i,e),o.fromRotationTranslation(h,a.set(0,0,0)),t.transformMat4(o)}},function(t,i,e){var n=e(0),s=e(6),r=e(26),o=e(27),h=e(32),a=e(158),u=e(34),c=e(1),f=e(3),l=e(11),p=new f,d=new l,y=new f,x=new f,v=new s,m=new n({initialize:function(t){this.scene=t,this.displayList=t.sys.displayList,this.updateList=t.sys.updateList,this.name="",this.direction=new f(0,0,-1),this.up=new f(0,1,0),this.position=new f,this.pixelScale=128,this.projection=new s,this.view=new s,this.combined=new s,this.invProjectionView=new s,this.near=1,this.far=100,this.ray={origin:new f,direction:new f},this.viewportWidth=0,this.viewportHeight=0,this.billboardMatrixDirty=!0,this.children=new a},setPosition:function(t,i,e){return this.position.set(t,i,e),this.update()},setScene:function(t){return this.scene=t,this},setPixelScale:function(t){return this.pixelScale=t,this.update()},add:function(t){return this.children.set(t),this.displayList.add(t.gameObject),this.updateList.add(t.gameObject),this.updateChildren(),t},remove:function(t){return this.displayList.remove(t.gameObject),this.updateList.remove(t.gameObject),this.children.delete(t),this},clear:function(){for(var t=this.getChildren(),i=0;i<t.length;i++)this.remove(t[i]);return this},getChildren:function(){return this.children.entries},create:function(t,i,e,n,s,r){void 0===r&&(r=!0);var o=new u(this.scene,t,i,e,n,s);return this.displayList.add(o.gameObject),this.updateList.add(o.gameObject),o.visible=r,this.children.set(o),this.updateChildren(),o},createMultiple:function(t,i,e,n){void 0===n&&(n=!0);for(var s=[],r=0;r<t;r++){var o=new u(this.scene,0,0,0,i,e);this.displayList.add(o.gameObject),this.updateList.add(o.gameObject),o.visible=n,this.children.set(o),s.push(o)}return s},createRect:function(t,i,e,n){"number"==typeof t&&(t={x:t,y:t,z:t}),"number"==typeof i&&(i={x:i,y:i,z:i});for(var s=t.x*t.y*t.z,r=this.createMultiple(s,e,n),o=0,h=.5-t.z/2;h<t.z/2;h++)for(var a=.5-t.y/2;a<t.y/2;a++)for(var u=.5-t.x/2;u<t.x/2;u++){var c=u*i.x,f=a*i.y,l=h*i.z;r[o].position.set(c,f,l),o++}return this.update(),r},randomSphere:function(t,i){void 0===i&&(i=this.getChildren());for(var e=0;e<i.length;e++)r(i[e].position,t);return this.update()},randomCube:function(t,i){void 0===i&&(i=this.getChildren());for(var e=0;e<i.length;e++)o(i[e].position,t);return this.update()},translateChildren:function(t,i){void 0===i&&(i=this.getChildren());for(var e=0;e<i.length;e++)i[e].position.add(t);return this.update()},transformChildren:function(t,i){void 0===i&&(i=this.getChildren());for(var e=0;e<i.length;e++)i[e].position.transformMat4(t);return this.update()},setViewport:function(t,i){return this.viewportWidth=t,this.viewportHeight=i,this.update()},translate:function(t,i,e){return"object"==typeof t?(this.position.x+=t.x||0,this.position.y+=t.y||0,this.position.z+=t.z||0):(this.position.x+=t||0,this.position.y+=i||0,this.position.z+=e||0),this.update()},lookAt:function(t,i,e){var n=this.direction,s=this.up;return"object"==typeof t?n.copy(t):n.set(t,i,e),n.subtract(this.position).normalize(),p.copy(n).cross(s).normalize(),s.copy(p).cross(n).normalize(),this.update()},rotate:function(t,i){return h(this.direction,i,t),h(this.up,i,t),this.update()},rotateAround:function(t,i,e){return p.copy(t).subtract(this.position),this.translate(p),this.rotate(i,e),this.translate(p.negate()),this.update()},project:function(t,i){void 0===i&&(i=new l);var e=this.viewportWidth,n=this.viewportHeight,s=m.NEAR_RANGE,r=m.FAR_RANGE;return d.set(t.x,t.y,t.z,1),d.transformMat4(this.combined),0===d.w&&(d.w=1),d.x=d.x/d.w,d.y=d.y/d.w,d.z=d.z/d.w,i.x=e/2*d.x+(0+e/2),i.y=n/2*d.y+(0+n/2),i.z=(r-s)/2*d.z+(r+s)/2,0!==i.w&&!i.w||(i.w=1/d.w),i},unproject:function(t,i){void 0===i&&(i=new f);var e=d.set(0,0,this.viewportWidth,this.viewportHeight);return i.copy(t).unproject(e,this.invProjectionView)},getPickRay:function(t,i){var e=this.ray.origin.set(t,i,0),n=this.ray.direction.set(t,i,1),s=d.set(0,0,this.viewportWidth,this.viewportHeight),r=this.invProjectionView;return e.unproject(s,r),n.unproject(s,r),n.subtract(e).normalize(),this.ray},updateChildren:function(){for(var t=this.children.entries,i=0;i<t.length;i++)t[i].project(this);return this},update:function(){return this.updateChildren()},updateBillboardMatrix:function(){var t=y.set(this.direction).negate(),i=x.set(this.up).cross(t).normalize(),e=p.set(t).cross(i).normalize(),n=v.val;n[0]=i.x,n[1]=i.y,n[2]=i.z,n[3]=0,n[4]=e.x,n[5]=e.y,n[6]=e.z,n[7]=0,n[8]=t.x,n[9]=t.y,n[10]=t.z,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,this.billboardMatrixDirty=!1},getPointSize:function(t,i,e){void 0===e&&(e=new c),this.billboardMatrixDirty&&this.updateBillboardMatrix();var n=p,s=i.x/this.pixelScale/2,r=i.y/this.pixelScale/2;n.set(-s,-r,0).transformMat4(v).add(t),this.project(n,n);var o=n.x,h=n.y;n.set(s,r,0).transformMat4(v).add(t),this.project(n,n);var a=n.x-o,u=n.y-h;return e.set(a,u)},destroy:function(){this.children.clear(),this.scene=void 0,this.children=void 0},setX:function(t){return this.position.x=t,this.update()},setY:function(t){return this.position.y=t,this.update()},setZ:function(t){return this.position.z=t,this.update()},x:{get:function(){return this.position.x},set:function(t){this.position.x=t,this.update()}},y:{get:function(){return this.position.y},set:function(t){this.position.y=t,this.update()}},z:{get:function(){return this.position.z},set:function(t){this.position.z=t,this.update()}}});m.FAR_RANGE=1,m.NEAR_RANGE=0,t.exports=m},function(t,i,e){var n=e(0),o=e(35),h=e(180),a=e(1),u=e(11),s=new n({Extends:o,initialize:function(t,i,e,n,s,r){o.call(this,t,"Sprite3D"),this.gameObject=new h(t,0,0,s,r),this.position=new u(i,e,n),this.size=new a(this.gameObject.width,this.gameObject.height),this.scale=new a(1,1),this.adjustScaleX=!0,this.adjustScaleY=!0,this._visible=!0},project:function(t){var i=this.position,e=this.gameObject;t.project(i,e),t.getPointSize(i,this.size,this.scale),this.scale.x<=0||this.scale.y<=0?e.setVisible(!1):(e.visible||e.setVisible(!0),this.adjustScaleX&&(e.scaleX=this.scale.x),this.adjustScaleY&&(e.scaleY=this.scale.y),e.setDepth(-1*e.z))},setVisible:function(t){return this.visible=t,this},visible:{get:function(){return this._visible},set:function(t){this._visible=t,this.gameObject.visible=t}},x:{get:function(){return this.position.x},set:function(t){this.position.x=t}},y:{get:function(){return this.position.y},set:function(t){this.position.y=t}},z:{get:function(){return this.position.z},set:function(t){this.position.z=t}}});t.exports=s},function(t,i,e){var n=e(0),s=e(36),r=e(159),o=e(166),h=e(37),a=new n({Extends:o,initialize:function(t,i){o.call(this),this.scene=t,this.type=i,this.state=0,this.parentContainer=null,this.name="",this.active=!0,this.tabIndex=-1,this.data=null,this.renderFlags=15,this.cameraFilter=0,this.input=null,this.body=null,this.ignoreDestroy=!1,t.sys.queueDepthSort()},setActive:function(t){return this.active=t,this},setName:function(t){return this.name=t,this},setState:function(t){return this.state=t,this},setDataEnabled:function(){return this.data||(this.data=new r(this)),this},setData:function(t,i){return this.data||(this.data=new r(this)),this.data.set(t,i),this},incData:function(t,i){return this.data||(this.data=new r(this)),this.data.inc(t,i),this},toggleData:function(t){return this.data||(this.data=new r(this)),this.data.toggle(t),this},getData:function(t){return this.data||(this.data=new r(this)),this.data.get(t)},setInteractive:function(t,i,e){return this.scene.sys.input.enable(this,t,i,e),this},disableInteractive:function(){return this.input&&(this.input.enabled=!1),this},removeInteractive:function(){return this.scene.sys.input.clear(this),this.input=void 0,this},addedToScene:function(){},removedFromScene:function(){},update:function(){},toJSON:function(){return s(this)},willRender:function(t){return!(a.RENDER_MASK!==this.renderFlags||0!==this.cameraFilter&&this.cameraFilter&t.id)},getIndexList:function(){for(var t=this,i=this.parentContainer,e=[];i&&(e.unshift(i.getIndex(t)),(t=i).parentContainer);)i=i.parentContainer;return e.unshift(this.scene.sys.displayList.getIndex(t)),e},destroy:function(t){var i;void 0===t&&(t=!1),this.scene&&!this.ignoreDestroy&&(this.preDestroy&&this.preDestroy.call(this),this.emit(h.DESTROY,this),i=this.scene.sys,t||i.displayList.remove(this),this.input&&(i.input.clear(this),this.input=void 0),this.data&&(this.data.destroy(),this.data=void 0),this.body&&(this.body.destroy(),this.body=void 0),t||i.queueDepthSort(),this.active=!1,this.visible=!1,this.scene=void 0,this.parentContainer=void 0,this.removeAllListeners())}});a.RENDER_MASK=15,t.exports=a},function(t,i){t.exports=function(t){var i={name:t.name,type:t.type,x:t.x,y:t.y,depth:t.depth,scale:{x:t.scaleX,y:t.scaleY},origin:{x:t.originX,y:t.originY},flipX:t.flipX,flipY:t.flipY,rotation:t.rotation,alpha:t.alpha,visible:t.visible,blendMode:t.blendMode,textureKey:"",frameKey:"",data:{}};return t.texture&&(i.textureKey=t.texture.key,i.frameKey=t.frame.name),i}},function(t,i,e){t.exports={ADDED_TO_SCENE:e(167),DESTROY:e(168),REMOVED_FROM_SCENE:e(169),VIDEO_COMPLETE:e(170),VIDEO_CREATED:e(171),VIDEO_ERROR:e(172),VIDEO_LOOP:e(173),VIDEO_PLAY:e(174),VIDEO_SEEKED:e(175),VIDEO_SEEKING:e(176),VIDEO_STOP:e(177),VIDEO_TIMEOUT:e(178),VIDEO_UNLOCKED:e(179)}},function(t,i,e){t.exports={ADD_ANIMATION:e(184),ANIMATION_COMPLETE:e(185),ANIMATION_COMPLETE_KEY:e(186),ANIMATION_REPEAT:e(187),ANIMATION_RESTART:e(188),ANIMATION_START:e(189),ANIMATION_STOP:e(190),ANIMATION_UPDATE:e(191),PAUSE_ALL:e(192),REMOVE_ANIMATION:e(193),RESUME_ALL:e(194)}},function(t,i,e){var s=e(40),r=e(5);t.exports=function(t,i,e){if(void 0===e&&(e=new r),i<=0||1<=i)return e.x=t.x,e.y=t.y,e;var n=s(t)*i;return.5<i?(n-=t.width+t.height)<=t.width?(e.x=t.right-n,e.y=t.bottom):(e.x=t.x,e.y=t.bottom-(n-t.width)):n<=t.width?(e.x=t.x+n,e.y=t.y):(e.x=t.right,e.y=t.y+(n-t.width)),e}},function(t,i){t.exports=function(t){return 2*(t.width+t.height)}},function(t,i,e){var n=e(0),r=e(2),f=e(1),s=new n({initialize:function(t,i,e,n,s,r){void 0===t&&(t=1),void 0===i&&(i=0),void 0===e&&(e=0),void 0===n&&(n=1),void 0===s&&(s=0),void 0===r&&(r=0),this.matrix=new Float32Array([t,i,e,n,s,r,0,0,1]),this.decomposedMatrix={translateX:0,translateY:0,scaleX:1,scaleY:1,rotation:0}},a:{get:function(){return this.matrix[0]},set:function(t){this.matrix[0]=t}},b:{get:function(){return this.matrix[1]},set:function(t){this.matrix[1]=t}},c:{get:function(){return this.matrix[2]},set:function(t){this.matrix[2]=t}},d:{get:function(){return this.matrix[3]},set:function(t){this.matrix[3]=t}},e:{get:function(){return this.matrix[4]},set:function(t){this.matrix[4]=t}},f:{get:function(){return this.matrix[5]},set:function(t){this.matrix[5]=t}},tx:{get:function(){return this.matrix[4]},set:function(t){this.matrix[4]=t}},ty:{get:function(){return this.matrix[5]},set:function(t){this.matrix[5]=t}},rotation:{get:function(){return Math.acos(this.a/this.scaleX)*(Math.atan(-this.c/this.a)<0?-1:1)}},rotationNormalized:{get:function(){var t=this.matrix,i=t[0],e=t[1],n=t[2],s=t[3];return i||e?0<e?Math.acos(i/this.scaleX):-Math.acos(i/this.scaleX):n||s?r.TAU-(0<s?Math.acos(-n/this.scaleY):-Math.acos(n/this.scaleY)):0}},scaleX:{get:function(){return Math.sqrt(this.a*this.a+this.b*this.b)}},scaleY:{get:function(){return Math.sqrt(this.c*this.c+this.d*this.d)}},loadIdentity:function(){var t=this.matrix;return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,this},translate:function(t,i){var e=this.matrix;return e[4]=e[0]*t+e[2]*i+e[4],e[5]=e[1]*t+e[3]*i+e[5],this},scale:function(t,i){var e=this.matrix;return e[0]*=t,e[1]*=t,e[2]*=i,e[3]*=i,this},rotate:function(t){var i=Math.sin(t),e=Math.cos(t),n=this.matrix,s=n[0],r=n[1],o=n[2],h=n[3];return n[0]=s*e+o*i,n[1]=r*e+h*i,n[2]=s*-i+o*e,n[3]=r*-i+h*e,this},multiply:function(t,i){var e=this.matrix,n=t.matrix,s=e[0],r=e[1],o=e[2],h=e[3],a=e[4],u=e[5],c=n[0],f=n[1],l=n[2],p=n[3],d=n[4],y=n[5],x=void 0===i?this:i;return x.a=c*s+f*o,x.b=c*r+f*h,x.c=l*s+p*o,x.d=l*r+p*h,x.e=d*s+y*o+a,x.f=d*r+y*h+u,x},multiplyWithOffset:function(t,i,e){var n=this.matrix,s=t.matrix,r=n[0],o=n[1],h=n[2],a=n[3],u=i*r+e*h+n[4],c=i*o+e*a+n[5],f=s[0],l=s[1],p=s[2],d=s[3],y=s[4],x=s[5];return n[0]=f*r+l*h,n[1]=f*o+l*a,n[2]=p*r+d*h,n[3]=p*o+d*a,n[4]=y*r+x*h+u,n[5]=y*o+x*a+c,this},transform:function(t,i,e,n,s,r){var o=this.matrix,h=o[0],a=o[1],u=o[2],c=o[3],f=o[4],l=o[5];return o[0]=t*h+i*u,o[1]=t*a+i*c,o[2]=e*h+n*u,o[3]=e*a+n*c,o[4]=s*h+r*u+f,o[5]=s*a+r*c+l,this},transformPoint:function(t,i,e){void 0===e&&(e={x:0,y:0});var n=this.matrix,s=n[0],r=n[1],o=n[2],h=n[3],a=n[4],u=n[5];return e.x=t*s+i*o+a,e.y=t*r+i*h+u,e},invert:function(){var t=this.matrix,i=t[0],e=t[1],n=t[2],s=t[3],r=t[4],o=t[5],h=i*s-e*n;return t[0]=s/h,t[1]=-e/h,t[2]=-n/h,t[3]=i/h,t[4]=(n*o-s*r)/h,t[5]=-(i*o-e*r)/h,this},copyFrom:function(t){var i=this.matrix;return i[0]=t.a,i[1]=t.b,i[2]=t.c,i[3]=t.d,i[4]=t.e,i[5]=t.f,this},copyFromArray:function(t){var i=this.matrix;return i[0]=t[0],i[1]=t[1],i[2]=t[2],i[3]=t[3],i[4]=t[4],i[5]=t[5],this},copyToContext:function(t){var i=this.matrix;return t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),t},setToContext:function(t){var i=this.matrix;return t.setTransform(i[0],i[1],i[2],i[3],i[4],i[5]),t},copyToArray:function(t){var i=this.matrix;return void 0===t?t=[i[0],i[1],i[2],i[3],i[4],i[5]]:(t[0]=i[0],t[1]=i[1],t[2]=i[2],t[3]=i[3],t[4]=i[4],t[5]=i[5]),t},setTransform:function(t,i,e,n,s,r){var o=this.matrix;return o[0]=t,o[1]=i,o[2]=e,o[3]=n,o[4]=s,o[5]=r,this},decomposeMatrix:function(){var t,i,e=this.decomposedMatrix,n=this.matrix,s=n[0],r=n[1],o=n[2],h=n[3],a=s*h-r*o;return e.translateX=n[4],e.translateY=n[5],s||r?(t=Math.sqrt(s*s+r*r),e.rotation=0<r?Math.acos(s/t):-Math.acos(s/t),e.scaleX=t,e.scaleY=a/t):o||h?(i=Math.sqrt(o*o+h*h),e.rotation=.5*Math.PI-(0<h?Math.acos(-o/i):-Math.acos(o/i)),e.scaleX=a/i,e.scaleY=i):(e.rotation=0,e.scaleX=0,e.scaleY=0),e},applyITRS:function(t,i,e,n,s){var r=this.matrix,o=Math.sin(e),h=Math.cos(e);return r[4]=t,r[5]=i,r[0]=h*n,r[1]=o*n,r[2]=-o*s,r[3]=h*s,this},applyInverse:function(t,i,e){void 0===e&&(e=new f);var n=this.matrix,s=n[0],r=n[1],o=n[2],h=n[3],a=n[4],u=n[5],c=1/(s*h+o*-r);return e.x=h*c*t+-o*c*i+(u*o-a*h)*c,e.y=s*c*i+-r*c*t+(-u*s+a*r)*c,e},getX:function(t,i){return t*this.a+i*this.c+this.e},getY:function(t,i){return t*this.b+i*this.d+this.f},getXRound:function(t,i,e){var n=this.getX(t,i);return e&&(n=Math.round(n)),n},getYRound:function(t,i,e){var n=this.getY(t,i);return e&&(n=Math.round(n)),n},getCSSMatrix:function(){var t=this.matrix;return"matrix("+t[0]+","+t[1]+","+t[2]+","+t[3]+","+t[4]+","+t[5]+")"},destroy:function(){this.matrix=null,this.decomposedMatrix=null}});t.exports=s},function(t,i,e){var r=e(43),o=e(156),n=e(0),h=e(8),s=e(157),a=e(253),u=e(254),c=e(34),f=new n({Extends:u,initialize:function(t,i){u.call(this,t,i),this.cameras=[],i.registerGameObject("sprite3D",this.sprite3DFactory,this.sprite3DCreator)},sprite3DFactory:function(t,i,e,n,s){var r=new c(this.scene,t,i,e,n,s);return this.displayList.add(r.gameObject),this.updateList.add(r.gameObject),r},sprite3DCreator:function(t,i){void 0===t&&(t={});var e=h(t,"key",null),n=h(t,"frame",null),s=new c(this.scene,0,0,e,n);return void 0!==i&&(t.add=i),r(this.scene,s,t),o(s,t),s},boot:function(){this.systems.events.once("destroy",this.destroy,this)},start:function(){var t=this.systems.events;t.on("update",this.update,this),t.once("shutdown",this.shutdown,this)},add:function(t,i,e){return this.addPerspectiveCamera(t,i,e)},addOrthographicCamera:function(t,i){var e=this.scene.sys.game.config;void 0===t&&(t=e.width),void 0===i&&(i=e.height);var n=new s(this.scene,t,i);return this.cameras.push(n),n},addPerspectiveCamera:function(t,i,e){var n=this.scene.sys.game.config;void 0===t&&(t=80),void 0===i&&(i=n.width),void 0===e&&(e=n.height);var s=new a(this.scene,t,i,e);return this.cameras.push(s),s},getCamera:function(t){for(var i=0;i<this.cameras.length;i++)if(this.cameras[i].name===t)return this.cameras[i];return null},removeCamera:function(t){var i=this.cameras.indexOf(t);-1!==i&&this.cameras.splice(i,1)},removeAll:function(){for(;0<this.cameras.length;){this.cameras.pop().destroy()}return this.main},update:function(t,i){for(var e=0,n=this.cameras.length;e<n;++e)this.cameras[e].update(t,i)},shutdown:function(){var t=this.systems.events;t.off("update",this.update,this),t.off("shutdown",this.shutdown,this),this.removeAll()},destroy:function(){this.shutdown(),this.pluginManager=null,this.game=null,this.scene=null,this.systems=null}});t.exports=f},function(t,i,e){var u=e(14),c=e(8);t.exports=function(t,i,e){i.x=c(e,"x",0),i.y=c(e,"y",0),i.depth=c(e,"depth",0),i.flipX=c(e,"flipX",!1),i.flipY=c(e,"flipY",!1);var n=c(e,"scale",null);"number"==typeof n?i.setScale(n):null!==n&&(i.scaleX=c(n,"x",1),i.scaleY=c(n,"y",1));var s=c(e,"scrollFactor",null);"number"==typeof s?i.setScrollFactor(s):null!==s&&(i.scrollFactorX=c(s,"x",1),i.scrollFactorY=c(s,"y",1)),i.rotation=c(e,"rotation",0);var r=c(e,"angle",null);null!==r&&(i.angle=r),i.alpha=c(e,"alpha",1);var o,h,a=c(e,"origin",null);return"number"==typeof a?i.setOrigin(a):null!==a&&(o=c(a,"x",.5),h=c(a,"y",.5),i.setOrigin(o,h)),i.blendMode=c(e,"blendMode",u.NORMAL),i.visible=c(e,"visible",!0),c(e,"add",!0)&&t.sys.displayList.add(i),i.preUpdate&&t.sys.updateList.add(i),i}},function(t,i,e){var n=e(2),s=e(45)(!1,s={Angle:e(47),Distance:e(58),Easing:e(66),Fuzzy:e(111),Interpolation:e(116),Pow2:e(124),Snap:e(128),RandomDataGenerator:e(132),Average:e(133),Bernstein:e(19),Between:e(134),CatmullRom:e(21),CeilTo:e(135),Clamp:e(4),DegToRad:e(25),Difference:e(136),Euler:e(137),Factorial:e(20),FloatBetween:e(9),FloorTo:e(138),FromPercent:e(139),GetSpeed:e(140),IsEven:e(141),IsEvenStrict:e(142),Linear:e(22),MaxAdd:e(143),MinSub:e(144),Percent:e(145),RadToDeg:e(146),RandomXY:e(147),RandomXYZ:e(26),RandomXYZW:e(27),Rotate:e(148),RotateAround:e(28),RotateAroundDistance:e(149),RotateTo:e(150),RoundAwayFromZero:e(151),RoundTo:e(152),SinCosTableGenerator:e(153),SmootherStep:e(24),SmoothStep:e(23),ToXY:e(154),TransformXY:e(29),Within:e(155),Wrap:e(10),Vector2:e(1),Vector3:e(3),Vector4:e(11),Matrix3:e(30),Matrix4:e(6),Quaternion:e(31),RotateVec3:e(32)},n);t.exports=s},function(t,i,e){var c=e(46),f=function(){var t,i,e,n,s,r,o=arguments[0]||{},h=1,a=arguments.length,u=!1;for("boolean"==typeof o&&(u=o,o=arguments[1]||{},h=2),a===h&&(o=this,--h);h<a;h++)if(null!=(t=arguments[h]))for(i in t)e=o[i],o!==(n=t[i])&&(u&&n&&(c(n)||(s=Array.isArray(n)))?(r=s?(s=!1,e&&Array.isArray(e)?e:[]):e&&c(e)?e:{},o[i]=f(u,r,n)):void 0!==n&&(o[i]=n));return o};t.exports=f},function(t,i){t.exports=function(t){if("object"!=typeof t||t.nodeType||t===t.window)return!1;try{if(t.constructor&&!{}.hasOwnProperty.call(t.constructor.prototype,"isPrototypeOf"))return!1}catch(t){return!1}return!0}},function(t,i,e){t.exports={Between:e(48),BetweenPoints:e(49),BetweenPointsY:e(50),BetweenY:e(51),CounterClockwise:e(52),Normalize:e(15),Random:e(53),RandomDegrees:e(54),Reverse:e(55),RotateTo:e(56),ShortestBetween:e(57),Wrap:e(16),WrapDegrees:e(17)}},function(t,i){t.exports=function(t,i,e,n){return Math.atan2(n-i,e-t)}},function(t,i){t.exports=function(t,i){return Math.atan2(i.y-t.y,i.x-t.x)}},function(t,i){t.exports=function(t,i){return Math.atan2(i.x-t.x,i.y-t.y)}},function(t,i){t.exports=function(t,i,e,n){return Math.atan2(e-t,n-i)}},function(t,i,e){var n=e(2);t.exports=function(t){return t>Math.PI&&(t-=n.PI2),Math.abs(((t+n.TAU)%n.PI2-n.PI2)%n.PI2)}},function(t,i,e){var n=e(9);t.exports=function(){return n(-Math.PI,Math.PI)}},function(t,i,e){var n=e(9);t.exports=function(){return n(-180,180)}},function(t,i,e){var n=e(15);t.exports=function(t){return n(t+Math.PI)}},function(t,i,e){var n=e(2);t.exports=function(t,i,e){return void 0===e&&(e=.05),t===i||(Math.abs(i-t)<=e||Math.abs(i-t)>=n.PI2-e?t=i:(Math.abs(i-t)>Math.PI&&(i<t?i+=n.PI2:i-=n.PI2),t<i?t+=e:i<t&&(t-=e))),t}},function(t,i){t.exports=function(t,i){var e=i-t;return 0==e?0:e-360*Math.floor((e+180)/360)}},function(t,i,e){t.exports={Between:e(59),BetweenPoints:e(60),BetweenPointsSquared:e(61),Chebyshev:e(62),Power:e(63),Snake:e(64),Squared:e(65)}},function(t,i){t.exports=function(t,i,e,n){var s=t-e,r=i-n;return Math.sqrt(s*s+r*r)}},function(t,i){t.exports=function(t,i){var e=t.x-i.x,n=t.y-i.y;return Math.sqrt(e*e+n*n)}},function(t,i){t.exports=function(t,i){var e=t.x-i.x,n=t.y-i.y;return e*e+n*n}},function(t,i){t.exports=function(t,i,e,n){return Math.max(Math.abs(t-e),Math.abs(i-n))}},function(t,i){t.exports=function(t,i,e,n,s){return void 0===s&&(s=2),Math.sqrt(Math.pow(e-t,s)+Math.pow(n-i,s))}},function(t,i){t.exports=function(t,i,e,n){return Math.abs(t-e)+Math.abs(i-n)}},function(t,i){t.exports=function(t,i,e,n){var s=t-e,r=i-n;return s*s+r*r}},function(t,i,e){t.exports={Back:e(67),Bounce:e(71),Circular:e(75),Cubic:e(79),Elastic:e(83),Expo:e(87),Linear:e(91),Quadratic:e(93),Quartic:e(97),Quintic:e(101),Sine:e(105),Stepped:e(109)}},function(t,i,e){t.exports={In:e(68),Out:e(69),InOut:e(70)}},function(t,i){t.exports=function(t,i){return void 0===i&&(i=1.70158),t*t*((i+1)*t-i)}},function(t,i){t.exports=function(t,i){return void 0===i&&(i=1.70158),--t*t*((i+1)*t+i)+1}},function(t,i){t.exports=function(t,i){void 0===i&&(i=1.70158);var e=1.525*i;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)}},function(t,i,e){t.exports={In:e(72),Out:e(73),InOut:e(74)}},function(t,i){t.exports=function(t){return(t=1-t)<1/2.75?1-7.5625*t*t:t<2/2.75?1-(7.5625*(t-=1.5/2.75)*t+.75):t<2.5/2.75?1-(7.5625*(t-=2.25/2.75)*t+.9375):1-(7.5625*(t-=2.625/2.75)*t+.984375)}},function(t,i){t.exports=function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375}},function(t,i){t.exports=function(t){var i=!1;return t<.5?(t=1-2*t,i=!0):t=2*t-1,t<1/2.75?t*=7.5625*t:t=t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375,i?.5*(1-t):.5*t+.5}},function(t,i,e){t.exports={In:e(76),Out:e(77),InOut:e(78)}},function(t,i){t.exports=function(t){return 1-Math.sqrt(1-t*t)}},function(t,i){t.exports=function(t){return Math.sqrt(1- --t*t)}},function(t,i){t.exports=function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)}},function(t,i,e){t.exports={In:e(80),Out:e(81),InOut:e(82)}},function(t,i){t.exports=function(t){return t*t*t}},function(t,i){t.exports=function(t){return--t*t*t+1}},function(t,i){t.exports=function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)}},function(t,i,e){t.exports={In:e(84),Out:e(85),InOut:e(86)}},function(t,i){t.exports=function(t,i,e){if(void 0===i&&(i=.1),void 0===e&&(e=.1),0===t)return 0;if(1===t)return 1;var n=e/4;return i<1?i=1:n=e*Math.asin(1/i)/(2*Math.PI),-(i*Math.pow(2,10*--t)*Math.sin((t-n)*(2*Math.PI)/e))}},function(t,i){t.exports=function(t,i,e){if(void 0===i&&(i=.1),void 0===e&&(e=.1),0===t)return 0;if(1===t)return 1;var n=e/4;return i<1?i=1:n=e*Math.asin(1/i)/(2*Math.PI),i*Math.pow(2,-10*t)*Math.sin((t-n)*(2*Math.PI)/e)+1}},function(t,i){t.exports=function(t,i,e){if(void 0===i&&(i=.1),void 0===e&&(e=.1),0===t)return 0;if(1===t)return 1;var n=e/4;return i<1?i=1:n=e*Math.asin(1/i)/(2*Math.PI),(t*=2)<1?i*Math.pow(2,10*--t)*Math.sin((t-n)*(2*Math.PI)/e)*-.5:i*Math.pow(2,-10*--t)*Math.sin((t-n)*(2*Math.PI)/e)*.5+1}},function(t,i,e){t.exports={In:e(88),Out:e(89),InOut:e(90)}},function(t,i){t.exports=function(t){return Math.pow(2,10*(t-1))-.001}},function(t,i){t.exports=function(t){return 1-Math.pow(2,-10*t)}},function(t,i){t.exports=function(t){return(t*=2)<1?.5*Math.pow(2,10*(t-1)):.5*(2-Math.pow(2,-10*(t-1)))}},function(t,i,e){t.exports=e(92)},function(t,i){t.exports=function(t){return t}},function(t,i,e){t.exports={In:e(94),Out:e(95),InOut:e(96)}},function(t,i){t.exports=function(t){return t*t}},function(t,i){t.exports=function(t){return t*(2-t)}},function(t,i){t.exports=function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)}},function(t,i,e){t.exports={In:e(98),Out:e(99),InOut:e(100)}},function(t,i){t.exports=function(t){return t*t*t*t}},function(t,i){t.exports=function(t){return 1- --t*t*t*t}},function(t,i){t.exports=function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)}},function(t,i,e){t.exports={In:e(102),Out:e(103),InOut:e(104)}},function(t,i){t.exports=function(t){return t*t*t*t*t}},function(t,i){t.exports=function(t){return--t*t*t*t*t+1}},function(t,i){t.exports=function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)}},function(t,i,e){t.exports={In:e(106),Out:e(107),InOut:e(108)}},function(t,i){t.exports=function(t){return 0===t?0:1===t?1:1-Math.cos(t*Math.PI/2)}},function(t,i){t.exports=function(t){return 0===t?0:1===t?1:Math.sin(t*Math.PI/2)}},function(t,i){t.exports=function(t){return 0===t?0:1===t?1:.5*(1-Math.cos(Math.PI*t))}},function(t,i,e){t.exports=e(110)},function(t,i){t.exports=function(t,i){return void 0===i&&(i=1),t<=0?0:1<=t?1:1/i*(1+(i*t|0))}},function(t,i,e){t.exports={Ceil:e(112),Equal:e(18),Floor:e(113),GreaterThan:e(114),LessThan:e(115)}},function(t,i){t.exports=function(t,i){return void 0===i&&(i=1e-4),Math.ceil(t-i)}},function(t,i){t.exports=function(t,i){return void 0===i&&(i=1e-4),Math.floor(t+i)}},function(t,i){t.exports=function(t,i,e){return void 0===e&&(e=1e-4),i-e<t}},function(t,i){t.exports=function(t,i,e){return void 0===e&&(e=1e-4),t<i+e}},function(t,i,e){t.exports={Bezier:e(117),CatmullRom:e(118),CubicBezier:e(119),Linear:e(120),QuadraticBezier:e(121),SmoothStep:e(122),SmootherStep:e(123)}},function(t,i,e){var r=e(19);t.exports=function(t,i){for(var e=0,n=t.length-1,s=0;s<=n;s++)e+=Math.pow(1-i,n-s)*Math.pow(i,s)*t[s]*r(n,s);return e}},function(t,i,e){var r=e(21);t.exports=function(t,i){var e=t.length-1,n=e*i,s=Math.floor(n);return t[0]===t[e]?(i<0&&(s=Math.floor(n=e*(1+i))),r(n-s,t[(s-1+e)%e],t[s],t[(s+1)%e],t[(s+2)%e])):i<0?t[0]-(r(-n,t[0],t[0],t[1],t[1])-t[0]):1<i?t[e]-(r(n-e,t[e],t[e],t[e-1],t[e-1])-t[e]):r(n-s,t[s?s-1:0],t[s],t[e<s+1?e:s+1],t[e<s+2?e:s+2])}},function(t,i){t.exports=function(t,i,e,n,s){return(u=1-t)*u*u*i+3*(a=1-(h=t))*a*h*e+3*(1-(o=t))*o*o*n+(r=t)*r*r*s;var r,o,h,a,u}},function(t,i,e){var r=e(22);t.exports=function(t,i){var e=t.length-1,n=e*i,s=Math.floor(n);return i<0?r(t[0],t[1],n):1<i?r(t[e],t[e-1],e-n):r(t[s],t[e<s+1?e:s+1],n-s)}},function(t,i){t.exports=function(t,i,e,n){return(o=1-t)*o*i+2*(1-(r=t))*r*e+(s=t)*s*n;var s,r,o}},function(t,i,e){var n=e(23);t.exports=function(t,i,e){return i+(e-i)*n(t,0,1)}},function(t,i,e){var n=e(24);t.exports=function(t,i,e){return i+(e-i)*n(t,0,1)}},function(t,i,e){t.exports={GetNext:e(125),IsSize:e(126),IsValue:e(127)}},function(t,i){t.exports=function(t){var i=Math.log(t)/.6931471805599453;return 1<<Math.ceil(i)}},function(t,i){t.exports=function(t,i){return 0<t&&0==(t&t-1)&&0<i&&0==(i&i-1)}},function(t,i){t.exports=function(t){return 0<t&&0==(t&t-1)}},function(t,i,e){t.exports={Ceil:e(129),Floor:e(130),To:e(131)}},function(t,i){t.exports=function(t,i,e,n){return void 0===e&&(e=0),0===i?t:(t-=e,t=i*Math.ceil(t/i),n?(e+t)/i:e+t)}},function(t,i){t.exports=function(t,i,e,n){return void 0===e&&(e=0),0===i?t:(t-=e,t=i*Math.floor(t/i),n?(e+t)/i:e+t)}},function(t,i){t.exports=function(t,i,e,n){return void 0===e&&(e=0),0===i?t:(t-=e,t=i*Math.round(t/i),n?(e+t)/i:e+t)}},function(t,i,e){var n=new(e(0))({initialize:function(t){void 0===t&&(t=[(Date.now()*Math.random()).toString()]),this.c=1,this.s0=0,this.s1=0,this.s2=0,this.n=0,this.signs=[-1,1],t&&this.init(t)},rnd:function(){var t=2091639*this.s0+2.3283064365386963e-10*this.c;return this.c=0|t,this.s0=this.s1,this.s1=this.s2,this.s2=t-this.c,this.s2},hash:function(t){var i,e=this.n;t=t.toString();for(var n=0;n<t.length;n++)i=.02519603282416938*(e+=t.charCodeAt(n)),i-=e=i>>>0,e=(i*=e)>>>0,e+=4294967296*(i-=e);return 2.3283064365386963e-10*((this.n=e)>>>0)},init:function(t){"string"==typeof t?this.state(t):this.sow(t)},sow:function(t){if(this.n=4022871197,this.s0=this.hash(" "),this.s1=this.hash(" "),this.s2=this.hash(" "),this.c=1,t)for(var i=0;i<t.length&&null!=t[i];i++){var e=t[i];this.s0-=this.hash(e),this.s0+=~~(this.s0<0),this.s1-=this.hash(e),this.s1+=~~(this.s1<0),this.s2-=this.hash(e),this.s2+=~~(this.s2<0)}},integer:function(){return 4294967296*this.rnd()},frac:function(){return this.rnd()+11102230246251565e-32*(2097152*this.rnd()|0)},real:function(){return this.integer()+this.frac()},integerInRange:function(t,i){return Math.floor(this.realInRange(0,i-t+1)+t)},between:function(t,i){return Math.floor(this.realInRange(0,i-t+1)+t)},realInRange:function(t,i){return this.frac()*(i-t)+t},normal:function(){return 1-2*this.frac()},uuid:function(){for(var t="",i="",i=t="";t++<36;i+=~t%5|3*t&4?(15^t?8^this.frac()*(20^t?16:4):4).toString(16):"-");return i},pick:function(t){return t[this.integerInRange(0,t.length-1)]},sign:function(){return this.pick(this.signs)},weightedPick:function(t){return t[~~(Math.pow(this.frac(),2)*(t.length-1)+.5)]},timestamp:function(t,i){return this.realInRange(t||9466848e5,i||1577862e6)},angle:function(){return this.integerInRange(-180,180)},rotation:function(){return this.realInRange(-3.1415926,3.1415926)},state:function(t){return"string"==typeof t&&t.match(/^!rnd/)&&(t=t.split(","),this.c=parseFloat(t[1]),this.s0=parseFloat(t[2]),this.s1=parseFloat(t[3]),this.s2=parseFloat(t[4])),["!rnd",this.c,this.s0,this.s1,this.s2].join(",")},shuffle:function(t){for(var i=t.length-1;0<i;i--){var e=Math.floor(this.frac()*(i+1)),n=t[e];t[e]=t[i],t[i]=n}return t}});t.exports=n},function(t,i){t.exports=function(t){for(var i=0,e=0;e<t.length;e++)i+=+t[e];return i/t.length}},function(t,i){t.exports=function(t,i){return Math.floor(Math.random()*(i-t+1)+t)}},function(t,i){t.exports=function(t,i,e){void 0===i&&(i=0),void 0===e&&(e=10);var n=Math.pow(e,-i);return Math.ceil(t*n)/n}},function(t,i){t.exports=function(t,i){return Math.abs(t-i)}},function(t,i,e){var v=e(4),n=e(0),s=e(6),r=e(7),o=new s,h=new n({initialize:function t(i,e,n,s){void 0===i&&(i=0),void 0===e&&(e=0),void 0===n&&(n=0),void 0===s&&(s=t.DefaultOrder),this._x=i,this._y=e,this._z=n,this._order=s,this.onChangeCallback=r},x:{get:function(){return this._x},set:function(t){this._x=t,this.onChangeCallback(this)}},y:{get:function(){return this._y},set:function(t){this._y=t,this.onChangeCallback(this)}},z:{get:function(){return this._z},set:function(t){this._z=t,this.onChangeCallback(this)}},order:{get:function(){return this._order},set:function(t){this._order=t,this.onChangeCallback(this)}},set:function(t,i,e,n){return void 0===n&&(n=this._order),this._x=t,this._y=i,this._z=e,this._order=n,this.onChangeCallback(this),this},copy:function(t){return this.set(t.x,t.y,t.z,t.order)},setFromQuaternion:function(t,i,e){return void 0===i&&(i=this._order),void 0===e&&(e=!1),o.fromQuat(t),this.setFromRotationMatrix(o,i,e)},setFromRotationMatrix:function(t,i,e){void 0===i&&(i=this._order),void 0===e&&(e=!1);var n=t.val,s=n[0],r=n[4],o=n[8],h=n[1],a=n[5],u=n[9],c=n[2],f=n[6],l=n[10],p=0,d=0,y=0,x=.99999;switch(i){case"XYZ":d=Math.asin(v(o,-1,1)),Math.abs(o)<x?(p=Math.atan2(-u,l),y=Math.atan2(-r,s)):p=Math.atan2(f,a);break;case"YXZ":p=Math.asin(-v(u,-1,1)),Math.abs(u)<x?(d=Math.atan2(o,l),y=Math.atan2(h,a)):d=Math.atan2(-c,s);break;case"ZXY":p=Math.asin(v(f,-1,1)),y=Math.abs(f)<x?(d=Math.atan2(-c,l),Math.atan2(-r,a)):Math.atan2(h,s);break;case"ZYX":d=Math.asin(-v(c,-1,1)),y=Math.abs(c)<x?(p=Math.atan2(f,l),Math.atan2(h,s)):Math.atan2(-r,a);break;case"YZX":y=Math.asin(v(h,-1,1)),d=Math.abs(h)<x?(p=Math.atan2(-u,a),Math.atan2(-c,s)):Math.atan2(o,l);break;case"XZY":y=Math.asin(-v(r,-1,1)),Math.abs(r)<x?(p=Math.atan2(f,a),d=Math.atan2(o,s)):p=Math.atan2(-u,l)}return this._x=p,this._y=d,this._z=y,this._order=i,e&&this.onChangeCallback(this),this}});h.RotationOrders=["XYZ","YXZ","ZXY","ZYX","YZX","XZY"],h.DefaultOrder="XYZ",t.exports=h},function(t,i){t.exports=function(t,i,e){void 0===i&&(i=0),void 0===e&&(e=10);var n=Math.pow(e,-i);return Math.floor(t*n)/n}},function(t,i,e){var n=e(4);t.exports=function(t,i,e){return(e-i)*(t=n(t,0,1))}},function(t,i){t.exports=function(t,i){return t/i/1e3}},function(t,i){t.exports=function(t){return t==parseFloat(t)?!(t%2):void 0}},function(t,i){t.exports=function(t){return t===parseFloat(t)?!(t%2):void 0}},function(t,i){t.exports=function(t,i,e){return Math.min(t+i,e)}},function(t,i){t.exports=function(t,i,e){return Math.max(t-i,e)}},function(t,i){t.exports=function(t,i,e,n){void 0===e&&(e=i+1);var s=(t-i)/(e-i);return 1<s?void 0!==n?(s=(n-t)/(n-e))<0&&(s=0):s=1:s<0&&(s=0),s}},function(t,i,e){var n=e(2);t.exports=function(t){return t*n.RAD_TO_DEG}},function(t,i){t.exports=function(t,i){void 0===i&&(i=1);var e=2*Math.random()*Math.PI;return t.x=Math.cos(e)*i,t.y=Math.sin(e)*i,t}},function(t,i){t.exports=function(t,i){var e=t.x,n=t.y;return t.x=e*Math.cos(i)-n*Math.sin(i),t.y=e*Math.sin(i)+n*Math.cos(i),t}},function(t,i){t.exports=function(t,i,e,n,s){var r=n+Math.atan2(t.y-e,t.x-i);return t.x=i+s*Math.cos(r),t.y=e+s*Math.sin(r),t}},function(t,i){t.exports=function(t,i,e,n,s){return t.x=i+s*Math.cos(n),t.y=e+s*Math.sin(n),t}},function(t,i){t.exports=function(t){return 0<t?Math.ceil(t):Math.floor(t)}},function(t,i){t.exports=function(t,i,e){void 0===i&&(i=0),void 0===e&&(e=10);var n=Math.pow(e,-i);return Math.round(t*n)/n}},function(t,i){t.exports=function(t,i,e,n){void 0===i&&(i=1),void 0===e&&(e=1),void 0===n&&(n=1),n*=Math.PI/t;for(var s=[],r=[],o=0;o<t;o++)i+=(e-=i*n)*n,s[o]=e,r[o]=i;return{sin:r,cos:s,length:t}}},function(t,i,e){var o=e(1);t.exports=function(t,i,e,n){void 0===n&&(n=new o);var s=0,r=0;return 0<t&&t<=i*e&&(s=i-1<t?t-(r=Math.floor(t/i))*i:t,n.set(s,r)),n}},function(t,i){t.exports=function(t,i,e){return Math.abs(t-i)<=e}},function(t,i,e){var p=e(8);t.exports=function(t,i){var e,n,s,r,o,h,a,u,c,f,l=p(i,"anims",null);return null===l||("string"==typeof l?t.anims.play(l):"object"==typeof l&&(e=t.anims,(n=p(l,"key",void 0))&&(s=p(l,"startFrame",void 0),r=p(l,"delay",0),o=p(l,"repeat",0),h=p(l,"repeatDelay",0),a=p(l,"yoyo",!1),u=p(l,"play",!1),c=p(l,"delayedPlay",0),f={key:n,delay:r,repeat:o,repeatDelay:h,yoyo:a,startFrame:s},u?e.play(f):0<c?e.playAfterDelay(f,c):e.load(f)))),t}},function(t,i,e){var n=e(33),s=e(0),r=new(e(3)),o=new s({Extends:n,initialize:function(t,i,e){void 0===i&&(i=0),void 0===e&&(e=0),n.call(this,t),this.viewportWidth=i,this.viewportHeight=e,this._zoom=1,this.near=0,this.update()},setToOrtho:function(t,i,e){void 0===i&&(i=this.viewportWidth),void 0===e&&(e=this.viewportHeight);var n=this.zoom;return this.up.set(0,t?-1:1,0),this.direction.set(0,0,t?1:-1),this.position.set(n*i/2,n*e/2,0),this.viewportWidth=i,this.viewportHeight=e,this.update()},update:function(){var t=this.viewportWidth,i=this.viewportHeight,e=Math.abs(this.near),n=Math.abs(this.far),s=this.zoom;return 0===t||0===i||(this.projection.ortho(s*-t/2,s*t/2,s*-i/2,s*i/2,e,n),r.copy(this.position).add(this.direction),this.view.lookAt(this.position,r,this.up),this.combined.copy(this.projection).multiply(this.view),this.invProjectionView.copy(this.combined).invert(),this.billboardMatrixDirty=!0,this.updateChildren()),this},zoom:{get:function(){return this._zoom},set:function(t){this._zoom=t,this.update()}}});t.exports=o},function(t,i,e){var n=new(e(0))({initialize:function(t){if(this.entries=[],Array.isArray(t))for(var i=0;i<t.length;i++)this.set(t[i])},set:function(t){return-1===this.entries.indexOf(t)&&this.entries.push(t),this},get:function(t,i){for(var e=0;e<this.entries.length;e++){var n=this.entries[e];if(n[t]===i)return n}},getArray:function(){return this.entries.slice(0)},delete:function(t){var i=this.entries.indexOf(t);return-1<i&&this.entries.splice(i,1),this},dump:function(){console.group("Set");for(var t=0;t<this.entries.length;t++){var i=this.entries[t];console.log(i)}console.groupEnd()},each:function(t,i){var e,n=this.entries.slice(),s=n.length;if(i)for(e=0;e<s&&!1!==t.call(i,n[e],e);e++);else for(e=0;e<s&&!1!==t(n[e],e);e++);return this},iterate:function(t,i){var e,n=this.entries.length;if(i)for(e=0;e<n&&!1!==t.call(i,this.entries[e],e);e++);else for(e=0;e<n&&!1!==t(this.entries[e],e);e++);return this},iterateLocal:function(t){for(var i=[],e=1;e<arguments.length;e++)i.push(arguments[e]);var n=this.entries.length;for(e=0;e<n;e++){var s=this.entries[e];s[t].apply(s,i)}return this},clear:function(){return this.entries.length=0,this},contains:function(t){return-1<this.entries.indexOf(t)},union:function(t){var i=new n;return t.entries.forEach(function(t){i.set(t)}),this.entries.forEach(function(t){i.set(t)}),i},intersect:function(i){var e=new n;return this.entries.forEach(function(t){i.contains(t)&&e.set(t)}),e},difference:function(i){var e=new n;return this.entries.forEach(function(t){i.contains(t)||e.set(t)}),e},size:{get:function(){return this.entries.length},set:function(t){return t<this.entries.length?this.entries.length=t:this.entries.length}}});t.exports=n},function(t,i,e){var n=e(0),h=e(160),s=new n({initialize:function(t,i){this.parent=t,(this.events=i)||(this.events=t.events?t.events:t),this.list={},this.values={},this._frozen=!1,!t.hasOwnProperty("sys")&&this.events&&this.events.once(h.DESTROY,this.destroy,this)},get:function(t){var i=this.list;if(Array.isArray(t)){for(var e=[],n=0;n<t.length;n++)e.push(i[t[n]]);return e}return i[t]},getAll:function(){var t={};for(var i in this.list)this.list.hasOwnProperty(i)&&(t[i]=this.list[i]);return t},query:function(t){var i={};for(var e in this.list)this.list.hasOwnProperty(e)&&e.match(t)&&(i[e]=this.list[e]);return i},set:function(t,i){if(this._frozen)return this;if("string"==typeof t)return this.setValue(t,i);for(var e in t)this.setValue(e,t[e]);return this},inc:function(t,i){if(this._frozen)return this;void 0===i&&(i=1);var e=this.get(t);return void 0===e&&(e=0),this.set(t,e+i),this},toggle:function(t){return this._frozen||this.set(t,!this.get(t)),this},setValue:function(e,t){return this._frozen||(this.has(e)?this.values[e]=t:(s=(n=this).list,r=this.events,o=this.parent,Object.defineProperty(this.values,e,{enumerable:!0,configurable:!0,get:function(){return s[e]},set:function(t){var i;n._frozen||(i=s[e],s[e]=t,r.emit(h.CHANGE_DATA,o,e,t,i),r.emit(h.CHANGE_DATA_KEY+e,o,t,i))}}),s[e]=t,r.emit(h.SET_DATA,o,e,t))),this;var n,s,r,o},each:function(t,i){for(var e=[this.parent,null,void 0],n=1;n<arguments.length;n++)e.push(arguments[n]);for(var s in this.list)e[1]=s,e[2]=this.list[s],t.apply(i,e);return this},merge:function(t,i){for(var e in void 0===i&&(i=!0),t)t.hasOwnProperty(e)&&(i||!i&&!this.has(e))&&this.setValue(e,t[e]);return this},remove:function(t){if(this._frozen)return this;if(!Array.isArray(t))return this.removeValue(t);for(var i=0;i<t.length;i++)this.removeValue(t[i]);return this},removeValue:function(t){var i;return this.has(t)&&(i=this.list[t],delete this.list[t],delete this.values[t],this.events.emit(h.REMOVE_DATA,this.parent,t,i)),this},pop:function(t){var i=void 0;return!this._frozen&&this.has(t)&&(i=this.list[t],delete this.list[t],delete this.values[t],this.events.emit(h.REMOVE_DATA,this.parent,t,i)),i},has:function(t){return this.list.hasOwnProperty(t)},setFreeze:function(t){return this._frozen=t,this},reset:function(){for(var t in this.list)delete this.list[t],delete this.values[t];return this._frozen=!1,this},destroy:function(){this.reset(),this.events.off(h.CHANGE_DATA),this.events.off(h.SET_DATA),this.events.off(h.REMOVE_DATA),this.parent=null},freeze:{get:function(){return this._frozen},set:function(t){this._frozen=!!t}},count:{get:function(){var t=0;for(var i in this.list)void 0!==this.list[i]&&t++;return t}}});t.exports=s},function(t,i,e){t.exports={CHANGE_DATA:e(161),CHANGE_DATA_KEY:e(162),DESTROY:e(163),REMOVE_DATA:e(164),SET_DATA:e(165)}},function(t,i){t.exports="changedata"},function(t,i){t.exports="changedata-"},function(t,i){t.exports="destroy"},function(t,i){t.exports="removedata"},function(t,i){t.exports="setdata"},function(t,i,e){"use strict";var n=Object.prototype.hasOwnProperty,p="~";function s(){}function h(t,i,e){this.fn=t,this.context=i,this.once=e||!1}function r(t,i,e,n,s){if("function"!=typeof e)throw new TypeError("The listener must be a function");var r=new h(e,n||t,s),o=p?p+i:i;return t._events[o]?t._events[o].fn?t._events[o]=[t._events[o],r]:t._events[o].push(r):(t._events[o]=r,t._eventsCount++),t}function u(t,i){0==--t._eventsCount?t._events=new s:delete t._events[i]}function o(){this._events=new s,this._eventsCount=0}Object.create&&(s.prototype=Object.create(null),(new s).__proto__||(p=!1)),o.prototype.eventNames=function(){var t,i,e=[];if(0===this._eventsCount)return e;for(i in t=this._events)n.call(t,i)&&e.push(p?i.slice(1):i);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(t)):e},o.prototype.listeners=function(t){var i=p?p+t:t,e=this._events[i];if(!e)return[];if(e.fn)return[e.fn];for(var n=0,s=e.length,r=new Array(s);n<s;n++)r[n]=e[n].fn;return r},o.prototype.listenerCount=function(t){var i=p?p+t:t,e=this._events[i];return e?e.fn?1:e.length:0},o.prototype.emit=function(t,i,e,n,s,r){var o=p?p+t:t;if(!this._events[o])return!1;var h,a=this._events[o],u=arguments.length;if(a.fn){switch(a.once&&this.removeListener(t,a.fn,void 0,!0),u){case 1:return a.fn.call(a.context),!0;case 2:return a.fn.call(a.context,i),!0;case 3:return a.fn.call(a.context,i,e),!0;case 4:return a.fn.call(a.context,i,e,n),!0;case 5:return a.fn.call(a.context,i,e,n,s),!0;case 6:return a.fn.call(a.context,i,e,n,s,r),!0}for(l=1,h=new Array(u-1);l<u;l++)h[l-1]=arguments[l];a.fn.apply(a.context,h)}else for(var c,f=a.length,l=0;l<f;l++)switch(a[l].once&&this.removeListener(t,a[l].fn,void 0,!0),u){case 1:a[l].fn.call(a[l].context);break;case 2:a[l].fn.call(a[l].context,i);break;case 3:a[l].fn.call(a[l].context,i,e);break;case 4:a[l].fn.call(a[l].context,i,e,n);break;default:if(!h)for(c=1,h=new Array(u-1);c<u;c++)h[c-1]=arguments[c];a[l].fn.apply(a[l].context,h)}return!0},o.prototype.on=function(t,i,e){return r(this,t,i,e,!1)},o.prototype.once=function(t,i,e){return r(this,t,i,e,!0)},o.prototype.removeListener=function(t,i,e,n){var s=p?p+t:t;if(!this._events[s])return this;if(!i)return u(this,s),this;var r=this._events[s];if(r.fn)r.fn!==i||n&&!r.once||e&&r.context!==e||u(this,s);else{for(var o=0,h=[],a=r.length;o<a;o++)(r[o].fn!==i||n&&!r[o].once||e&&r[o].context!==e)&&h.push(r[o]);h.length?this._events[s]=1===h.length?h[0]:h:u(this,s)}return this},o.prototype.removeAllListeners=function(t){var i;return t?(i=p?p+t:t,this._events[i]&&u(this,i)):(this._events=new s,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=p,o.EventEmitter=o,t.exports=o},function(t,i){t.exports="addedtoscene"},function(t,i){t.exports="destroy"},function(t,i){t.exports="removedfromscene"},function(t,i){t.exports="complete"},function(t,i){t.exports="created"},function(t,i){t.exports="error"},function(t,i){t.exports="loop"},function(t,i){t.exports="play"},function(t,i){t.exports="seeked"},function(t,i){t.exports="seeking"},function(t,i){t.exports="stop"},function(t,i){t.exports="timeout"},function(t,i){t.exports="unlocked"},function(t,i,e){var r=e(181),n=e(0),s=e(199),o=e(35),h=e(37),a=e(250),u=new n({Extends:o,Mixins:[s.Alpha,s.BlendMode,s.Depth,s.Flip,s.GetBounds,s.Mask,s.Origin,s.Pipeline,s.ScrollFactor,s.Size,s.TextureCrop,s.Tint,s.Transform,s.Visible,a],initialize:function(t,i,e,n,s){o.call(this,t,"Sprite"),this._crop=this.resetCropObject(),this.anims=new r(this),this.setTexture(n,s),this.setPosition(i,e),this.setSizeToFrame(),this.setOriginFromFrame(),this.initPipeline(),this.on(h.ADDED_TO_SCENE,this.addedToScene,this),this.on(h.REMOVED_FROM_SCENE,this.removedFromScene,this)},addedToScene:function(){this.scene.sys.updateList.add(this)},removedFromScene:function(){this.scene.sys.updateList.remove(this)},preUpdate:function(t,i){this.anims.update(t,i)},play:function(t,i){return this.anims.play(t,i)},playReverse:function(t,i){return this.anims.playReverse(t,i)},playAfterDelay:function(t,i){return this.anims.playAfterDelay(t,i)},playAfterRepeat:function(t,i){return this.anims.playAfterRepeat(t,i)},chain:function(t){return this.anims.chain(t)},stop:function(){return this.anims.stop()},stopAfterDelay:function(t){return this.anims.stopAfterDelay(t)},stopAfterRepeat:function(t){return this.anims.stopAfterRepeat(t)},stopOnFrame:function(t){return this.anims.stopOnFrame(t)},toJSON:function(){return s.ToJSON(this)},preDestroy:function(){this.anims.destroy(),this.anims=void 0}});t.exports=u},function(t,i,e){var n=e(0),s=e(182),u=e(183),r=e(38),o=e(195),h=new n({initialize:function(t){this.parent=t,this.animationManager=t.scene.sys.anims,this.animationManager.on(r.REMOVE_ANIMATION,this.globalRemove,this),this.textureManager=this.animationManager.textureManager,this.anims=null,this.isPlaying=!1,this.hasStarted=!1,this.currentAnim=null,this.currentFrame=null,this.nextAnim=null,this.nextAnimsQueue=[],this.timeScale=1,this.frameRate=0,this.duration=0,this.msPerFrame=0,this.skipMissedFrames=!0,this.delay=0,this.repeat=0,this.repeatDelay=0,this.yoyo=!1,this.showOnStart=!1,this.hideOnComplete=!1,this.forward=!0,this.inReverse=!1,this.accumulator=0,this.nextTick=0,this.delayCounter=0,this.repeatCounter=0,this.pendingRepeat=!1,this._paused=!1,this._wasPlaying=!1,this._pendingStop=0,this._pendingStopValue},chain:function(t){var i=this.parent;if(void 0===t)return this.nextAnimsQueue.length=0,this.nextAnim=null,i;Array.isArray(t)||(t=[t]);for(var e=0;e<t.length;e++){var n=t[e];null===this.nextAnim?this.nextAnim=n:this.nextAnimsQueue.push(n)}return this.parent},getName:function(){return this.currentAnim?this.currentAnim.key:""},getFrameName:function(){return this.currentFrame?this.currentFrame.textureFrame:""},load:function(t){this.isPlaying&&this.stop();var i,e,n,s,r,o=this.animationManager,h="string"==typeof t?t:u(t,"key",null),a=this.exists(h)?this.get(h):o.get(h);return a?(i=(this.currentAnim=a).getTotalFrames(),e=u(t,"frameRate",a.frameRate),n=u(t,"duration",a.duration),a.calculateDuration(this,i,n,e),this.delay=u(t,"delay",a.delay),this.repeat=u(t,"repeat",a.repeat),this.repeatDelay=u(t,"repeatDelay",a.repeatDelay),this.yoyo=u(t,"yoyo",a.yoyo),this.showOnStart=u(t,"showOnStart",a.showOnStart),this.hideOnComplete=u(t,"hideOnComplete",a.hideOnComplete),this.skipMissedFrames=u(t,"skipMissedFrames",a.skipMissedFrames),this.timeScale=u(t,"timeScale",this.timeScale),(s=u(t,"startFrame",0))>a.getTotalFrames()&&(s=0),r=a.frames[s],0!==s||this.forward||(r=a.getLastFrame()),this.currentFrame=r):console.warn("Missing animation: "+h),this.parent},pause:function(t){return this._paused||(this._paused=!0,this._wasPlaying=this.isPlaying,this.isPlaying=!1),void 0!==t&&this.setCurrentFrame(t),this.parent},resume:function(t){return this._paused&&(this._paused=!1,this.isPlaying=this._wasPlaying),void 0!==t&&this.setCurrentFrame(t),this.parent},playAfterDelay:function(t,i){var e,n;return this.isPlaying?(e=this.nextAnim,n=this.nextAnimsQueue,e&&n.unshift(e),this.nextAnim=t,this._pendingStop=1,this._pendingStopValue=i):(this.delayCounter=i,this.play(t,!0)),this.parent},playAfterRepeat:function(t,i){var e,n;return void 0===i&&(i=1),this.isPlaying?(e=this.nextAnim,n=this.nextAnimsQueue,e&&n.unshift(e),-1!==this.repeatCounter&&i>this.repeatCounter&&(i=this.repeatCounter),this.nextAnim=t,this._pendingStop=2,this._pendingStopValue=i):this.play(t),this.parent},play:function(t,i){void 0===i&&(i=!1);var e=this.currentAnim,n=this.parent,s="string"==typeof t?t:t.key;if(i&&this.isPlaying&&e.key===s)return n;if(e&&this.isPlaying){var r=this.animationManager.getMix(e.key,t);if(0<r)return this.playAfterDelay(t,r)}return this.forward=!0,this.inReverse=!1,this._paused=!1,this._wasPlaying=!0,this.startAnimation(t)},playReverse:function(t,i){void 0===i&&(i=!1);var e="string"==typeof t?t:t.key;return i&&this.isPlaying&&this.currentAnim.key===e?this.parent:(this.forward=!1,this.inReverse=!0,this._paused=!1,this._wasPlaying=!0,this.startAnimation(t))},startAnimation:function(t){this.load(t);var i=this.currentAnim,e=this.parent;return i&&(this.repeatCounter=-1===this.repeat?Number.MAX_VALUE:this.repeat,i.getFirstTick(this),this.isPlaying=!0,this.pendingRepeat=!1,this.hasStarted=!1,this._pendingStop=0,this._pendingStopValue=0,this._paused=!1,this.delayCounter+=this.delay,0===this.delayCounter&&this.handleStart()),e},handleStart:function(){this.showOnStart&&this.parent.setVisible(!0),this.setCurrentFrame(this.currentFrame),this.hasStarted=!0,this.emitEvents(r.ANIMATION_START)},handleRepeat:function(){this.pendingRepeat=!1,this.emitEvents(r.ANIMATION_REPEAT)},handleStop:function(){this._pendingStop=0,this.isPlaying=!1,this.emitEvents(r.ANIMATION_STOP)},handleComplete:function(){this._pendingStop=0,this.isPlaying=!1,this.hideOnComplete&&this.parent.setVisible(!1),this.emitEvents(r.ANIMATION_COMPLETE,r.ANIMATION_COMPLETE_KEY)},emitEvents:function(t,i){var e=this.currentAnim,n=this.currentFrame,s=this.parent,r=n.textureFrame;s.emit(t,e,n,s,r),i&&s.emit(i+e.key,e,n,s,r)},reverse:function(){return this.isPlaying&&(this.inReverse=!this.inReverse,this.forward=!this.forward),this.parent},getProgress:function(){var t=this.currentFrame;if(!t)return 0;var i=t.progress;return this.inReverse&&(i*=-1),i},setProgress:function(t){return this.forward||(t=1-t),this.setCurrentFrame(this.currentAnim.getFrameByProgress(t)),this.parent},setRepeat:function(t){return this.repeatCounter=-1===t?Number.MAX_VALUE:t,this.parent},globalRemove:function(t,i){void 0===i&&(i=this.currentAnim),this.isPlaying&&i.key===this.currentAnim.key&&(this.stop(),this.setCurrentFrame(this.currentAnim.frames[0]))},restart:function(t,i){void 0===t&&(t=!1),void 0===i&&(i=!1);var e=this.currentAnim,n=this.parent;return e?(i&&(this.repeatCounter=-1===this.repeat?Number.MAX_VALUE:this.repeat),e.getFirstTick(this),this.emitEvents(r.ANIMATION_RESTART),this.isPlaying=!0,this.pendingRepeat=!1,this.hasStarted=!t,this._pendingStop=0,this._pendingStopValue=0,this._paused=!1,this.setCurrentFrame(e.frames[0]),this.parent):n},complete:function(){var t;return this._pendingStop=0,this.isPlaying=!1,this.currentAnim&&this.handleComplete(),this.nextAnim&&(t=this.nextAnim,this.nextAnim=0<this.nextAnimsQueue.length?this.nextAnimsQueue.shift():null,this.play(t)),this.parent},stop:function(){var t;return this._pendingStop=0,this.isPlaying=!1,this.currentAnim&&this.handleStop(),this.nextAnim&&(t=this.nextAnim,this.nextAnim=this.nextAnimsQueue.shift(),this.play(t)),this.parent},stopAfterDelay:function(t){return this._pendingStop=1,this._pendingStopValue=t,this.parent},stopAfterRepeat:function(t){return void 0===t&&(t=1),-1!==this.repeatCounter&&t>this.repeatCounter&&(t=this.repeatCounter),this._pendingStop=2,this._pendingStopValue=t,this.parent},stopOnFrame:function(t){return this._pendingStop=3,this._pendingStopValue=t,this.parent},getTotalFrames:function(){return this.currentAnim?this.currentAnim.getTotalFrames():0},update:function(t,i){var e=this.currentAnim;if(this.isPlaying&&e&&!e.paused){if(this.accumulator+=i*this.timeScale,1===this._pendingStop&&(this._pendingStopValue-=i,this._pendingStopValue<=0))return this.stop();if(this.hasStarted){if(this.accumulator>=this.nextTick&&(this.forward?e.nextFrame(this):e.previousFrame(this),this.isPlaying&&0===this._pendingStop&&this.skipMissedFrames&&this.accumulator>this.nextTick))for(var n=0;this.forward?e.nextFrame(this):e.previousFrame(this),n++,this.accumulator>this.nextTick&&n<60;);}else this.accumulator>=this.delayCounter&&(this.accumulator-=this.delayCounter,this.handleStart())}},setCurrentFrame:function(t){var i=this.parent;return this.currentFrame=t,i.texture=t.frame.texture,i.frame=t.frame,i.isCropped&&i.frame.updateCropUVs(i._crop,i.flipX,i.flipY),t.setAlpha&&(i.alpha=t.alpha),i.setSizeToFrame(),i._originComponent&&(t.frame.customPivot?i.setOrigin(t.frame.pivotX,t.frame.pivotY):i.updateDisplayOrigin()),this.isPlaying&&this.hasStarted&&(this.emitEvents(r.ANIMATION_UPDATE),3===this._pendingStop&&this._pendingStopValue===t&&this.stop()),i},nextFrame:function(){return this.currentAnim&&this.currentAnim.nextFrame(this),this.parent},previousFrame:function(){return this.currentAnim&&this.currentAnim.previousFrame(this),this.parent},get:function(t){return this.anims?this.anims.get(t):null},exists:function(t){return!!this.anims&&this.anims.has(t)},create:function(t){var i=t.key,e=!1;return i&&((e=this.get(i))||(e=new o(this,i,t),this.anims||(this.anims=new s),this.anims.set(i,e))),e},generateFrameNames:function(t,i){return this.animationManager.generateFrameNames(t,i)},generateFrameNumbers:function(t,i){return this.animationManager.generateFrameNumbers(t,i)},remove:function(t){var i=this.get(t);return i&&(this.currentAnim===i&&this.stop(),this.anims.delete(t)),i},destroy:function(){this.animationManager.off(r.REMOVE_ANIMATION,this.globalRemove,this),this.anims&&this.anims.clear(),this.animationManager=null,this.parent=null,this.nextAnim=null,this.nextAnimsQueue.length=0,this.currentAnim=null,this.currentFrame=null},isPaused:{get:function(){return this._paused}}});t.exports=h},function(t,i,e){var n=new(e(0))({initialize:function(t){if(this.entries={},this.size=0,Array.isArray(t))for(var i=0;i<t.length;i++)this.set(t[i][0],t[i][1])},set:function(t,i){return this.has(t)||this.size++,this.entries[t]=i,this},get:function(t){if(this.has(t))return this.entries[t]},getArray:function(){var t=[],i=this.entries;for(var e in i)t.push(i[e]);return t},has:function(t){return this.entries.hasOwnProperty(t)},delete:function(t){return this.has(t)&&(delete this.entries[t],this.size--),this},clear:function(){return Object.keys(this.entries).forEach(function(t){delete this.entries[t]},this),this.size=0,this},keys:function(){return Object.keys(this.entries)},values:function(){var t=[],i=this.entries;for(var e in i)t.push(i[e]);return t},dump:function(){var t=this.entries;for(var i in console.group("Map"),t)console.log(i,t[i]);console.groupEnd()},each:function(t){var i=this.entries;for(var e in i)if(!1===t(e,i[e]))break;return this},contains:function(t){var i=this.entries;for(var e in i)if(i[e]===t)return!0;return!1},merge:function(t,i){void 0===i&&(i=!1);var e=this.entries,n=t.entries;for(var s in n)e.hasOwnProperty(s)&&i?e[s]=n[s]:this.set(s,n[s]);return this}});t.exports=n},function(t,i){t.exports=function(t,i,e){var n=typeof t;return t&&"number"!=n&&"string"!=n&&t.hasOwnProperty(i)&&void 0!==t[i]?t[i]:e}},function(t,i){t.exports="add"},function(t,i){t.exports="animationcomplete"},function(t,i){t.exports="animationcomplete-"},function(t,i){t.exports="animationrepeat"},function(t,i){t.exports="animationrestart"},function(t,i){t.exports="animationstart"},function(t,i){t.exports="animationstop"},function(t,i){t.exports="animationupdate"},function(t,i){t.exports="pauseall"},function(t,i){t.exports="remove"},function(t,i){t.exports="resumeall"},function(t,i,e){var n=e(4),s=e(0),r=e(38),o=e(196),x=e(197),v=e(12),m=e(198),h=new s({initialize:function(t,i,e){this.manager=t,this.key=i,this.type="frame",this.frames=this.getFrames(t.textureManager,v(e,"frames",[]),v(e,"defaultTextureKey",null),v(e,"sortFrames",!0)),this.frameRate=v(e,"frameRate",null),this.duration=v(e,"duration",null),this.msPerFrame,this.skipMissedFrames=v(e,"skipMissedFrames",!0),this.delay=v(e,"delay",0),this.repeat=v(e,"repeat",0),this.repeatDelay=v(e,"repeatDelay",0),this.yoyo=v(e,"yoyo",!1),this.showOnStart=v(e,"showOnStart",!1),this.hideOnComplete=v(e,"hideOnComplete",!1),this.paused=!1,this.calculateDuration(this,this.getTotalFrames(),this.duration,this.frameRate),this.manager.on&&(this.manager.on(r.PAUSE_ALL,this.pause,this),this.manager.on(r.RESUME_ALL,this.resume,this))},getTotalFrames:function(){return this.frames.length},calculateDuration:function(t,i,e,n){null===e&&null===n?(t.frameRate=24,t.duration=24/i*1e3):e&&null===n?(t.duration=e,t.frameRate=i/(e/1e3)):(t.frameRate=n,t.duration=i/n*1e3),t.msPerFrame=1e3/t.frameRate},addFrame:function(t){return this.addFrameAt(this.frames.length,t)},addFrameAt:function(t,i){var e,n,s=this.getFrames(this.manager.textureManager,i);return 0<s.length&&(0===t?this.frames=s.concat(this.frames):t===this.frames.length?this.frames=this.frames.concat(s):(e=this.frames.slice(0,t),n=this.frames.slice(t),this.frames=e.concat(s,n)),this.updateFrameSequence()),this},checkFrame:function(t){return 0<=t&&t<this.frames.length},getFirstTick:function(t){t.accumulator=0,t.nextTick=t.msPerFrame+t.currentFrame.duration},getFrameAt:function(t){return this.frames[t]},getFrames:function(t,i,e,n){void 0===n&&(n=!0);var s,r,o,h=[],a=1;if("string"==typeof i&&(r=i,o=t.get(r).getFrameNames(),n&&m(o),i=[],o.forEach(function(t){i.push({key:r,frame:t})})),!Array.isArray(i)||0===i.length)return h;for(y=0;y<i.length;y++){var u,c,f,l=i[y],p=v(l,"key",e);p&&(u=v(l,"frame",0),c=t.getFrame(p,u),(f=new x(p,u,a,c)).duration=v(l,"duration",0),f.isFirst=!s,s&&((s.nextFrame=f).prevFrame=s),h.push(f),s=f,a++)}if(0<h.length){f.isLast=!0,f.nextFrame=h[0],h[0].prevFrame=f;for(var d=1/(h.length-1),y=0;y<h.length;y++)h[y].progress=y*d}return h},getNextTick:function(t){t.accumulator-=t.nextTick,t.nextTick=t.msPerFrame+t.currentFrame.duration},getFrameByProgress:function(t){return t=n(t,0,1),o(t,this.frames,"progress")},nextFrame:function(t){var i=t.currentFrame;i.isLast?t.yoyo?this.handleYoyoFrame(t,!1):0<t.repeatCounter?t.inReverse&&t.forward?t.forward=!1:this.repeatAnimation(t):t.complete():this.updateAndGetNextTick(t,i.nextFrame)},handleYoyoFrame:function(t,i){if(i=i||!1,t.inReverse===!i&&0<t.repeatCounter)return 0!==t.repeatDelay&&!t.pendingRepeat||(t.forward=i),void this.repeatAnimation(t);var e;t.inReverse===i||0!==t.repeatCounter?(e=(t.forward=i)?t.currentFrame.nextFrame:t.currentFrame.prevFrame,this.updateAndGetNextTick(t,e)):t.complete()},getLastFrame:function(){return this.frames[this.frames.length-1]},previousFrame:function(t){var i=t.currentFrame;i.isFirst?t.yoyo?this.handleYoyoFrame(t,!0):0<t.repeatCounter?(t.inReverse&&!t.forward||(t.forward=!0),this.repeatAnimation(t)):t.complete():this.updateAndGetNextTick(t,i.prevFrame)},updateAndGetNextTick:function(t,i){t.setCurrentFrame(i),this.getNextTick(t)},removeFrame:function(t){var i=this.frames.indexOf(t);return-1!==i&&this.removeFrameAt(i),this},removeFrameAt:function(t){return this.frames.splice(t,1),this.updateFrameSequence(),this},repeatAnimation:function(t){if(2===t._pendingStop){if(0===t._pendingStopValue)return t.stop();t._pendingStopValue--}0<t.repeatDelay&&!t.pendingRepeat?(t.pendingRepeat=!0,t.accumulator-=t.nextTick,t.nextTick+=t.repeatDelay):(t.repeatCounter--,t.forward?t.setCurrentFrame(t.currentFrame.nextFrame):t.setCurrentFrame(t.currentFrame.prevFrame),t.isPlaying&&(this.getNextTick(t),t.handleRepeat()))},toJSON:function(){var i={key:this.key,type:this.type,frames:[],frameRate:this.frameRate,duration:this.duration,skipMissedFrames:this.skipMissedFrames,delay:this.delay,repeat:this.repeat,repeatDelay:this.repeatDelay,yoyo:this.yoyo,showOnStart:this.showOnStart,hideOnComplete:this.hideOnComplete};return this.frames.forEach(function(t){i.frames.push(t.toJSON())}),i},updateFrameSequence:function(){for(var t,i=this.frames.length,e=1/(i-1),n=0;n<i;n++)(t=this.frames[n]).index=n+1,t.isFirst=!1,t.isLast=!1,t.progress=n*e,0===n?(t.isFirst=!0,1===i?(t.isLast=!0,(t.nextFrame=t).prevFrame=t):(t.isLast=!1,t.prevFrame=this.frames[i-1],t.nextFrame=this.frames[n+1])):n===i-1&&1<i?(t.isLast=!0,t.prevFrame=this.frames[i-2],t.nextFrame=this.frames[0]):1<i&&(t.prevFrame=this.frames[n-1],t.nextFrame=this.frames[n+1]);return this},pause:function(){return this.paused=!0,this},resume:function(){return this.paused=!1,this},destroy:function(){this.manager.off&&(this.manager.off(r.PAUSE_ALL,this.pause,this),this.manager.off(r.RESUME_ALL,this.resume,this)),this.manager.remove(this.key);for(var t=0;t<this.frames.length;t++)this.frames[t].destroy();this.frames=[],this.manager=null}});t.exports=h},function(t,i){t.exports=function(t,i,e){if(!i.length)return NaN;if(1===i.length)return i[0];var n,s,r=1;if(e){if(t<i[0][e])return i[0];for(;i[r][e]<t;)r++}else for(;i[r]<t;)r++;return r>i.length&&(r=i.length),e?(n=i[r-1][e],(s=i[r][e])-t<=t-n?i[r]:i[r-1]):(n=i[r-1],(s=i[r])-t<=t-n?s:n)}},function(t,i,e){var n=new(e(0))({initialize:function(t,i,e,n,s){void 0===s&&(s=!1),this.textureKey=t,this.textureFrame=i,this.index=e,this.frame=n,this.isFirst=!1,this.isLast=!1,this.prevFrame=null,this.nextFrame=null,this.duration=0,this.progress=0,this.isKeyFrame=s},toJSON:function(){return{key:this.textureKey,frame:this.textureFrame,duration:this.duration,keyframe:this.isKeyFrame}},destroy:function(){this.frame=void 0}});t.exports=n},function(t,i){t.exports=function(t){var e=/\D/g;return t.sort(function(t,i){return parseInt(t.replace(e,""),10)-parseInt(i.replace(e,""),10)}),t}},function(t,i,e){t.exports={Alpha:e(200),AlphaSingle:e(201),BlendMode:e(202),ComputedSize:e(203),Crop:e(204),Depth:e(205),Flip:e(206),GetBounds:e(207),Mask:e(217),Origin:e(237),PathFollower:e(238),Pipeline:e(241),ScrollFactor:e(243),Size:e(244),Texture:e(245),TextureCrop:e(246),Tint:e(247),ToJSON:e(36),Transform:e(248),TransformMatrix:e(41),Visible:e(249)}},function(t,i,e){var s=e(4),n={_alpha:1,_alphaTL:1,_alphaTR:1,_alphaBL:1,_alphaBR:1,clearAlpha:function(){return this.setAlpha(1)},setAlpha:function(t,i,e,n){return void 0===t&&(t=1),void 0===i?this.alpha=t:(this._alphaTL=s(t,0,1),this._alphaTR=s(i,0,1),this._alphaBL=s(e,0,1),this._alphaBR=s(n,0,1)),this},alpha:{get:function(){return this._alpha},set:function(t){var i=s(t,0,1);this._alpha=i,this._alphaTL=i,this._alphaTR=i,this._alphaBL=i,0===(this._alphaBR=i)?this.renderFlags&=-3:this.renderFlags|=2}},alphaTopLeft:{get:function(){return this._alphaTL},set:function(t){var i=s(t,0,1);0!==(this._alphaTL=i)&&(this.renderFlags|=2)}},alphaTopRight:{get:function(){return this._alphaTR},set:function(t){var i=s(t,0,1);0!==(this._alphaTR=i)&&(this.renderFlags|=2)}},alphaBottomLeft:{get:function(){return this._alphaBL},set:function(t){var i=s(t,0,1);0!==(this._alphaBL=i)&&(this.renderFlags|=2)}},alphaBottomRight:{get:function(){return this._alphaBR},set:function(t){var i=s(t,0,1);0!==(this._alphaBR=i)&&(this.renderFlags|=2)}}};t.exports=n},function(t,i,e){var n=e(4),s={_alpha:1,clearAlpha:function(){return this.setAlpha(1)},setAlpha:function(t){return void 0===t&&(t=1),this.alpha=t,this},alpha:{get:function(){return this._alpha},set:function(t){var i=n(t,0,1);0===(this._alpha=i)?this.renderFlags&=-3:this.renderFlags|=2}}};t.exports=s},function(t,i,e){var n=e(14),s={_blendMode:n.NORMAL,blendMode:{get:function(){return this._blendMode},set:function(t){"string"==typeof t&&(t=n[t]),-1<=(t|=0)&&(this._blendMode=t)}},setBlendMode:function(t){return this.blendMode=t,this}};t.exports=s},function(t,i){t.exports={width:0,height:0,displayWidth:{get:function(){return this.scaleX*this.width},set:function(t){this.scaleX=t/this.width}},displayHeight:{get:function(){return this.scaleY*this.height},set:function(t){this.scaleY=t/this.height}},setSize:function(t,i){return this.width=t,this.height=i,this},setDisplaySize:function(t,i){return this.displayWidth=t,this.displayHeight=i,this}}},function(t,i){var e={texture:null,frame:null,isCropped:!1,setCrop:function(t,i,e,n){return void 0===t?this.isCropped=!1:this.frame&&("number"==typeof t?this.frame.setCropUVs(this._crop,t,i,e,n,this.flipX,this.flipY):this.frame.setCropUVs(this._crop,t.x,t.y,t.width,t.height,this.flipX,this.flipY),this.isCropped=!0),this},resetCropObject:function(){return{u0:0,v0:0,u1:0,v1:0,width:0,height:0,x:0,y:0,flipX:!1,flipY:!1,cx:0,cy:0,cw:0,ch:0}}};t.exports=e},function(t,i){var e={_depth:0,depth:{get:function(){return this._depth},set:function(t){this.scene.sys.queueDepthSort(),this._depth=t}},setDepth:function(t){return void 0===t&&(t=0),this.depth=t,this}};t.exports=e},function(t,i){t.exports={flipX:!1,flipY:!1,toggleFlipX:function(){return this.flipX=!this.flipX,this},toggleFlipY:function(){return this.flipY=!this.flipY,this},setFlipX:function(t){return this.flipX=t,this},setFlipY:function(t){return this.flipY=t,this},setFlip:function(t,i){return this.flipX=t,this.flipY=i,this},resetFlip:function(){return this.flipX=!1,this.flipY=!1,this}}},function(t,i,e){var c=e(208),n=e(28),s=e(1),r={prepareBoundsOutput:function(t,i){return void 0===i&&(i=!1),0!==this.rotation&&n(t,this.x,this.y,this.rotation),i&&this.parentContainer&&this.parentContainer.getBoundsTransformMatrix().transformPoint(t.x,t.y,t),t},getCenter:function(t){return void 0===t&&(t=new s),t.x=this.x-this.displayWidth*this.originX+this.displayWidth/2,t.y=this.y-this.displayHeight*this.originY+this.displayHeight/2,t},getTopLeft:function(t,i){return(t=t||new s).x=this.x-this.displayWidth*this.originX,t.y=this.y-this.displayHeight*this.originY,this.prepareBoundsOutput(t,i)},getTopCenter:function(t,i){return(t=t||new s).x=this.x-this.displayWidth*this.originX+this.displayWidth/2,t.y=this.y-this.displayHeight*this.originY,this.prepareBoundsOutput(t,i)},getTopRight:function(t,i){return(t=t||new s).x=this.x-this.displayWidth*this.originX+this.displayWidth,t.y=this.y-this.displayHeight*this.originY,this.prepareBoundsOutput(t,i)},getLeftCenter:function(t,i){return(t=t||new s).x=this.x-this.displayWidth*this.originX,t.y=this.y-this.displayHeight*this.originY+this.displayHeight/2,this.prepareBoundsOutput(t,i)},getRightCenter:function(t,i){return(t=t||new s).x=this.x-this.displayWidth*this.originX+this.displayWidth,t.y=this.y-this.displayHeight*this.originY+this.displayHeight/2,this.prepareBoundsOutput(t,i)},getBottomLeft:function(t,i){return(t=t||new s).x=this.x-this.displayWidth*this.originX,t.y=this.y-this.displayHeight*this.originY+this.displayHeight,this.prepareBoundsOutput(t,i)},getBottomCenter:function(t,i){return(t=t||new s).x=this.x-this.displayWidth*this.originX+this.displayWidth/2,t.y=this.y-this.displayHeight*this.originY+this.displayHeight,this.prepareBoundsOutput(t,i)},getBottomRight:function(t,i){return(t=t||new s).x=this.x-this.displayWidth*this.originX+this.displayWidth,t.y=this.y-this.displayHeight*this.originY+this.displayHeight,this.prepareBoundsOutput(t,i)},getBounds:function(t){var i,e,n,s,r,o,h,a,u;return void 0===t&&(t=new c),a=(h=(this.parentContainer?(u=this.parentContainer.getBoundsTransformMatrix(),this.getTopLeft(t),u.transformPoint(t.x,t.y,t),i=t.x,e=t.y,this.getTopRight(t),u.transformPoint(t.x,t.y,t),n=t.x,s=t.y,this.getBottomLeft(t),u.transformPoint(t.x,t.y,t),r=t.x,o=t.y,this.getBottomRight(t),u.transformPoint(t.x,t.y,t)):(this.getTopLeft(t),i=t.x,e=t.y,this.getTopRight(t),n=t.x,s=t.y,this.getBottomLeft(t),r=t.x,o=t.y,this.getBottomRight(t)),t.x),t.y),t.x=Math.min(i,n,r,h),t.y=Math.min(e,s,o,a),t.width=Math.max(i,n,r,h)-t.x,t.height=Math.max(e,s,o,a)-t.y,t}};t.exports=r},function(t,i,e){var n=e(0),s=e(209),r=e(39),o=e(210),h=e(13),a=e(211),u=e(216),c=new n({initialize:function(t,i,e,n){void 0===t&&(t=0),void 0===i&&(i=0),void 0===e&&(e=0),void 0===n&&(n=0),this.type=h.RECTANGLE,this.x=t,this.y=i,this.width=e,this.height=n},contains:function(t,i){return s(this,t,i)},getPoint:function(t,i){return r(this,t,i)},getPoints:function(t,i,e){return o(this,t,i,e)},getRandomPoint:function(t){return u(this,t)},setTo:function(t,i,e,n){return this.x=t,this.y=i,this.width=e,this.height=n,this},setEmpty:function(){return this.setTo(0,0,0,0)},setPosition:function(t,i){return void 0===i&&(i=t),this.x=t,this.y=i,this},setSize:function(t,i){return void 0===i&&(i=t),this.width=t,this.height=i,this},isEmpty:function(){return this.width<=0||this.height<=0},getLineA:function(t){return void 0===t&&(t=new a),t.setTo(this.x,this.y,this.right,this.y),t},getLineB:function(t){return void 0===t&&(t=new a),t.setTo(this.right,this.y,this.right,this.bottom),t},getLineC:function(t){return void 0===t&&(t=new a),t.setTo(this.right,this.bottom,this.x,this.bottom),t},getLineD:function(t){return void 0===t&&(t=new a),t.setTo(this.x,this.bottom,this.x,this.y),t},left:{get:function(){return this.x},set:function(t){t>=this.right?this.width=0:this.width=this.right-t,this.x=t}},right:{get:function(){return this.x+this.width},set:function(t){t<=this.x?this.width=0:this.width=t-this.x}},top:{get:function(){return this.y},set:function(t){t>=this.bottom?this.height=0:this.height=this.bottom-t,this.y=t}},bottom:{get:function(){return this.y+this.height},set:function(t){t<=this.y?this.height=0:this.height=t-this.y}},centerX:{get:function(){return this.x+this.width/2},set:function(t){this.x=t-this.width/2}},centerY:{get:function(){return this.y+this.height/2},set:function(t){this.y=t-this.height/2}}});t.exports=c},function(t,i){t.exports=function(t,i,e){return!(t.width<=0||t.height<=0)&&(t.x<=i&&t.x+t.width>=i&&t.y<=e&&t.y+t.height>=e)}},function(t,i,e){var o=e(39),h=e(40);t.exports=function(t,i,e,n){void 0===n&&(n=[]),!i&&0<e&&(i=h(t)/e);for(var s=0;s<i;s++){var r=s/i;n.push(o(t,r))}return n}},function(t,i,e){var n=e(0),s=e(212),r=e(213),o=e(13),h=e(215),a=e(1),u=new n({initialize:function(t,i,e,n){void 0===t&&(t=0),void 0===i&&(i=0),void 0===e&&(e=0),void 0===n&&(n=0),this.type=o.LINE,this.x1=t,this.y1=i,this.x2=e,this.y2=n},getPoint:function(t,i){return s(this,t,i)},getPoints:function(t,i,e){return r(this,t,i,e)},getRandomPoint:function(t){return h(this,t)},setTo:function(t,i,e,n){return void 0===t&&(t=0),void 0===i&&(i=0),void 0===e&&(e=0),void 0===n&&(n=0),this.x1=t,this.y1=i,this.x2=e,this.y2=n,this},getPointA:function(t){return void 0===t&&(t=new a),t.set(this.x1,this.y1),t},getPointB:function(t){return void 0===t&&(t=new a),t.set(this.x2,this.y2),t},left:{get:function(){return Math.min(this.x1,this.x2)},set:function(t){this.x1<=this.x2?this.x1=t:this.x2=t}},right:{get:function(){return Math.max(this.x1,this.x2)},set:function(t){this.x1>this.x2?this.x1=t:this.x2=t}},top:{get:function(){return Math.min(this.y1,this.y2)},set:function(t){this.y1<=this.y2?this.y1=t:this.y2=t}},bottom:{get:function(){return Math.max(this.y1,this.y2)},set:function(t){this.y1>this.y2?this.y1=t:this.y2=t}}});t.exports=u},function(t,i,e){var n=e(5);t.exports=function(t,i,e){return void 0===e&&(e=new n),e.x=t.x1+(t.x2-t.x1)*i,e.y=t.y1+(t.y2-t.y1)*i,e}},function(t,i,e){var l=e(214),p=e(5);t.exports=function(t,i,e,n){void 0===n&&(n=[]),!i&&0<e&&(i=l(t)/e);for(var s=t.x1,r=t.y1,o=t.x2,h=t.y2,a=0;a<i;a++){var u=a/i,c=s+(o-s)*u,f=r+(h-r)*u;n.push(new p(c,f))}return n}},function(t,i){t.exports=function(t){return Math.sqrt((t.x2-t.x1)*(t.x2-t.x1)+(t.y2-t.y1)*(t.y2-t.y1))}},function(t,i,e){var n=e(5);t.exports=function(t,i){void 0===i&&(i=new n);var e=Math.random();return i.x=t.x1+e*(t.x2-t.x1),i.y=t.y1+e*(t.y2-t.y1),i}},function(t,i,e){var n=e(5);t.exports=function(t,i){return void 0===i&&(i=new n),i.x=t.x+Math.random()*t.width,i.y=t.y+Math.random()*t.height,i}},function(t,i,e){var n=e(218),s=e(236),r={mask:null,setMask:function(t){return this.mask=t,this},clearMask:function(t){return void 0===t&&(t=!1),t&&this.mask&&this.mask.destroy(),this.mask=null,this},createBitmapMask:function(t){return void 0===t&&(this.texture||this.shader)&&(t=this),new n(this.scene,t)},createGeometryMask:function(t){return void 0===t&&"Graphics"===this.type&&(t=this),new s(this.scene,t)}};t.exports=r},function(t,i,e){var n=e(0),s=e(219),r=new n({initialize:function(t,i){var e=t.sys.game.renderer;this.renderer=e,this.bitmapMask=i,this.maskTexture=null,this.mainTexture=null,this.dirty=!0,this.mainFramebuffer=null,this.maskFramebuffer=null,this.prevFramebuffer=null,this.invertAlpha=!1,this.isStencil=!1,this.createMask(),t.sys.game.events.on(s.CONTEXT_RESTORED,this.createMask,this)},createMask:function(){var t,i,e,n,s,r,o=this.renderer;o.gl&&(this.mainTexture&&this.clearMask(),t=o.width,i=o.height,e=0==(t&t-1)&&0==(i&i-1),n=o.gl,s=e?n.REPEAT:n.CLAMP_TO_EDGE,r=n.LINEAR,this.mainTexture=o.createTexture2D(0,r,r,s,s,n.RGBA,null,t,i),this.maskTexture=o.createTexture2D(0,r,r,s,s,n.RGBA,null,t,i),this.mainFramebuffer=o.createFramebuffer(t,i,this.mainTexture,!0),this.maskFramebuffer=o.createFramebuffer(t,i,this.maskTexture,!0))},clearMask:function(){var t=this.renderer;t.gl&&this.mainTexture&&(t.deleteTexture(this.mainTexture),t.deleteTexture(this.maskTexture),t.deleteFramebuffer(this.mainFramebuffer),t.deleteFramebuffer(this.maskFramebuffer),this.mainTexture=null,this.maskTexture=null,this.mainFramebuffer=null,this.maskFramebuffer=null)},setBitmap:function(t){this.bitmapMask=t},preRenderWebGL:function(t,i,e){t.pipelines.BITMAPMASK_PIPELINE.beginMask(this,i,e)},postRenderWebGL:function(t,i){t.pipelines.BITMAPMASK_PIPELINE.endMask(this,i)},preRenderCanvas:function(){},postRenderCanvas:function(){},destroy:function(){this.clearMask(),this.bitmapMask=null,this.prevFramebuffer=null,this.renderer=null}});t.exports=r},function(t,i,e){t.exports={BLUR:e(220),BOOT:e(221),CONTEXT_LOST:e(222),CONTEXT_RESTORED:e(223),DESTROY:e(224),FOCUS:e(225),HIDDEN:e(226),PAUSE:e(227),POST_RENDER:e(228),POST_STEP:e(229),PRE_RENDER:e(230),PRE_STEP:e(231),READY:e(232),RESUME:e(233),STEP:e(234),VISIBLE:e(235)}},function(t,i){t.exports="blur"},function(t,i){t.exports="boot"},function(t,i){t.exports="contextlost"},function(t,i){t.exports="contextrestored"},function(t,i){t.exports="destroy"},function(t,i){t.exports="focus"},function(t,i){t.exports="hidden"},function(t,i){t.exports="pause"},function(t,i){t.exports="postrender"},function(t,i){t.exports="poststep"},function(t,i){t.exports="prerender"},function(t,i){t.exports="prestep"},function(t,i){t.exports="ready"},function(t,i){t.exports="resume"},function(t,i){t.exports="step"},function(t,i){t.exports="visible"},function(t,i,e){var n=new(e(0))({initialize:function(t,i){this.geometryMask=i,this.invertAlpha=!1,this.isStencil=!0,this.level=0},setShape:function(t){return this.geometryMask=t,this},setInvertAlpha:function(t){return void 0===t&&(t=!0),this.invertAlpha=t,this},preRenderWebGL:function(t,i,e){var n=t.gl;t.flush(),0===t.maskStack.length&&(n.enable(n.STENCIL_TEST),n.clear(n.STENCIL_BUFFER_BIT),t.maskCount=0),t.currentCameraMask.mask!==this&&(t.currentMask.mask=this),t.maskStack.push({mask:this,camera:e}),this.applyStencil(t,e,!0),t.maskCount++},applyStencil:function(t,i,e){var n=t.gl,s=this.geometryMask,r=t.maskCount;n.colorMask(!1,!1,!1,!1),e?(n.stencilFunc(n.EQUAL,r,255),n.stencilOp(n.KEEP,n.KEEP,n.INCR)):(n.stencilFunc(n.EQUAL,r+1,255),n.stencilOp(n.KEEP,n.KEEP,n.DECR)),s.renderWebGL(t,s,i),t.flush(),n.colorMask(!0,!0,!0,!0),n.stencilOp(n.KEEP,n.KEEP,n.KEEP),e?this.invertAlpha?n.stencilFunc(n.NOTEQUAL,r+1,255):n.stencilFunc(n.EQUAL,r+1,255):this.invertAlpha?n.stencilFunc(n.NOTEQUAL,r,255):n.stencilFunc(n.EQUAL,r,255)},postRenderWebGL:function(t){var i,e=t.gl;t.maskStack.pop(),t.maskCount--,0===t.maskStack.length?(t.flush(),t.currentMask.mask=null,e.disable(e.STENCIL_TEST)):(t.flush(),(i=t.maskStack[t.maskStack.length-1]).mask.applyStencil(t,i.camera,!1),t.currentCameraMask.mask!==i.mask?(t.currentMask.mask=i.mask,t.currentMask.camera=i.camera):t.currentMask.mask=null)},preRenderCanvas:function(t,i,e){var n=this.geometryMask;t.currentContext.save(),n.renderCanvas(t,n,e,null,null,!0),t.currentContext.clip()},postRenderCanvas:function(t){t.currentContext.restore()},destroy:function(){this.geometryMask=null}});t.exports=n},function(t,i){var e={_originComponent:!0,originX:.5,originY:.5,_displayOriginX:0,_displayOriginY:0,displayOriginX:{get:function(){return this._displayOriginX},set:function(t){this._displayOriginX=t,this.originX=t/this.width}},displayOriginY:{get:function(){return this._displayOriginY},set:function(t){this._displayOriginY=t,this.originY=t/this.height}},setOrigin:function(t,i){return void 0===t&&(t=.5),void 0===i&&(i=t),this.originX=t,this.originY=i,this.updateDisplayOrigin()},setOriginFromFrame:function(){return this.frame&&this.frame.customPivot?(this.originX=this.frame.pivotX,this.originY=this.frame.pivotY,this.updateDisplayOrigin()):this.setOrigin()},setDisplayOrigin:function(t,i){return void 0===t&&(t=0),void 0===i&&(i=t),this.displayOriginX=t,this.displayOriginY=i,this},updateDisplayOrigin:function(){return this._displayOriginX=this.originX*this.width,this._displayOriginY=this.originY*this.height,this}};t.exports=e},function(t,i,e){var a=e(25),o=e(239),h=e(12),u=e(240),c=e(1),n={path:null,rotateToPath:!1,pathRotationOffset:0,pathOffset:null,pathVector:null,pathDelta:null,pathTween:null,pathConfig:null,_prevDirection:u.PLAYING_FORWARD,setPath:function(t,i){void 0===i&&(i=this.pathConfig);var e=this.pathTween;return e&&e.isPlaying()&&e.stop(),this.path=t,i&&this.startFollow(i),this},setRotateToPath:function(t,i){return void 0===i&&(i=0),this.rotateToPath=t,this.pathRotationOffset=i,this},isFollowing:function(){var t=this.pathTween;return t&&t.isPlaying()},startFollow:function(t,i){void 0===t&&(t={}),void 0===i&&(i=0);var e=this.pathTween;e&&e.isPlaying()&&e.stop(),"number"==typeof t&&(t={duration:t}),t.from=h(t,"from",0),t.to=h(t,"to",1);var n=o(t,"positionOnPath",!1);this.rotateToPath=o(t,"rotateToPath",!1),this.pathRotationOffset=h(t,"rotationOffset",0);var s,r=h(t,"startAt",i);return r&&(t.onStart=function(t){var i=t.data[0];i.progress=r,i.elapsed=i.duration*r;var e=i.ease(i.progress);i.current=i.start+(i.end-i.start)*e,i.target[i.key]=i.current}),this.pathOffset||(this.pathOffset=new c(this.x,this.y)),this.pathVector||(this.pathVector=new c),this.pathDelta||(this.pathDelta=new c),this.pathDelta.reset(),this.pathTween=this.scene.sys.tweens.addCounter(t),this.path.getStartPoint(this.pathOffset),n&&(this.x=this.pathOffset.x,this.y=this.pathOffset.y),this.pathOffset.x=this.x-this.pathOffset.x,this.pathOffset.y=this.y-this.pathOffset.y,this._prevDirection=u.PLAYING_FORWARD,this.rotateToPath&&(s=this.path.getPoint(.1),this.rotation=Math.atan2(s.y-this.y,s.x-this.x)+a(this.pathRotationOffset)),this.pathConfig=t,this},pauseFollow:function(){var t=this.pathTween;return t&&t.isPlaying()&&t.pause(),this},resumeFollow:function(){var t=this.pathTween;return t&&t.isPaused()&&t.resume(),this},stopFollow:function(){var t=this.pathTween;return t&&t.isPlaying()&&t.stop(),this},pathUpdate:function(){var t=this.pathTween;if(t){var i=t.data[0],e=this.pathDelta,n=this.pathVector;if(e.copy(n).negate(),i.state===u.COMPLETE)return this.path.getPoint(1,n),e.add(n),n.add(this.pathOffset),void this.setPosition(n.x,n.y);if(i.state!==u.PLAYING_FORWARD&&i.state!==u.PLAYING_BACKWARD)return;this.path.getPoint(t.getValue(),n),e.add(n),n.add(this.pathOffset);var s=this.x,r=this.y;this.setPosition(n.x,n.y);var o=this.x-s,h=this.y-r;if(0==o&&0==h)return;if(i.state!==this._prevDirection)return void(this._prevDirection=i.state);this.rotateToPath&&(this.rotation=Math.atan2(h,o)+a(this.pathRotationOffset))}}};t.exports=n},function(t,i){t.exports=function(t,i,e){return t&&t.hasOwnProperty(i)?t[i]:e}},function(t,i){t.exports={CREATED:0,INIT:1,DELAY:2,OFFSET_DELAY:3,PENDING_RENDER:4,PLAYING_FORWARD:5,PLAYING_BACKWARD:6,HOLD_DELAY:7,REPEAT_DELAY:8,COMPLETE:9,PENDING_ADD:20,PAUSED:21,LOOP_DELAY:22,ACTIVE:23,COMPLETE_DELAY:24,PENDING_REMOVE:25,REMOVED:26}},function(t,i,e){var n=e(242),s={defaultPipeline:null,pipeline:null,initPipeline:function(t){void 0===t&&(t=n.MULTI_PIPELINE);var i=this.scene.sys.game.renderer.pipelines;return!(!i||!i.has(t))&&(this.defaultPipeline=i.get(t),this.pipeline=this.defaultPipeline,!0)},setPipeline:function(t){var i=this.scene.sys.game.renderer.pipelines;return i&&i.has(t)&&(this.pipeline=i.get(t)),this},resetPipeline:function(){return this.pipeline=this.defaultPipeline,null!==this.pipeline},getPipelineName:function(){return this.pipeline.name}};t.exports=s},function(t,i){t.exports={BITMAPMASK_PIPELINE:"BitmapMaskPipeline",LIGHT_PIPELINE:"Light2D",SINGLE_PIPELINE:"SinglePipeline",MULTI_PIPELINE:"MultiPipeline",ROPE_PIPELINE:"RopePipeline",MESH_PIPELINE:"MeshPipeline"}},function(t,i){var e={scrollFactorX:1,scrollFactorY:1,setScrollFactor:function(t,i){return void 0===i&&(i=t),this.scrollFactorX=t,this.scrollFactorY=i,this}};t.exports=e},function(t,i){var e={_sizeComponent:!0,width:0,height:0,displayWidth:{get:function(){return Math.abs(this.scaleX*this.frame.realWidth)},set:function(t){this.scaleX=t/this.frame.realWidth}},displayHeight:{get:function(){return Math.abs(this.scaleY*this.frame.realHeight)},set:function(t){this.scaleY=t/this.frame.realHeight}},setSizeToFrame:function(t){return void 0===t&&(t=this.frame),this.width=t.realWidth,this.height=t.realHeight,this},setSize:function(t,i){return this.width=t,this.height=i,this},setDisplaySize:function(t,i){return this.displayWidth=t,this.displayHeight=i,this}};t.exports=e},function(t,i){var e={texture:null,frame:null,isCropped:!1,setTexture:function(t,i){return this.texture=this.scene.sys.textures.get(t),this.setFrame(i)},setFrame:function(t,i,e){return void 0===i&&(i=!0),void 0===e&&(e=!0),this.frame=this.texture.get(t),this.frame.cutWidth&&this.frame.cutHeight?this.renderFlags|=8:this.renderFlags&=-9,this._sizeComponent&&i&&this.setSizeToFrame(),this._originComponent&&e&&(this.frame.customPivot?this.setOrigin(this.frame.pivotX,this.frame.pivotY):this.updateDisplayOrigin()),this}};t.exports=e},function(t,i){var e={texture:null,frame:null,isCropped:!1,setCrop:function(t,i,e,n){return void 0===t?this.isCropped=!1:this.frame&&("number"==typeof t?this.frame.setCropUVs(this._crop,t,i,e,n,this.flipX,this.flipY):this.frame.setCropUVs(this._crop,t.x,t.y,t.width,t.height,this.flipX,this.flipY),this.isCropped=!0),this},setTexture:function(t,i){return this.texture=this.scene.sys.textures.get(t),this.setFrame(i)},setFrame:function(t,i,e){return void 0===i&&(i=!0),void 0===e&&(e=!0),this.frame=this.texture.get(t),this.frame.cutWidth&&this.frame.cutHeight?this.renderFlags|=8:this.renderFlags&=-9,this._sizeComponent&&i&&this.setSizeToFrame(),this._originComponent&&e&&(this.frame.customPivot?this.setOrigin(this.frame.pivotX,this.frame.pivotY):this.updateDisplayOrigin()),this.isCropped&&this.frame.updateCropUVs(this._crop,this.flipX,this.flipY),this},resetCropObject:function(){return{u0:0,v0:0,u1:0,v1:0,width:0,height:0,x:0,y:0,flipX:!1,flipY:!1,cx:0,cy:0,cw:0,ch:0}}};t.exports=e},function(t,i){var e={tintTopLeft:16777215,tintTopRight:16777215,tintBottomLeft:16777215,tintBottomRight:16777215,tintFill:!1,clearTint:function(){return this.setTint(16777215),this},setTint:function(t,i,e,n){return void 0===t&&(t=16777215),void 0===i&&(n=e=i=t),this.tintTopLeft=t,this.tintTopRight=i,this.tintBottomLeft=e,this.tintBottomRight=n,this.tintFill=!1,this},setTintFill:function(t,i,e,n){return this.setTint(t,i,e,n),this.tintFill=!0,this},tint:{set:function(t){this.setTint(t,t,t,t)}},isTinted:{get:function(){var t=16777215;return this.tintFill||this.tintTopLeft!==t||this.tintTopRight!==t||this.tintBottomLeft!==t||this.tintBottomRight!==t}}};t.exports=e},function(t,i,e){var n=e(2),s=e(41),a=e(29),r=e(16),o=e(17),u=e(1),h={_scaleX:1,_scaleY:1,_rotation:0,x:0,y:0,z:0,w:0,scale:{get:function(){return(this._scaleX+this._scaleY)/2},set:function(t){this._scaleX=t,0===(this._scaleY=t)?this.renderFlags&=-5:this.renderFlags|=4}},scaleX:{get:function(){return this._scaleX},set:function(t){0===(this._scaleX=t)?this.renderFlags&=-5:this.renderFlags|=4}},scaleY:{get:function(){return this._scaleY},set:function(t){0===(this._scaleY=t)?this.renderFlags&=-5:this.renderFlags|=4}},angle:{get:function(){return o(this._rotation*n.RAD_TO_DEG)},set:function(t){this.rotation=o(t)*n.DEG_TO_RAD}},rotation:{get:function(){return this._rotation},set:function(t){this._rotation=r(t)}},setPosition:function(t,i,e,n){return void 0===t&&(t=0),void 0===i&&(i=t),void 0===e&&(e=0),void 0===n&&(n=0),this.x=t,this.y=i,this.z=e,this.w=n,this},copyPosition:function(t){return void 0!==t.x&&(this.x=t.x),void 0!==t.y&&(this.y=t.y),void 0!==t.z&&(this.z=t.z),void 0!==t.w&&(this.w=t.w),this},setRandomPosition:function(t,i,e,n){return void 0===t&&(t=0),void 0===i&&(i=0),void 0===e&&(e=this.scene.sys.scale.width),void 0===n&&(n=this.scene.sys.scale.height),this.x=t+Math.random()*e,this.y=i+Math.random()*n,this},setRotation:function(t){return void 0===t&&(t=0),this.rotation=t,this},setAngle:function(t){return void 0===t&&(t=0),this.angle=t,this},setScale:function(t,i){return void 0===t&&(t=1),void 0===i&&(i=t),this.scaleX=t,this.scaleY=i,this},setX:function(t){return void 0===t&&(t=0),this.x=t,this},setY:function(t){return void 0===t&&(t=0),this.y=t,this},setZ:function(t){return void 0===t&&(t=0),this.z=t,this},setW:function(t){return void 0===t&&(t=0),this.w=t,this},getLocalTransformMatrix:function(t){return void 0===t&&(t=new s),t.applyITRS(this.x,this.y,this._rotation,this._scaleX,this._scaleY)},getWorldTransformMatrix:function(t,i){void 0===t&&(t=new s),void 0===i&&(i=new s);var e=this.parentContainer;if(!e)return this.getLocalTransformMatrix(t);for(t.applyITRS(this.x,this.y,this._rotation,this._scaleX,this._scaleY);e;)i.applyITRS(e.x,e.y,e._rotation,e._scaleX,e._scaleY),i.multiply(t,t),e=e.parentContainer;return t},getLocalPoint:function(t,i,e,n){e=e||new u;var s=(n=n||this.scene.sys.cameras.main).scrollX,r=n.scrollY,o=t+s*this.scrollFactorX-s,h=i+r*this.scrollFactorY-r;return this.parentContainer?this.getWorldTransformMatrix().applyInverse(o,h,e):a(o,h,this.x,this.y,this.rotation,this.scaleX,this.scaleY,e),this._originComponent&&(e.x+=this._displayOriginX,e.y+=this._displayOriginY),e},getParentRotation:function(){for(var t=0,i=this.parentContainer;i;)t+=i.rotation,i=i.parentContainer;return t}};t.exports=h},function(t,i){var e={_visible:!0,visible:{get:function(){return this._visible},set:function(t){t?(this._visible=!0,this.renderFlags|=1):(this._visible=!1,this.renderFlags&=-2)}},setVisible:function(t){return this.visible=t,this}};t.exports=e},function(t,i,e){var n=e(7),s=e(7),n=e(251),s=e(252);t.exports={renderWebGL:n,renderCanvas:s}},function(t,i){t.exports=function(t,i,e,n){this.pipeline.batchSprite(i,e,n)}},function(t,i){t.exports=function(t,i,e,n){t.batchSprite(i,i.frame,e,n)}},function(t,i,e){var s=e(33),n=e(0),r=new(e(3)),o=new n({Extends:s,initialize:function(t,i,e,n){void 0===i&&(i=80),void 0===e&&(e=0),void 0===n&&(n=0),s.call(this,t),this.viewportWidth=e,this.viewportHeight=n,this.fieldOfView=i*Math.PI/180,this.update()},setFOV:function(t){return this.fieldOfView=t*Math.PI/180,this},update:function(){var t=this.viewportWidth/this.viewportHeight;return this.projection.perspective(this.fieldOfView,t,Math.abs(this.near),Math.abs(this.far)),r.copy(this.position).add(this.direction),this.view.lookAt(this.position,r,this.up),this.combined.copy(this.projection).multiply(this.view),this.invProjectionView.copy(this.combined).invert(),this.billboardMatrixDirty=!0,this.updateChildren(),this}});t.exports=o},function(t,i,e){var n=e(255),s=e(0),r=e(256),o=new s({Extends:n,initialize:function(t,i){n.call(this,i),this.scene=t,this.systems=t.sys,t.sys.events.once(r.BOOT,this.boot,this)},boot:function(){},destroy:function(){this.pluginManager=null,this.game=null,this.scene=null,this.systems=null}});t.exports=o},function(t,i,e){var n=new(e(0))({initialize:function(t){this.pluginManager=t,this.game=t.game},init:function(){},start:function(){},stop:function(){},destroy:function(){this.pluginManager=null,this.game=null,this.scene=null,this.systems=null}});t.exports=n},function(t,i,e){t.exports={ADDED_TO_SCENE:e(257),BOOT:e(258),CREATE:e(259),DESTROY:e(260),PAUSE:e(261),POST_UPDATE:e(262),PRE_UPDATE:e(263),READY:e(264),REMOVED_FROM_SCENE:e(265),RENDER:e(266),RESUME:e(267),SHUTDOWN:e(268),SLEEP:e(269),START:e(270),TRANSITION_COMPLETE:e(271),TRANSITION_INIT:e(272),TRANSITION_OUT:e(273),TRANSITION_START:e(274),TRANSITION_WAKE:e(275),UPDATE:e(276),WAKE:e(277)}},function(t,i){t.exports="addedtoscene"},function(t,i){t.exports="boot"},function(t,i){t.exports="create"},function(t,i){t.exports="destroy"},function(t,i){t.exports="pause"},function(t,i){t.exports="postupdate"},function(t,i){t.exports="preupdate"},function(t,i){t.exports="ready"},function(t,i){t.exports="removedfromscene"},function(t,i){t.exports="render"},function(t,i){t.exports="resume"},function(t,i){t.exports="shutdown"},function(t,i){t.exports="sleep"},function(t,i){t.exports="start"},function(t,i){t.exports="transitioncomplete"},function(t,i){t.exports="transitioninit"},function(t,i){t.exports="transitionout"},function(t,i){t.exports="transitionstart"},function(t,i){t.exports="transitionwake"},function(t,i){t.exports="update"},function(t,i){t.exports="wake"}]);