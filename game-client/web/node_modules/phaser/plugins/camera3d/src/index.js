/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2018 Photon Storm Ltd.
 * @license      {@link https://github.com/photonstorm/phaser/blob/master/license.txt|MIT License}
 */

/**
 * @namespace Phaser.Cameras.Sprite3D
 */

module.exports = {

    Camera: require('./Camera'),
    CameraManager: require('./CameraManager'),
    OrthographicCamera: require('./OrthographicCamera'),
    PerspectiveCamera: require('./PerspectiveCamera')

};
