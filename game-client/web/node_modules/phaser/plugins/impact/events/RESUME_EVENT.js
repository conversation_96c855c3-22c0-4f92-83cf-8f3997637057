/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2020 Photon Storm Ltd.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * The Impact Physics World Resume Event.
 * 
 * This event is dispatched by an Impact Physics World instance when it resumes from a paused state.
 * 
 * Listen to it from a Scene using: `this.impact.world.on('resume', listener)`.
 *
 * @event Phaser.Physics.Impact.Events#RESUME
 * @since 3.0.0
 */
module.exports = 'resume';
