/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2020 Photon Storm Ltd.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * The Impact Physics World Pause Event.
 * 
 * This event is dispatched by an Impact Physics World instance when it is paused.
 * 
 * Listen to it from a Scene using: `this.impact.world.on('pause', listener)`.
 *
 * @event Phaser.Physics.Impact.Events#PAUSE
 * @since 3.0.0
 */
module.exports = 'pause';
