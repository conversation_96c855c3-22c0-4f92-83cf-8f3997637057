/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2020 Photon Storm Ltd.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * @namespace Phaser.Physics.Impact.Components
 */

module.exports = {

    Acceleration: require('./Acceleration'),
    BodyScale: require('./BodyScale'),
    BodyType: require('./BodyType'),
    Bounce: require('./Bounce'),
    CheckAgainst: require('./CheckAgainst'),
    Collides: require('./Collides'),
    Debug: require('./Debug'),
    Friction: require('./Friction'),
    Gravity: require('./Gravity'),
    Offset: require('./Offset'),
    SetGameObject: require('./SetGameObject'),
    Velocity: require('./Velocity')

};
