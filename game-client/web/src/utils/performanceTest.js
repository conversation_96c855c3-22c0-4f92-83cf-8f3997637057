// 性能测试工具

/**
 * 测试组件渲染性能
 * @param {Function} renderFunction - 渲染函数
 * @param {number} iterations - 测试次数
 * @returns {Object} 性能测试结果
 */
export function testRenderPerformance(renderFunction, iterations = 100) {
  const times = [];
  
  for (let i = 0; i < iterations; i++) {
    const startTime = performance.now();
    renderFunction();
    const endTime = performance.now();
    times.push(endTime - startTime);
  }
  
  const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
  const minTime = Math.min(...times);
  const maxTime = Math.max(...times);
  
  return {
    average: avgTime.toFixed(2),
    min: minTime.toFixed(2),
    max: maxTime.toFixed(2),
    total: times.reduce((sum, time) => sum + time, 0).toFixed(2),
    iterations
  };
}

/**
 * 测试动画性能
 * @param {HTMLElement} element - 要测试的元素
 * @param {number} duration - 测试持续时间（毫秒）
 * @returns {Promise<Object>} 动画性能测试结果
 */
export function testAnimationPerformance(element, duration = 5000) {
  return new Promise((resolve) => {
    let frameCount = 0;
    let startTime = performance.now();
    let lastFrameTime = startTime;
    const frameTimes = [];
    
    function countFrame() {
      const currentTime = performance.now();
      const frameTime = currentTime - lastFrameTime;
      frameTimes.push(frameTime);
      frameCount++;
      lastFrameTime = currentTime;
      
      if (currentTime - startTime < duration) {
        requestAnimationFrame(countFrame);
      } else {
        const totalTime = currentTime - startTime;
        const fps = (frameCount / totalTime) * 1000;
        const avgFrameTime = frameTimes.reduce((sum, time) => sum + time, 0) / frameTimes.length;
        
        resolve({
          fps: fps.toFixed(2),
          frameCount,
          totalTime: totalTime.toFixed(2),
          avgFrameTime: avgFrameTime.toFixed(2),
          droppedFrames: frameTimes.filter(time => time > 16.67).length // 60fps = 16.67ms per frame
        });
      }
    }
    
    requestAnimationFrame(countFrame);
  });
}

/**
 * 测试内存使用情况
 * @returns {Object} 内存使用信息
 */
export function getMemoryUsage() {
  if (performance.memory) {
    return {
      usedJSHeapSize: (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2) + ' MB',
      totalJSHeapSize: (performance.memory.totalJSHeapSize / 1024 / 1024).toFixed(2) + ' MB',
      jsHeapSizeLimit: (performance.memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2) + ' MB'
    };
  }
  return { message: 'Memory API not supported' };
}

/**
 * 测试图片加载性能
 * @param {string[]} imageUrls - 图片URL数组
 * @returns {Promise<Object>} 图片加载性能结果
 */
export function testImageLoadPerformance(imageUrls) {
  const startTime = performance.now();
  const loadPromises = imageUrls.map(url => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const imageStartTime = performance.now();
      
      img.onload = () => {
        resolve({
          url,
          loadTime: performance.now() - imageStartTime,
          success: true
        });
      };
      
      img.onerror = () => {
        resolve({
          url,
          loadTime: performance.now() - imageStartTime,
          success: false
        });
      };
      
      img.src = url;
    });
  });
  
  return Promise.all(loadPromises).then(results => {
    const totalTime = performance.now() - startTime;
    const successCount = results.filter(r => r.success).length;
    const avgLoadTime = results.reduce((sum, r) => sum + r.loadTime, 0) / results.length;
    
    return {
      totalTime: totalTime.toFixed(2),
      successCount,
      totalCount: results.length,
      successRate: ((successCount / results.length) * 100).toFixed(2) + '%',
      avgLoadTime: avgLoadTime.toFixed(2),
      results
    };
  });
}

/**
 * 运行完整的性能测试套件
 * @returns {Promise<Object>} 完整的性能测试结果
 */
export async function runPerformanceTestSuite() {
  console.log('开始性能测试...');
  
  const results = {
    timestamp: new Date().toISOString(),
    memory: getMemoryUsage(),
    tests: {}
  };
  
  // 测试UI素材加载性能
  const uiAssets = [
    '/resources/table_bg_1.jpg',
    '/resources/UI/card/card.png',
    '/resources/UI/button/btn_chupai.png',
    '/resources/UI/button/btn_bujiao.png',
    '/resources/UI/headimage/avatar_1.png',
    '/resources/UI/headimage/img_Card_dizhu.png'
  ];
  
  try {
    results.tests.imageLoading = await testImageLoadPerformance(uiAssets);
    console.log('图片加载测试完成');
  } catch (error) {
    results.tests.imageLoading = { error: error.message };
    console.error('图片加载测试失败:', error);
  }
  
  // 测试DOM操作性能
  try {
    const domTestResult = testRenderPerformance(() => {
      const div = document.createElement('div');
      div.className = 'test-element';
      div.innerHTML = '<span>Test</span>';
      document.body.appendChild(div);
      document.body.removeChild(div);
    }, 50);
    
    results.tests.domOperations = domTestResult;
    console.log('DOM操作测试完成');
  } catch (error) {
    results.tests.domOperations = { error: error.message };
    console.error('DOM操作测试失败:', error);
  }
  
  console.log('性能测试完成:', results);
  return results;
}

/**
 * 在控制台输出性能测试结果
 * @param {Object} results - 性能测试结果
 */
export function logPerformanceResults(results) {
  console.group('🚀 性能测试结果');
  
  console.group('💾 内存使用');
  console.table(results.memory);
  console.groupEnd();
  
  if (results.tests.imageLoading) {
    console.group('🖼️ 图片加载性能');
    const { successCount, totalCount, successRate, avgLoadTime, totalTime } = results.tests.imageLoading;
    console.log(`成功加载: ${successCount}/${totalCount} (${successRate})`);
    console.log(`平均加载时间: ${avgLoadTime}ms`);
    console.log(`总加载时间: ${totalTime}ms`);
    console.groupEnd();
  }
  
  if (results.tests.domOperations) {
    console.group('🔧 DOM操作性能');
    const { average, min, max, iterations } = results.tests.domOperations;
    console.log(`平均时间: ${average}ms`);
    console.log(`最快时间: ${min}ms`);
    console.log(`最慢时间: ${max}ms`);
    console.log(`测试次数: ${iterations}`);
    console.groupEnd();
  }
  
  console.groupEnd();
}
