// 卡牌映射工具 - 将游戏逻辑中的卡牌映射到雪碧图中的位置

/**
 * 卡牌雪碧图映射表
 * 根据plist文件中的信息，每张卡牌在雪碧图中的位置
 */
const CARD_SPRITE_MAP = {
  // 黑桃 (Spades) - card_1 到 card_13
  'spade_3': { frame: '{{238,646},{116,159}}', cardId: 'card_1' },
  'spade_4': { frame: '{{120,646},{116,159}}', cardId: 'card_10' },
  'spade_5': { frame: '{{2,646},{116,159}}', cardId: 'card_11' },
  'spade_6': { frame: '{{1418,485},{116,159}}', cardId: 'card_12' },
  'spade_7': { frame: '{{1300,485},{116,159}}', cardId: 'card_13' },
  'spade_8': { frame: '{{1182,485},{116,159}}', cardId: 'card_14' },
  'spade_9': { frame: '{{1064,485},{116,159}}', cardId: 'card_15' },
  'spade_10': { frame: '{{946,485},{116,159}}', cardId: 'card_16' },
  'spade_11': { frame: '{{828,485},{116,159}}', cardId: 'card_17' }, // J
  'spade_12': { frame: '{{710,485},{116,159}}', cardId: 'card_18' }, // Q
  'spade_13': { frame: '{{592,485},{116,159}}', cardId: 'card_19' }, // K
  'spade_14': { frame: '{{474,485},{116,159}}', cardId: 'card_2' },  // A
  'spade_15': { frame: '{{356,485},{116,159}}', cardId: 'card_20' }, // 2

  // 红桃 (Hearts) - card_21 到 card_33
  'heart_3': { frame: '{{238,485},{116,159}}', cardId: 'card_21' },
  'heart_4': { frame: '{{120,485},{116,159}}', cardId: 'card_22' },
  'heart_5': { frame: '{{2,485},{116,159}}', cardId: 'card_23' },
  'heart_6': { frame: '{{1418,324},{116,159}}', cardId: 'card_24' },
  'heart_7': { frame: '{{1300,324},{116,159}}', cardId: 'card_25' },
  'heart_8': { frame: '{{1182,324},{116,159}}', cardId: 'card_26' },
  'heart_9': { frame: '{{1064,324},{116,159}}', cardId: 'card_27' },
  'heart_10': { frame: '{{946,324},{116,159}}', cardId: 'card_28' },
  'heart_11': { frame: '{{828,324},{116,159}}', cardId: 'card_29' }, // J
  'heart_12': { frame: '{{710,324},{116,159}}', cardId: 'card_3' },  // Q
  'heart_13': { frame: '{{592,324},{116,159}}', cardId: 'card_30' }, // K
  'heart_14': { frame: '{{474,324},{116,159}}', cardId: 'card_31' }, // A
  'heart_15': { frame: '{{356,324},{116,159}}', cardId: 'card_32' }, // 2

  // 梅花 (Clubs) - card_34 到 card_46
  'club_3': { frame: '{{238,324},{116,159}}', cardId: 'card_33' },
  'club_4': { frame: '{{120,324},{116,159}}', cardId: 'card_34' },
  'club_5': { frame: '{{2,324},{116,159}}', cardId: 'card_35' },
  'club_6': { frame: '{{1418,163},{116,159}}', cardId: 'card_36' },
  'club_7': { frame: '{{1300,163},{116,159}}', cardId: 'card_37' },
  'club_8': { frame: '{{1182,163},{116,159}}', cardId: 'card_38' },
  'club_9': { frame: '{{1064,163},{116,159}}', cardId: 'card_39' },
  'club_10': { frame: '{{946,163},{116,159}}', cardId: 'card_4' },
  'club_11': { frame: '{{828,163},{116,159}}', cardId: 'card_40' }, // J
  'club_12': { frame: '{{710,163},{116,159}}', cardId: 'card_41' }, // Q
  'club_13': { frame: '{{592,163},{116,159}}', cardId: 'card_42' }, // K
  'club_14': { frame: '{{474,163},{116,159}}', cardId: 'card_43' }, // A
  'club_15': { frame: '{{356,163},{116,159}}', cardId: 'card_44' }, // 2

  // 方块 (Diamonds) - card_47 到 card_52 + 王牌
  'diamond_3': { frame: '{{238,163},{116,159}}', cardId: 'card_45' },
  'diamond_4': { frame: '{{120,163},{116,159}}', cardId: 'card_46' },
  'diamond_5': { frame: '{{2,163},{116,159}}', cardId: 'card_47' },
  'diamond_6': { frame: '{{1418,2},{116,159}}', cardId: 'card_48' },
  'diamond_7': { frame: '{{1300,2},{116,159}}', cardId: 'card_49' },
  'diamond_8': { frame: '{{1182,2},{116,159}}', cardId: 'card_5' },
  'diamond_9': { frame: '{{1064,2},{116,159}}', cardId: 'card_50' },
  'diamond_10': { frame: '{{946,2},{116,159}}', cardId: 'card_51' },
  'diamond_11': { frame: '{{828,2},{116,159}}', cardId: 'card_52' }, // J
  'diamond_12': { frame: '{{710,2},{116,159}}', cardId: 'card_53' }, // Q
  'diamond_13': { frame: '{{592,2},{116,159}}', cardId: 'card_54' }, // K
  'diamond_14': { frame: '{{474,2},{116,159}}', cardId: 'card_6' },  // A
  'diamond_15': { frame: '{{356,2},{116,159}}', cardId: 'card_7' },  // 2

  // 王牌
  'joker_16': { frame: '{{238,2},{116,159}}', cardId: 'card_8' },  // 小王
  'joker_17': { frame: '{{120,2},{116,159}}', cardId: 'card_9' },  // 大王
};

/**
 * 解析frame字符串，获取x, y, width, height
 * @param {string} frameStr - 格式如 "{{238,646},{116,159}}"
 * @returns {object} - {x, y, width, height}
 */
function parseFrame(frameStr) {
  const match = frameStr.match(/\{\{(\d+),(\d+)\},\{(\d+),(\d+)\}\}/);
  if (!match) return null;
  
  return {
    x: parseInt(match[1]),
    y: parseInt(match[2]),
    width: parseInt(match[3]),
    height: parseInt(match[4])
  };
}

/**
 * 根据卡牌信息获取雪碧图位置
 * @param {object} card - 卡牌对象 {type, value}
 * @returns {object} - {x, y, width, height} 或 null
 */
export function getCardSpritePosition(card) {
  if (!card || !card.type || !card.value) return null;
  
  let key;
  if (card.value === 16 || card.value === 17) {
    // 王牌
    key = `joker_${card.value}`;
  } else {
    // 普通牌
    key = `${card.type}_${card.value}`;
  }
  
  const spriteInfo = CARD_SPRITE_MAP[key];
  if (!spriteInfo) return null;
  
  return parseFrame(spriteInfo.frame);
}

/**
 * 获取卡牌背面的雪碧图位置（假设有卡背图片）
 * @returns {object} - {x, y, width, height}
 */
export function getCardBackSpritePosition() {
  // 假设卡背是雪碧图中的最后一张，或者使用默认位置
  // 这里需要根据实际的雪碧图内容调整
  return {
    x: 2,
    y: 2,
    width: 116,
    height: 159
  };
}
