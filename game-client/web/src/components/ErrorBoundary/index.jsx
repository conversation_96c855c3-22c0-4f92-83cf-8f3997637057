import React from 'react';
import { But<PERSON>, Result } from 'antd';
import styles from './index.module.less';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null 
    };
  }

  static getDerivedStateFromError(error) {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // 你同样可以将错误日志上报给服务器
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // 可以在这里添加错误上报逻辑
    this.reportError(error, errorInfo);
  }

  reportError = (error, errorInfo) => {
    // 错误上报逻辑
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    console.log('Error Report:', errorReport);
    
    // 这里可以发送到错误监控服务
    // fetch('/api/error-report', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(errorReport)
    // });
  };

  handleReload = () => {
    window.location.reload();
  };

  handleReset = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null 
    });
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className={styles.errorBoundary}>
          <Result
            status="error"
            title="游戏遇到了一个错误"
            subTitle="抱歉，游戏运行时出现了问题。您可以尝试重新加载页面或重置游戏状态。"
            extra={[
              <Button type="primary" key="reload" onClick={this.handleReload}>
                重新加载页面
              </Button>,
              <Button key="reset" onClick={this.handleReset}>
                重置游戏状态
              </Button>,
            ]}
          />
          
          {/* 开发环境下显示详细错误信息 */}
          {process.env.NODE_ENV === 'development' && (
            <div className={styles.errorDetails}>
              <h3>错误详情 (仅开发环境显示)</h3>
              <div className={styles.errorMessage}>
                <strong>错误消息:</strong>
                <pre>{this.state.error && this.state.error.toString()}</pre>
              </div>
              <div className={styles.errorStack}>
                <strong>错误堆栈:</strong>
                <pre>{this.state.errorInfo.componentStack}</pre>
              </div>
            </div>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

// 函数式错误边界组件（用于特定场景）
export function SimpleErrorBoundary({ children, fallback }) {
  return (
    <ErrorBoundary>
      {children}
    </ErrorBoundary>
  );
}

// 高阶组件形式的错误边界
export function withErrorBoundary(Component, errorFallback) {
  return function WrappedComponent(props) {
    return (
      <ErrorBoundary>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
}

export default ErrorBoundary;
