.errorBoundary {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #1a6e1a 0%, #0d3509 100%);
  padding: 20px;
  color: white;

  // 覆盖 antd Result 组件的样式
  :global(.ant-result) {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 15px;
    border: 2px solid rgba(255, 215, 0, 0.5);
    padding: 40px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    
    :global(.ant-result-title) {
      color: #ffd700 !important;
      font-size: 24px;
      font-weight: bold;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    }
    
    :global(.ant-result-subtitle) {
      color: #fff !important;
      font-size: 16px;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    }
    
    :global(.ant-result-icon) {
      :global(.anticon) {
        color: #ff4757 !important;
        filter: drop-shadow(0 4px 8px rgba(255, 71, 87, 0.5));
      }
    }
    
    :global(.ant-result-extra) {
      margin-top: 30px;
      
      :global(.ant-btn) {
        margin: 0 8px;
        height: 40px;
        padding: 0 20px;
        font-size: 14px;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
        
        &:global(.ant-btn-primary) {
          background: linear-gradient(135deg, #ffd700 0%, #ffb700 100%);
          border: 2px solid #ffd700;
          color: #333;
          
          &:hover {
            background: linear-gradient(135deg, #ffed4e 0%, #ffd700 100%);
            border-color: #ffed4e;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 215, 0, 0.4);
          }
        }
        
        &:global(.ant-btn-default) {
          background: rgba(255, 255, 255, 0.1);
          border: 2px solid rgba(255, 255, 255, 0.3);
          color: #fff;
          
          &:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 255, 255, 0.2);
          }
        }
      }
    }
  }
}

.errorDetails {
  margin-top: 30px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  border: 1px solid rgba(255, 71, 87, 0.5);
  max-width: 800px;
  width: 100%;
  
  h3 {
    color: #ff4757;
    font-size: 18px;
    margin-bottom: 15px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  }
  
  .errorMessage,
  .errorStack {
    margin-bottom: 20px;
    
    strong {
      display: block;
      color: #ffd700;
      font-size: 14px;
      margin-bottom: 8px;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    }
    
    pre {
      background: rgba(0, 0, 0, 0.7);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 6px;
      padding: 12px;
      color: #fff;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
      overflow-x: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
      max-height: 200px;
      overflow-y: auto;
      
      // 自定义滚动条
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: rgba(255, 215, 0, 0.5);
        border-radius: 3px;
        
        &:hover {
          background: rgba(255, 215, 0, 0.7);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .errorBoundary {
    padding: 15px;
    
    :global(.ant-result) {
      padding: 20px;
      
      :global(.ant-result-title) {
        font-size: 20px;
      }
      
      :global(.ant-result-subtitle) {
        font-size: 14px;
      }
      
      :global(.ant-result-extra) {
        margin-top: 20px;
        
        :global(.ant-btn) {
          display: block;
          width: 100%;
          margin: 8px 0;
        }
      }
    }
  }
  
  .errorDetails {
    padding: 15px;
    
    h3 {
      font-size: 16px;
    }
    
    .errorMessage,
    .errorStack {
      pre {
        font-size: 11px;
        padding: 10px;
        max-height: 150px;
      }
    }
  }
}
