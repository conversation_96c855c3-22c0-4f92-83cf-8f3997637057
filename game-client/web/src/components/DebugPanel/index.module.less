.debugToggle {
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 9999;
  
  .toggleButton {
    background: rgba(255, 215, 0, 0.9);
    border: 2px solid #ffd700;
    color: #333;
    font-weight: bold;
    
    &:hover {
      background: rgba(255, 215, 0, 1);
      border-color: #ffed4e;
    }
  }
}

.debugPanel {
  position: fixed;
  top: 10px;
  right: 10px;
  width: 400px;
  max-height: 80vh;
  background: rgba(0, 0, 0, 0.9);
  border: 2px solid rgba(255, 215, 0, 0.8);
  border-radius: 10px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  z-index: 9999;
  overflow: hidden;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(255, 215, 0, 0.1);
    border-bottom: 1px solid rgba(255, 215, 0, 0.3);
    
    h3 {
      margin: 0;
      color: #ffd700;
      font-size: 16px;
      font-weight: bold;
    }
    
    .controls {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: #fff;
      
      span {
        white-space: nowrap;
      }
    }
  }
  
  // 覆盖 antd Collapse 样式
  :global(.ant-collapse) {
    background: transparent;
    border: none;
    
    :global(.ant-collapse-item) {
      border-bottom: 1px solid rgba(255, 215, 0, 0.2);
      
      &:last-child {
        border-bottom: none;
      }
      
      :global(.ant-collapse-header) {
        background: rgba(255, 215, 0, 0.05);
        color: #ffd700 !important;
        font-weight: 600;
        padding: 8px 16px;
        
        &:hover {
          background: rgba(255, 215, 0, 0.1);
        }
      }
      
      :global(.ant-collapse-content) {
        background: transparent;
        border-top: none;
        
        :global(.ant-collapse-content-box) {
          padding: 12px 16px;
        }
      }
    }
  }
}

.infoGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 12px;
}

.infoItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  label {
    color: #ffd700;
    font-size: 12px;
    font-weight: 600;
  }
  
  .value {
    color: #fff;
    font-size: 12px;
    font-family: monospace;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
  }
}

.actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.playersInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.playerItem {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: 6px;
  padding: 8px;
  
  .playerHeader {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    
    .playerIndex {
      color: #ffd700;
      font-weight: 600;
      font-size: 12px;
    }
    
    .landlordBadge {
      background: rgba(139, 0, 0, 0.8);
      color: #ffd700;
      padding: 1px 6px;
      border-radius: 3px;
      font-size: 10px;
      font-weight: bold;
    }
    
    .currentUserBadge {
      background: rgba(0, 139, 0, 0.8);
      color: #fff;
      padding: 1px 6px;
      border-radius: 3px;
      font-size: 10px;
      font-weight: bold;
    }
  }
  
  .playerDetails {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #fff;
    font-size: 11px;
  }
}

.lastHand {
  color: #fff;
  font-size: 12px;
  
  .cards {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 8px;
    
    .card {
      background: rgba(255, 215, 0, 0.2);
      border: 1px solid rgba(255, 215, 0, 0.5);
      border-radius: 3px;
      padding: 2px 6px;
      font-size: 10px;
      font-family: monospace;
    }
  }
}

.testingTools {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

// 响应式设计
@media (max-width: 768px) {
  .debugPanel {
    width: calc(100vw - 20px);
    max-width: 350px;
    max-height: 70vh;
    
    .header {
      padding: 8px 12px;
      
      h3 {
        font-size: 14px;
      }
      
      .controls {
        gap: 6px;
        font-size: 11px;
      }
    }
    
    :global(.ant-collapse-content-box) {
      padding: 8px 12px !important;
    }
  }
  
  .infoGrid {
    grid-template-columns: 1fr;
    gap: 6px;
  }
  
  .infoItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }
  
  .actions {
    gap: 6px;
  }
  
  .testingTools {
    gap: 6px;
  }
}
