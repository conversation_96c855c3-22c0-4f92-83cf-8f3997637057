import { useState, useEffect } from 'react';
import { Button, Collapse, Switch, InputNumber, Select } from 'antd';
import styles from './index.module.less';

const { Panel } = Collapse;
const { Option } = Select;

export function DebugPanel({ gameManager, visible = false, onToggle }) {
  const [debugInfo, setDebugInfo] = useState({});
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(1000);

  useEffect(() => {
    if (!autoRefresh || !visible) return;

    const interval = setInterval(() => {
      updateDebugInfo();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, visible, gameManager]);

  const updateDebugInfo = () => {
    if (!gameManager) return;

    setDebugInfo({
      gameState: gameManager.currentState,
      currentPlayer: gameManager.currentPlayer,
      players: gameManager.players?.map(p => ({
        index: p.index,
        isLandlord: p.isLandlord,
        cardCount: p.cards?.length || 0,
        isCurrentUser: p.isCurrentUser
      })) || [],
      lastHand: gameManager.lastHand,
      remainingCards: gameManager.remainingCards?.length || 0,
      timestamp: new Date().toLocaleTimeString()
    });
  };

  const handleForceUpdate = () => {
    updateDebugInfo();
  };

  const handleSimulateError = () => {
    throw new Error('这是一个模拟的错误，用于测试错误边界');
  };

  const handleChangeGameState = (newState) => {
    if (gameManager && gameManager.setState) {
      gameManager.setState(newState);
      updateDebugInfo();
    }
  };

  const handleAddCards = (playerIndex, count) => {
    if (gameManager && gameManager.players && gameManager.players[playerIndex]) {
      const player = gameManager.players[playerIndex];
      // 模拟添加卡牌
      for (let i = 0; i < count; i++) {
        player.cards.push({ type: 'spade', value: 3 + i });
      }
      updateDebugInfo();
    }
  };

  if (!visible) {
    return (
      <div className={styles.debugToggle}>
        <Button 
          type="primary" 
          size="small" 
          onClick={onToggle}
          className={styles.toggleButton}
        >
          调试
        </Button>
      </div>
    );
  }

  return (
    <div className={styles.debugPanel}>
      <div className={styles.header}>
        <h3>调试面板</h3>
        <div className={styles.controls}>
          <Switch 
            checked={autoRefresh}
            onChange={setAutoRefresh}
            size="small"
          />
          <span>自动刷新</span>
          <InputNumber
            min={500}
            max={5000}
            step={500}
            value={refreshInterval}
            onChange={setRefreshInterval}
            size="small"
            style={{ width: 80 }}
          />
          <span>ms</span>
          <Button size="small" onClick={handleForceUpdate}>
            刷新
          </Button>
          <Button size="small" onClick={onToggle}>
            关闭
          </Button>
        </div>
      </div>

      <Collapse defaultActiveKey={['gameState', 'players']} size="small">
        <Panel header="游戏状态" key="gameState">
          <div className={styles.infoGrid}>
            <div className={styles.infoItem}>
              <label>当前状态:</label>
              <span className={styles.value}>{debugInfo.gameState || 'N/A'}</span>
            </div>
            <div className={styles.infoItem}>
              <label>当前玩家:</label>
              <span className={styles.value}>{debugInfo.currentPlayer ?? 'N/A'}</span>
            </div>
            <div className={styles.infoItem}>
              <label>底牌数量:</label>
              <span className={styles.value}>{debugInfo.remainingCards}</span>
            </div>
            <div className={styles.infoItem}>
              <label>最后更新:</label>
              <span className={styles.value}>{debugInfo.timestamp}</span>
            </div>
          </div>
          
          <div className={styles.actions}>
            <Select
              placeholder="切换游戏状态"
              style={{ width: 150 }}
              size="small"
              onChange={handleChangeGameState}
            >
              <Option value="WAITING">等待中</Option>
              <Option value="BIDDING">叫地主</Option>
              <Option value="PLAYING">游戏中</Option>
              <Option value="FINISHED">已结束</Option>
            </Select>
          </div>
        </Panel>

        <Panel header="玩家信息" key="players">
          <div className={styles.playersInfo}>
            {debugInfo.players?.map((player, index) => (
              <div key={index} className={styles.playerItem}>
                <div className={styles.playerHeader}>
                  <span className={styles.playerIndex}>玩家 {player.index}</span>
                  {player.isLandlord && <span className={styles.landlordBadge}>地主</span>}
                  {player.isCurrentUser && <span className={styles.currentUserBadge}>当前用户</span>}
                </div>
                <div className={styles.playerDetails}>
                  <span>手牌: {player.cardCount} 张</span>
                  <Button 
                    size="small" 
                    onClick={() => handleAddCards(player.index, 1)}
                  >
                    +1张牌
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </Panel>

        <Panel header="最后出牌" key="lastHand">
          <div className={styles.lastHand}>
            {debugInfo.lastHand ? (
              <div>
                <p>牌型: {debugInfo.lastHand.length} 张牌</p>
                <div className={styles.cards}>
                  {debugInfo.lastHand.map((card, index) => (
                    <span key={index} className={styles.card}>
                      {card.type} {card.value}
                    </span>
                  ))}
                </div>
              </div>
            ) : (
              <p>暂无出牌</p>
            )}
          </div>
        </Panel>

        <Panel header="测试工具" key="testing">
          <div className={styles.testingTools}>
            <Button 
              danger 
              size="small" 
              onClick={handleSimulateError}
            >
              模拟错误
            </Button>
            <Button 
              size="small" 
              onClick={() => console.log('GameManager:', gameManager)}
            >
              输出到控制台
            </Button>
            <Button 
              size="small" 
              onClick={() => localStorage.clear()}
            >
              清除本地存储
            </Button>
          </div>
        </Panel>
      </Collapse>
    </div>
  );
}
