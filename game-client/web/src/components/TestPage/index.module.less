.testPage {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: linear-gradient(135deg, #1a6e1a 0%, #0d3509 100%);
  min-height: 100vh;
  color: white;
}

.title {
  text-align: center;
  color: #ffd700;
  font-size: 28px;
  margin-bottom: 30px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.controlPanel {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
  
  button {
    padding: 8px 16px;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.9) 0%, rgba(255, 193, 7, 0.9) 100%);
    border: 2px solid rgba(255, 215, 0, 1);
    border-radius: 8px;
    color: #333;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: linear-gradient(135deg, rgba(255, 215, 0, 1) 0%, rgba(255, 193, 7, 1) 100%);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(255, 215, 0, 0.4);
    }
  }
}

.section {
  margin-bottom: 40px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  border: 2px solid rgba(255, 215, 0, 0.3);
  
  h2 {
    color: #ffd700;
    font-size: 20px;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  }
}

.playerInfoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  justify-items: center;
}

.playerInfoContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.cardGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 15px;
  justify-items: center;
  max-width: 800px;
  margin: 0 auto;
}

.cardContainer {
  width: 80px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 8px 16px rgba(255, 215, 0, 0.4);
  }
}

.responsiveInfo {
  margin-top: 40px;
  padding: 20px;
  background: rgba(139, 0, 0, 0.3);
  border-radius: 15px;
  border: 2px solid rgba(255, 215, 0, 0.5);
  
  h3 {
    color: #ffd700;
    font-size: 18px;
    margin-bottom: 15px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  }
  
  p {
    margin-bottom: 10px;
    color: #fff;
  }
  
  ul {
    list-style: none;
    padding: 0;
    
    li {
      padding: 5px 0;
      color: #ffd700;
      
      &::before {
        content: '• ';
        color: #ffd700;
        font-weight: bold;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .testPage {
    padding: 15px;
  }
  
  .title {
    font-size: 24px;
    margin-bottom: 20px;
  }
  
  .controlPanel {
    gap: 10px;
    margin-bottom: 30px;
    
    button {
      padding: 6px 12px;
      font-size: 14px;
    }
  }
  
  .section {
    padding: 15px;
    margin-bottom: 30px;
    
    h2 {
      font-size: 18px;
      margin-bottom: 15px;
    }
  }
  
  .playerInfoGrid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .cardGrid {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 10px;
  }
  
  .cardContainer {
    width: 60px;
    height: 90px;
  }
  
  .responsiveInfo {
    padding: 15px;
    
    h3 {
      font-size: 16px;
      margin-bottom: 10px;
    }
  }
}
