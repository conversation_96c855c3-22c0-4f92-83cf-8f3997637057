import { useState } from 'react';
import { But<PERSON>, message } from 'antd';
import { GameStatus, PlayerIndicator, GameToast } from '../GameStatus';
import { PlayerInfo } from '../PlayerInfo';
import { LandlordSelection } from '../LandlordSelection';
import { CardContent } from '../CardDisplay/CardContent';
import { runPerformanceTestSuite, logPerformanceResults } from '../../utils/performanceTest';
import styles from './index.module.less';

export function TestPage() {
  const [showLandlordSelection, setShowLandlordSelection] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [gamePhase, setGamePhase] = useState('waiting');
  const [currentPlayer, setCurrentPlayer] = useState(0);

  // 测试数据
  const testPlayers = [
    { index: 0, isCurrentUser: true, isLandlord: false, cards: Array(13).fill(null) },
    { index: 1, isCurrentUser: false, isLandlord: true, cards: Array(17).fill(null) },
    { index: 2, isCurrentUser: false, isLandlord: false, cards: Array(17).fill(null) }
  ];

  const testCards = [
    { type: 'spade', value: 14 }, // A
    { type: 'heart', value: 13 }, // K
    { type: 'diamond', value: 12 }, // Q
    { type: 'club', value: 11 }, // J
    { type: 'spade', value: 10 },
    { type: 'joker', value: 16 }, // 小王
    { type: 'joker', value: 17 }, // 大王
  ];

  const handleBidConfirm = (score) => {
    console.log('叫地主:', score);
    setShowLandlordSelection(false);
  };

  const handleBidPass = () => {
    console.log('不叫');
    setShowLandlordSelection(false);
  };

  const cycleGamePhase = () => {
    const phases = ['waiting', 'bidding', 'playing'];
    const currentIndex = phases.indexOf(gamePhase);
    const nextIndex = (currentIndex + 1) % phases.length;
    setGamePhase(phases[nextIndex]);
  };

  const cycleCurrentPlayer = () => {
    setCurrentPlayer((prev) => (prev + 1) % 3);
  };

  const runPerformanceTest = async () => {
    message.loading('正在运行性能测试...', 0);
    try {
      const results = await runPerformanceTestSuite();
      logPerformanceResults(results);
      message.destroy();
      message.success('性能测试完成，请查看控制台输出');
    } catch (error) {
      message.destroy();
      message.error('性能测试失败: ' + error.message);
      console.error('Performance test error:', error);
    }
  };

  return (
    <div className={styles.testPage}>
      <h1 className={styles.title}>斗地主UI组件测试页面</h1>
      
      {/* 控制面板 */}
      <div className={styles.controlPanel}>
        <Button onClick={cycleGamePhase}>
          切换游戏阶段: {gamePhase}
        </Button>
        <Button onClick={cycleCurrentPlayer}>
          切换当前玩家: {currentPlayer}
        </Button>
        <Button onClick={() => setShowLandlordSelection(true)}>
          显示叫地主界面
        </Button>
        <Button onClick={() => setShowToast(true)}>
          显示提示消息
        </Button>
        <Button onClick={runPerformanceTest}>
          运行性能测试
        </Button>
      </div>

      {/* GameStatus 组件测试 */}
      <div className={styles.section}>
        <h2>游戏状态组件</h2>
        <GameStatus 
          currentPhase={gamePhase}
          currentPlayerIndex={currentPlayer}
          players={testPlayers}
          timeLeft={15}
          gameMessage={gamePhase === 'waiting' ? '等待玩家准备...' : ''}
        />
      </div>

      {/* PlayerInfo 组件测试 */}
      <div className={styles.section}>
        <h2>玩家信息组件</h2>
        <div className={styles.playerInfoGrid}>
          {testPlayers.map((player, index) => (
            <div key={index} className={styles.playerInfoContainer}>
              <PlayerInfo 
                player={player}
                isCurrentUser={player.isCurrentUser}
                isLandlord={player.isLandlord}
                remainingCardsCount={player.cards.length}
                position={index === 0 ? 'bottom' : index === 1 ? 'left' : 'right'}
              />
              <PlayerIndicator 
                isActive={currentPlayer === index}
                isLandlord={player.isLandlord}
                position="bottom"
              />
            </div>
          ))}
        </div>
      </div>

      {/* CardContent 组件测试 */}
      <div className={styles.section}>
        <h2>卡牌显示组件</h2>
        <div className={styles.cardGrid}>
          {testCards.map((card, index) => (
            <div key={index} className={styles.cardContainer}>
              <CardContent card={card} />
            </div>
          ))}
          <div className={styles.cardContainer}>
            <CardContent isBack={true} />
          </div>
        </div>
      </div>

      {/* 叫地主界面测试 */}
      <LandlordSelection 
        visible={showLandlordSelection}
        bidValue={1}
        onConfirm={handleBidConfirm}
        onPass={handleBidPass}
      />

      {/* 提示消息测试 */}
      {showToast && (
        <GameToast 
          message="这是一条测试消息"
          type="success"
          duration={3000}
          onClose={() => setShowToast(false)}
        />
      )}

      {/* 响应式测试提示 */}
      <div className={styles.responsiveInfo}>
        <h3>响应式测试</h3>
        <p>请调整浏览器窗口大小来测试响应式布局：</p>
        <ul>
          <li>桌面端: > 1024px</li>
          <li>平板端: 768px - 1024px</li>
          <li>手机端: < 768px</li>
        </ul>
      </div>
    </div>
  );
}
