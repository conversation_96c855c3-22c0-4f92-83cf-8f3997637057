import { Modal } from 'antd';
import styles from './index.module.less';

export function LandlordSelection({ visible, bidValue, onConfirm, onPass }) {
  return (
    <Modal
      title={null}
      visible={visible}
      footer={null}
      closable={false}
      centered
      width={500}
      className={styles.landlordModal}
    >
      <div className={styles.container}>
        {/* 标题区域 */}
        <div className={styles.title}>
          <img
            src="/resources/UI/headimage/img_Card_dizhu.png"
            alt="地主"
            className={styles.titleIcon}
          />
          <span className={styles.titleText}>叫地主</span>
        </div>

        {/* 当前叫分信息 */}
        <div className={styles.bidInfo}>
          <span className={styles.bidLabel}>当前叫分:</span>
          <span className={styles.bidValue}>{bidValue || '暂无'}</span>
        </div>

        {/* 按钮组 */}
        <div className={styles.buttonGroup}>
          {/* 叫分按钮 */}
          <div className={styles.bidButtons}>
            {[1, 2, 3].map(score => (
              <button
                key={score}
                onClick={() => onConfirm(score)}
                className={`${styles.actionButton} ${styles.bidButton}`}
                disabled={score <= bidValue}
              >
                <span className={styles.buttonText}>{score}分</span>
              </button>
            ))}
          </div>

          {/* 不叫按钮 */}
          <button
            onClick={onPass}
            className={`${styles.actionButton} ${styles.noBidButton}`}
          >
            <span className={styles.buttonText}>不叫</span>
          </button>
        </div>

        {/* 倒计时显示 */}
        <div className={styles.countdown}>
          <img
            src="/resources/UI/button/clock.png"
            alt="倒计时"
            className={styles.clockIcon}
          />
          <span className={styles.countdownText}>请选择...</span>
        </div>
      </div>
    </Modal>
  );
}
