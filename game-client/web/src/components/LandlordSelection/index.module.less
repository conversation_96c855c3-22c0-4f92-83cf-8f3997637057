// 叫地主模态框样式
.landlordModal {
  :global(.ant-modal-content) {
    background: linear-gradient(135deg, rgba(139, 0, 0, 0.95) 0%, rgba(75, 0, 0, 0.95) 100%);
    border: 3px solid rgba(255, 215, 0, 0.8);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
  }

  :global(.ant-modal-body) {
    padding: 0;
  }
}

.container {
  text-align: center;
  padding: 30px 20px;
  color: white;
}

// 标题区域
.title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 25px;

  .titleIcon {
    width: 40px;
    height: 40px;
    object-fit: contain;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));
  }

  .titleText {
    font-size: 28px;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  }
}

// 叫分信息
.bidInfo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 30px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  border: 2px solid rgba(255, 215, 0, 0.5);

  .bidLabel {
    font-size: 18px;
    color: #ffd700;
    font-weight: 600;
  }

  .bidValue {
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  }
}

// 按钮组
.buttonGroup {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.bidButtons {
  display: flex;
  gap: 15px;
  justify-content: center;
}

// 操作按钮基础样式
.actionButton {
  width: 120px;
  height: 60px;
  background-color: transparent;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  .buttonText {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    font-weight: bold;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    pointer-events: none;
  }

  &:hover:not(:disabled) {
    transform: scale(1.1) translateY(-3px);
    filter: brightness(1.2) drop-shadow(0 8px 16px rgba(255, 215, 0, 0.4));
  }

  &:active:not(:disabled) {
    transform: scale(1.05) translateY(-1px);
  }

  &:disabled {
    filter: grayscale(100%) brightness(0.5);
    cursor: not-allowed;
    transform: none;

    .buttonText {
      color: #999;
    }
  }
}

// 叫分按钮
.bidButton {
  background-image: url('/resources/UI/button/qiangzhuang.png');
}

// 不叫按钮
.noBidButton {
  background-image: url('/resources/UI/button/buqiangzhuang.png');
  width: 140px;
}

// 倒计时区域
.countdown {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 25px;
  padding: 10px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;

  .clockIcon {
    width: 24px;
    height: 24px;
    object-fit: contain;
    animation: clockTick 1s infinite;
  }

  .countdownText {
    font-size: 14px;
    color: #ffd700;
    font-weight: 600;
  }
}

// 时钟动画
@keyframes clockTick {
  0%, 100% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(5deg);
  }
}
