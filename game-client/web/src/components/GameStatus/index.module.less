// 游戏状态组件样式

.gameStatus {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  z-index: 100;
  pointer-events: none;
  
  > * {
    pointer-events: auto;
  }
}

// 游戏阶段指示器
.phaseIndicator {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
  border: 2px solid rgba(255, 215, 0, 0.6);
  border-radius: 25px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  
  .phaseIcon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 215, 0, 0.2);
    border: 2px solid rgba(255, 215, 0, 0.5);
    
    img {
      width: 20px;
      height: 20px;
      object-fit: contain;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
    }
    
    &.bidding {
      background: rgba(139, 0, 0, 0.3);
      border-color: rgba(255, 215, 0, 0.8);
      animation: pulseBid 2s infinite;
    }
    
    &.playing {
      background: rgba(0, 139, 0, 0.3);
      border-color: rgba(0, 255, 0, 0.8);
      animation: pulsePlay 2s infinite;
    }
    
    &.waiting {
      background: rgba(139, 139, 0, 0.3);
      border-color: rgba(255, 255, 0, 0.8);
      animation: pulseWait 2s infinite;
    }
  }
  
  .phaseText {
    font-size: 16px;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  }
}

// 当前玩家指示器
.currentPlayerIndicator {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 16px;
  background: rgba(255, 215, 0, 0.9);
  border: 2px solid rgba(255, 215, 0, 1);
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
  animation: playerIndicatorPulse 1.5s infinite;
  
  .playerArrow {
    width: 20px;
    height: 20px;
    animation: arrowBounce 1s infinite;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  
  .playerText {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
    
    strong {
      color: #8b0000;
    }
  }
}

// 倒计时显示
.countdown {
  .countdownCircle {
    position: relative;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
    border: 3px solid rgba(255, 215, 0, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    
    .clockIcon {
      position: absolute;
      top: 8px;
      left: 8px;
      width: 16px;
      height: 16px;
      object-fit: contain;
      animation: clockTick 1s infinite;
    }
    
    .countdownText {
      font-size: 18px;
      font-weight: bold;
      color: #ffd700;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    }
    
    &.urgent {
      border-color: #ff4757;
      animation: urgentPulse 0.5s infinite;
      
      .countdownText {
        color: #ff4757;
      }
    }
  }
}

// 游戏消息
.gameMessage {
  .messageContent {
    padding: 12px 20px;
    background: linear-gradient(135deg, rgba(139, 0, 0, 0.9) 0%, rgba(75, 0, 0, 0.9) 100%);
    border: 2px solid rgba(255, 215, 0, 0.8);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    animation: messageSlideIn 0.5s ease-out;
    
    .messageText {
      font-size: 16px;
      font-weight: 600;
      color: #ffd700;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    }
  }
}

// 游戏进度条
.gameProgress {
  width: 200px;
  
  .progressBar {
    width: 100%;
    height: 6px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
    overflow: hidden;
    border: 1px solid rgba(255, 215, 0, 0.5);
    
    .progressFill {
      height: 100%;
      background: linear-gradient(90deg, #ffd700 0%, #ff6b6b 100%);
      border-radius: 3px;
      transition: width 1s ease;
    }
  }
}

// 玩家指示器（显示在玩家头像附近）
.playerIndicator {
  position: absolute;
  z-index: 10;
  
  .indicatorArrow {
    width: 30px;
    height: 30px;
    animation: indicatorPulse 1.5s infinite;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.8));
    }
  }
  
  .landlordGlow {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    animation: landlordGlow 2s infinite;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  
  &.top {
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    
    .indicatorArrow {
      transform: rotate(180deg);
    }
  }
  
  &.bottom {
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
  }
  
  &.left {
    left: -40px;
    top: 50%;
    transform: translateY(-50%);
    
    .indicatorArrow {
      transform: rotate(90deg);
    }
  }
  
  &.right {
    right: -40px;
    top: 50%;
    transform: translateY(-50%);
    
    .indicatorArrow {
      transform: rotate(-90deg);
    }
  }
}

// 游戏提示消息
.gameToast {
  position: fixed;
  top: 100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  animation: toastSlideIn 0.3s ease-out;
  
  .toastContent {
    padding: 12px 20px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    
    .toastMessage {
      font-size: 14px;
      font-weight: 600;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    }
  }
  
  &.info {
    .toastContent {
      background: linear-gradient(135deg, rgba(0, 123, 255, 0.9) 0%, rgba(0, 86, 179, 0.9) 100%);
      border: 2px solid rgba(0, 123, 255, 1);
      
      .toastMessage {
        color: #fff;
      }
    }
  }
  
  &.success {
    .toastContent {
      background: linear-gradient(135deg, rgba(40, 167, 69, 0.9) 0%, rgba(25, 135, 84, 0.9) 100%);
      border: 2px solid rgba(40, 167, 69, 1);
      
      .toastMessage {
        color: #fff;
      }
    }
  }
  
  &.warning {
    .toastContent {
      background: linear-gradient(135deg, rgba(255, 193, 7, 0.9) 0%, rgba(255, 143, 0, 0.9) 100%);
      border: 2px solid rgba(255, 193, 7, 1);
      
      .toastMessage {
        color: #333;
      }
    }
  }
  
  &.error {
    .toastContent {
      background: linear-gradient(135deg, rgba(220, 53, 69, 0.9) 0%, rgba(176, 42, 55, 0.9) 100%);
      border: 2px solid rgba(220, 53, 69, 1);

      .toastMessage {
        color: #fff;
      }
    }
  }
}

// 动画定义
@keyframes pulseBid {
  0%, 100% {
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    transform: scale(1.05);
  }
}

@keyframes pulsePlay {
  0%, 100% {
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.8);
    transform: scale(1.05);
  }
}

@keyframes pulseWait {
  0%, 100% {
    box-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 255, 0, 0.8);
    transform: scale(1.05);
  }
}

@keyframes playerIndicatorPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.6);
  }
}

@keyframes arrowBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes clockTick {
  0%, 100% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(6deg);
  }
}

@keyframes urgentPulse {
  0%, 100% {
    border-color: #ff4757;
    box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
  }
  50% {
    border-color: #ff3742;
    box-shadow: 0 6px 25px rgba(255, 71, 87, 0.6);
  }
}

@keyframes messageSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes indicatorPulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.8));
  }
  50% {
    transform: scale(1.2);
    filter: drop-shadow(0 4px 8px rgba(255, 215, 0, 1));
  }
}

@keyframes landlordGlow {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.8));
  }
  50% {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 0 10px rgba(255, 215, 0, 1));
  }
}

@keyframes toastSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .gameStatus {
    top: 10px;
    gap: 10px;

    // 调整阶段指示器
    .phaseIndicator {
      padding: 8px 16px;
      gap: 8px;

      .phaseIcon {
        width: 28px;
        height: 28px;

        img {
          width: 16px;
          height: 16px;
        }
      }

      .phaseText {
        font-size: 14px;
      }
    }

    // 调整当前玩家指示器
    .currentPlayerIndicator {
      padding: 6px 12px;
      gap: 8px;

      .playerArrow {
        width: 16px;
        height: 16px;
      }

      .playerText {
        font-size: 12px;
      }
    }

    // 调整倒计时
    .countdown .countdownCircle {
      width: 50px;
      height: 50px;

      .clockIcon {
        width: 14px;
        height: 14px;
        top: 6px;
        left: 6px;
      }

      .countdownText {
        font-size: 16px;
      }
    }

    // 调整游戏消息
    .gameMessage .messageContent {
      padding: 8px 16px;

      .messageText {
        font-size: 14px;
      }
    }

    // 调整进度条
    .gameProgress {
      width: 150px;
    }
  }

  // 调整玩家指示器
  .playerIndicator {
    .indicatorArrow {
      width: 24px;
      height: 24px;
    }

    .landlordGlow {
      width: 16px;
      height: 16px;
      top: -3px;
      right: -3px;
    }

    &.top {
      top: -30px;
    }

    &.bottom {
      bottom: -30px;
    }

    &.left {
      left: -30px;
    }

    &.right {
      right: -30px;
    }
  }

  // 调整游戏提示
  .gameToast {
    top: 80px;

    .toastContent {
      padding: 8px 16px;

      .toastMessage {
        font-size: 12px;
      }
    }
  }
}
