import { useState, useEffect } from 'react';
import styles from './index.module.less';

export function GameStatus({ 
  currentPhase = 'waiting', // 'waiting', 'bidding', 'playing', 'finished'
  currentPlayerIndex = 0,
  players = [],
  timeLeft = 30,
  gameMessage = ''
}) {
  const [countdown, setCountdown] = useState(timeLeft);

  useEffect(() => {
    setCountdown(timeLeft);
  }, [timeLeft]);

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const getPhaseText = () => {
    switch (currentPhase) {
      case 'waiting':
        return '等待玩家准备';
      case 'bidding':
        return '叫地主阶段';
      case 'playing':
        return '游戏进行中';
      case 'finished':
        return '游戏结束';
      default:
        return '游戏状态';
    }
  };

  const getCurrentPlayerName = () => {
    const player = players[currentPlayerIndex];
    return player ? (player.isCurrentUser ? '你' : `玩家${currentPlayerIndex + 1}`) : '';
  };

  return (
    <div className={styles.gameStatus}>
      {/* 游戏阶段显示 */}
      <div className={styles.phaseIndicator}>
        <div className={`${styles.phaseIcon} ${styles[currentPhase]}`}>
          {currentPhase === 'bidding' && (
            <img src="/resources/UI/headimage/img_Card_dizhu.png" alt="叫地主" />
          )}
          {currentPhase === 'playing' && (
            <img src="/resources/UI/card/card.png" alt="出牌" />
          )}
          {currentPhase === 'waiting' && (
            <img src="/resources/UI/pm_ready.png" alt="准备" />
          )}
        </div>
        <span className={styles.phaseText}>{getPhaseText()}</span>
      </div>

      {/* 当前玩家指示器 */}
      {(currentPhase === 'bidding' || currentPhase === 'playing') && (
        <div className={styles.currentPlayerIndicator}>
          <div className={styles.playerArrow}>
            <img src="/resources/UI/button/arrow.png" alt="轮到" />
          </div>
          <span className={styles.playerText}>
            轮到 <strong>{getCurrentPlayerName()}</strong>
          </span>
        </div>
      )}

      {/* 倒计时显示 */}
      {countdown > 0 && (currentPhase === 'bidding' || currentPhase === 'playing') && (
        <div className={styles.countdown}>
          <div className={`${styles.countdownCircle} ${countdown <= 10 ? styles.urgent : ''}`}>
            <img src="/resources/UI/button/clock.png" alt="倒计时" className={styles.clockIcon} />
            <span className={styles.countdownText}>{countdown}</span>
          </div>
        </div>
      )}

      {/* 游戏消息 */}
      {gameMessage && (
        <div className={styles.gameMessage}>
          <div className={styles.messageContent}>
            <span className={styles.messageText}>{gameMessage}</span>
          </div>
        </div>
      )}

      {/* 游戏进度条 */}
      {currentPhase === 'playing' && (
        <div className={styles.gameProgress}>
          <div className={styles.progressBar}>
            <div 
              className={styles.progressFill}
              style={{ width: `${((30 - countdown) / 30) * 100}%` }}
            ></div>
          </div>
        </div>
      )}
    </div>
  );
}

// 玩家指示器组件 - 显示在玩家头像附近
export function PlayerIndicator({ 
  isActive = false, 
  isLandlord = false, 
  position = 'bottom' // 'top', 'bottom', 'left', 'right'
}) {
  if (!isActive) return null;

  return (
    <div className={`${styles.playerIndicator} ${styles[position]}`}>
      <div className={styles.indicatorArrow}>
        <img src="/resources/UI/button/arrow.png" alt="当前玩家" />
      </div>
      {isLandlord && (
        <div className={styles.landlordGlow}>
          <img src="/resources/UI/headimage/img_Card_dizhu.png" alt="地主" />
        </div>
      )}
    </div>
  );
}

// 游戏提示消息组件
export function GameToast({ 
  message, 
  type = 'info', // 'info', 'success', 'warning', 'error'
  duration = 3000,
  onClose 
}) {
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        onClose && onClose();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [duration, onClose]);

  return (
    <div className={`${styles.gameToast} ${styles[type]}`}>
      <div className={styles.toastContent}>
        <span className={styles.toastMessage}>{message}</span>
      </div>
    </div>
  );
}
