import { Card } from 'antd';
import styles from './index.module.less';
import { CardContent } from '../CardDisplay/CardContent';

export function OpponentHand({ player }) {
  if (!player) return null;

  const cardCount = player.cards.length;
  const displayCount = Math.min(cardCount, 8); // 最多显示8张卡背

  return (
    <div className={styles.opponentHand}>
      {Array.from({ length: displayCount }).map((_, index) => (
        <Card
          key={index}
          className={styles.cardBack}
          style={{
            marginLeft: index > 0 ? '-25px' : '0', // 卡牌重叠效果
            zIndex: index,
            transform: `rotate(${(index - displayCount / 2) * 2}deg)` // 轻微扇形排列
          }}
        >
          <CardContent isBack={true} />
        </Card>
      ))}

      {/* 如果牌数超过显示数量，显示省略号 */}
      {cardCount > displayCount && (
        <div className={styles.moreCards}>
          +{cardCount - displayCount}
        </div>
      )}
    </div>
  );
}
