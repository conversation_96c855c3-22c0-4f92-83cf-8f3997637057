import { Card } from 'antd';
import { useState } from 'react';
import styles from './index.module.less';
import { CardContent } from '../CardDisplay/CardContent';

export function LandlordCards({ cards, isRevealed = false }) {
  const [showCards, setShowCards] = useState(isRevealed);

  if (!cards || cards.length === 0) {
    return null;
  }

  return (
    <div className={styles.landlordCards}>
      {/* 底牌标题 */}
      <div className={styles.landlordTitle}>
        <img
          src="/resources/UI/headimage/img_Card_dizhu.png"
          alt="地主"
          className={styles.landlordIcon}
        />
        <span className={styles.titleText}>底牌</span>
        {!showCards && (
          <button
            className={styles.revealButton}
            onClick={() => setShowCards(true)}
          >
            翻开
          </button>
        )}
      </div>

      {/* 底牌区域 */}
      <div className={styles.cardsArea}>
        {cards.map((card, index) => (
          <Card
            key={`landlord-${index}`}
            className={`${styles.landlordCard} ${showCards ? styles.revealed : styles.hidden}`}
            style={{
              transform: `rotate(${index * 8 - 8}deg) translateY(${index * 2}px)`,
              zIndex: index,
              animationDelay: showCards ? `${index * 0.2}s` : '0s'
            }}
          >
            <CardContent card={showCards ? card : null} isBack={!showCards} />
          </Card>
        ))}
      </div>

      {/* 底牌数量提示 */}
      <div className={styles.cardCountBadge}>
        <span className={styles.countText}>{cards.length}</span>
      </div>
    </div>
  );
}
