import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, message } from 'antd';
import { PlayerHand } from './PlayerHand';
import { OpponentHand } from './OpponentHand';
import { LandlordCards } from './LandlordCards';
import { CardContent } from '../CardDisplay/CardContent'; // Import the shared component
import { PlayerInfo } from '../PlayerInfo'; // 引入PlayerInfo组件
import styles from './index.module.less';

export function GameTable({ gameManager, currentPlayer }) {
  const [selectedCards, setSelectedCards] = useState([]);
  const [lastPlayedHandUI, setLastPlayedHandUI] = useState(null); // For displaying last played cards
  const [currentTurnPlayerIndex, setCurrentTurnPlayerIndex] = useState(gameManager.currentPlayer);
  const [isMyTurn, setIsMyTurn] = useState(false);
  const [playerHandCards, setPlayerHandCards] = useState(currentPlayer ? [...currentPlayer.cards] : []);
  // 为对手玩家信息添加状态
  const [opponent1, setOpponent1] = useState(null);
  const [opponent2, setOpponent2] = useState(null);


  // 监听游戏状态变化
  useEffect(() => {
    const handleStateChange = (newState) => {
      // Update local state based on GameManager's state
      setCurrentTurnPlayerIndex(gameManager.currentPlayer);
      setLastPlayedHandUI(gameManager.lastHand ? [...gameManager.lastHand] : null);
      if (currentPlayer) {
        setPlayerHandCards([...currentPlayer.cards]); // Update current player's hand
      }
      // 更新对手信息
      if (gameManager.players && currentPlayer) {
        const otherPlayers = gameManager.players.filter(p => p.index !== currentPlayer.index);
        // 简单的分配，实际项目中可能需要根据座位顺序
        setOpponent1(otherPlayers[0] || null);
        setOpponent2(otherPlayers[1] || null);
      } else {
        setOpponent1(null);
        setOpponent2(null);
      }
      setIsMyTurn(gameManager.currentTurnPlayer === currentPlayer && newState === 'PLAYING');
    };

    gameManager.onStateChange = handleStateChange;
    // Initial sync
    handleStateChange(gameManager.currentState);

    return () => {
      gameManager.onStateChange = null;
    };
  }, [gameManager, currentPlayer]);

 useEffect(() => {
    if (currentPlayer) {
        setPlayerHandCards([...currentPlayer.cards]);
        const cb = () => {
            setPlayerHandCards([...currentPlayer.cards]);
        };
        currentPlayer.onCardsUpdate = cb; // Assuming Player class has this
        return () => {
            if (currentPlayer) currentPlayer.onCardsUpdate = null;
        };
    }
 }, [currentPlayer]);

  // 出牌处理
  const handlePlayCards = async () => {
    if (!currentPlayer || !isMyTurn) return;
    await gameManager.handlePlayerAction(currentPlayer, selectedCards);
    // GameManager will call onStateChange, which should update the UI
    // If play was successful, selectedCards should be cleared by GameManager or Player
    if (gameManager.lastHand === selectedCards) { // A simple check if the play was accepted
      setSelectedCards([]);
    }
  };
  // 不出/跳过
  const handlePass = async () => {
    if (!currentPlayer || !isMyTurn) return;
    await gameManager.handlePlayerAction(currentPlayer, []); // Pass empty array for "pass"
    // GameManager will call onStateChange
  };

  const canPass = () => {
    if (!isMyTurn) return false;
    // Cannot pass if you are the one who played the last hand (and it's still your turn because others passed)
    // or if there's no last hand (you are leading the round).
    return gameManager.lastHand !== null && gameManager.lastPlayer !== currentPlayer.index;
  };

  return (
    <div className={styles.gameTable}>
      {/* 四角装饰元素 */}
      <div className={styles.cornerDecorations}>
        <div className={`${styles.cornerDecor} ${styles.topLeft}`}></div>
        <div className={`${styles.cornerDecor} ${styles.topRight}`}></div>
        <div className={`${styles.cornerDecor} ${styles.bottomLeft}`}></div>
        <div className={`${styles.cornerDecor} ${styles.bottomRight}`}></div>
      </div>

      {/* 游戏标题 */}
      <div className={styles.gameTitle}>
        <img
          src="/resources/hall_logo_pic.png"
          alt="斗地主"
          className={styles.titleLogo}
        />
      </div>

      <div className={styles.opponentArea}>
        {opponent1 && (
          <div className={styles.opponentPlayerZone}>
            <PlayerInfo 
              player={opponent1} 
              isLandlord={opponent1.isLandlord} 
              remainingCardsCount={opponent1.cards.length} 
            />
            <OpponentHand player={opponent1} />
          </div>
        )}
        {/* 中间可以放一些全局信息或留空 */}
        {opponent2 && (
          <div className={styles.opponentPlayerZone}>
            <PlayerInfo 
              player={opponent2} 
              isLandlord={opponent2.isLandlord} 
              remainingCardsCount={opponent2.cards.length} 
            />
            <OpponentHand player={opponent2} />
          </div>
        )}
      </div>

      <div className={styles.tableCenter}>
        <LandlordCards cards={gameManager.remainingCards} />
        {lastPlayedHandUI && <PlayedCards cards={lastPlayedHandUI} />}
      </div>

      <div className={styles.playerArea}>
        {currentPlayer && (
          <PlayerInfo 
            player={currentPlayer} 
            isCurrentUser={true} 
            isLandlord={currentPlayer.isLandlord} 
            remainingCardsCount={playerHandCards.length} 
          />
        )}
        <PlayerHand
          cards={playerHandCards}
          selectedCards={selectedCards}
          onSelect={setSelectedCards} />
        
        <div className={styles.actions}>
          {/* 替换为图片按钮或自定义样式的按钮 */}
          <button
            onClick={handlePlayCards}
            disabled={!selectedCards.length || !isMyTurn}
            className={`${styles.actionButton} ${styles.playButton}`}
            title="出牌"
          >
          </button>
          <button
            onClick={handlePass}
            disabled={!canPass()}
            className={`${styles.actionButton} ${styles.passButton}`}
            style={{ backgroundImage: `url('/assets/UI/button/btn_bujiao.png')` }} // “不叫”的图也常用于“不出”
          >
            {/* 不出 */}
          </button>
          <Button 
            onClick={() => setSelectedCards([])} 
            className={styles.cancelButton} // 可以为取消按钮也设计一个样式或图片
            disabled={!selectedCards.length || !isMyTurn}
          >
            取消选择
          </Button>
        </div>
      </div>
    </div>
  );
}

// ... PlayedCards component remains the same

function PlayedCards({ cards }) {
  return (
    <div className={styles.playedCards}>
      {cards.map((card, index) => (
        <Card 
          key={`${card.type}-${card.value}-${index}`}
          className={styles.card}
          style={{
            transform: `rotate(${index * 5 - (cards.length * 2.5)}deg)`,
            zIndex: index
          }}
        >
          <CardContent card={card} />
        </Card>
      ))}
    </div>
  );
}
