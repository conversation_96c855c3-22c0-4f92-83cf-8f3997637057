// 标准斗地主游戏界面布局
.gameTable {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  // 多层背景效果
  background:
    // 主背景图片
    url('/resources/table_bg_1.jpg') center/cover no-repeat,
    // 备用背景图片
    url('/resources/mahjong_table.jpg') center/cover no-repeat,
    // 渐变背景作为最后的备用
    radial-gradient(ellipse at center, #2d5016 0%, #1a3009 70%, #0d1804 100%);

  // 添加纹理遮罩层
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      // 添加噪点纹理
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
      // 深色遮罩提高可读性
      rgba(0, 0, 0, 0.25);
    z-index: 1;
  }

  // 添加装饰边框
  &::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border: 3px solid rgba(255, 215, 0, 0.3);
    border-radius: 20px;
    box-shadow:
      inset 0 0 20px rgba(255, 215, 0, 0.1),
      0 0 30px rgba(0, 0, 0, 0.5);
    pointer-events: none;
    z-index: 1;
  }

  // 所有子元素需要在遮罩之上
  > * {
    position: relative;
    z-index: 2;
  }
}

// 四角装饰元素
.cornerDecorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;

  .cornerDecor {
    position: absolute;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
    border: 2px solid rgba(255, 215, 0, 0.4);
    border-radius: 50%;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 40px;
      height: 40px;
      background: rgba(255, 215, 0, 0.2);
      border-radius: 50%;
      border: 1px solid rgba(255, 215, 0, 0.6);
    }

    &.topLeft {
      top: 20px;
      left: 20px;
    }

    &.topRight {
      top: 20px;
      right: 20px;
    }

    &.bottomLeft {
      bottom: 20px;
      left: 20px;
    }

    &.bottomRight {
      bottom: 20px;
      right: 20px;
    }
  }
}

// 游戏标题
.gameTitle {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;

  .titleLogo {
    height: 60px;
    width: auto;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
      filter: drop-shadow(0 6px 12px rgba(255, 215, 0, 0.3));
    }
  }

  // 对手玩家区域 - 顶部左右两侧
  .opponentArea {
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    padding: 0 60px;
    height: 200px;

    .opponentPlayerZone {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;

      // 左侧玩家
      &:first-child {
        align-items: flex-start;
      }

      // 右侧玩家
      &:last-child {
        align-items: flex-end;
      }
    }
  }

  // 游戏桌面中央区域
  .tableCenter {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    min-width: 500px;
    min-height: 250px;

    // 添加桌面效果
    background:
      radial-gradient(ellipse 400px 200px at center, rgba(139, 69, 19, 0.3) 0%, transparent 70%),
      radial-gradient(ellipse 300px 150px at center, rgba(160, 82, 45, 0.2) 0%, transparent 80%);
    border-radius: 50%;
    border: 3px solid rgba(255, 215, 0, 0.2);
    box-shadow:
      inset 0 0 30px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(255, 215, 0, 0.1);

    // 添加桌面纹理
    &::before {
      content: '';
      position: absolute;
      top: 10px;
      left: 10px;
      right: 10px;
      bottom: 10px;
      background:
        repeating-linear-gradient(
          45deg,
          transparent,
          transparent 2px,
          rgba(255, 255, 255, 0.02) 2px,
          rgba(255, 255, 255, 0.02) 4px
        );
      border-radius: 50%;
      pointer-events: none;
    }
  }

  // 当前玩家区域 - 底部
  .playerArea {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    width: 90%;
    max-width: 800px;
  }

  // 手牌区域样式
  .playerHand {
    position: relative;
    width: 100%;
    height: 150px;
    display: flex;
    justify-content: center;
    align-items: flex-end;
  }

  // 卡牌样式
  .card {
    width: 75px;
    height: 110px;
    background: white;
    border-radius: 8px;
    border: 2px solid #ddd;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 8px;

    // 使用卡牌背景图片
    background-image: url('/resources/UI/card/card.png');
    background-size: cover;
    background-position: center;

    &.selected {
      transform: translateY(-25px) scale(1.05);
      box-shadow: 0 8px 16px rgba(255, 215, 0, 0.6);
      border-color: #ffd700;
    }

    &:hover {
      transform: translateY(-10px) scale(1.02);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
    }
  }

  // 对手手牌区域
  .opponentHand {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 80px;

    .moreCards {
      margin-left: 10px;
      font-size: 12px;
      color: #ffd700;
      font-weight: bold;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
      background: rgba(0, 0, 0, 0.5);
      padding: 4px 8px;
      border-radius: 8px;
      border: 1px solid rgba(255, 215, 0, 0.5);
    }
  }

  // 对手卡背样式
  .cardBack {
    width: 45px;
    height: 65px;
    border-radius: 6px;
    border: 1px solid rgba(255, 215, 0, 0.3);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.5);
      border-color: rgba(255, 215, 0, 0.6);
    }

    &:first-child {
      margin-left: 0;
    }
  }

  // 中央出牌区域
  .playedCards {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 25px;
    background:
      radial-gradient(ellipse at center, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 70%),
      linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, transparent 50%);
    border-radius: 20px;
    border: 3px solid rgba(255, 215, 0, 0.4);
    box-shadow:
      inset 0 0 20px rgba(0, 0, 0, 0.3),
      0 4px 15px rgba(0, 0, 0, 0.3);
    min-width: 350px;
    min-height: 140px;
    position: relative;

    // 添加装饰光效
    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(45deg,
        rgba(255, 215, 0, 0.3) 0%,
        transparent 25%,
        transparent 75%,
        rgba(255, 215, 0, 0.3) 100%);
      border-radius: 22px;
      z-index: -1;
      animation: borderGlow 3s ease-in-out infinite alternate;
    }
  }

  // 底牌区域
  .landlordCards {
    display: flex;
    gap: 8px;
    padding: 15px 20px;
    background:
      linear-gradient(135deg, rgba(139, 0, 0, 0.4) 0%, rgba(139, 0, 0, 0.2) 100%),
      radial-gradient(circle at center, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
    border-radius: 15px;
    border: 3px solid rgba(255, 215, 0, 0.6);
    box-shadow:
      inset 0 0 15px rgba(255, 215, 0, 0.2),
      0 4px 12px rgba(0, 0, 0, 0.4);
    position: relative;

    // 添加"底牌"标识
    &::after {
      content: '底牌';
      position: absolute;
      top: -12px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(139, 0, 0, 0.9);
      color: #ffd700;
      padding: 4px 12px;
      border-radius: 8px;
      font-size: 12px;
      font-weight: bold;
      border: 2px solid rgba(255, 215, 0, 0.8);
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    }
  }

  // 边框发光动画
  @keyframes borderGlow {
    0% {
      opacity: 0.5;
      transform: scale(1);
    }
    100% {
      opacity: 0.8;
      transform: scale(1.02);
    }
  }

  // 操作按钮区域
  .actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    align-items: center;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 15px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    backdrop-filter: blur(5px);

    .actionButton {
      width: 140px;
      height: 60px;
      background-color: transparent;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;

      // 添加按钮光效
      &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg,
          rgba(255, 215, 0, 0.3) 0%,
          transparent 25%,
          transparent 75%,
          rgba(255, 215, 0, 0.3) 100%);
        border-radius: 8px;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: -1;
      }

      &:hover:not(:disabled) {
        transform: scale(1.1) translateY(-3px);
        filter: brightness(1.2) drop-shadow(0 8px 16px rgba(255, 215, 0, 0.4));

        &::before {
          opacity: 1;
        }
      }

      &:active:not(:disabled) {
        transform: scale(1.05) translateY(-1px);
        filter: brightness(1.1);
      }

      &:disabled {
        filter: grayscale(100%) brightness(0.5);
        cursor: not-allowed;
        transform: none;

        &::before {
          opacity: 0;
        }
      }

      // 出牌按钮
      &.playButton {
        background-image: url('/resources/UI/button/btn_chupai.png');
      }

      // 不出按钮
      &.passButton {
        background-image: url('/resources/UI/button/btn_bujiao.png');
      }

      // 叫地主按钮
      &.bidButton {
        background-image: url('/resources/UI/button/qiangzhuang.png');
      }

      // 不叫按钮
      &.noBidButton {
        background-image: url('/resources/UI/button/buqiangzhuang.png');
      }

      // 提示按钮
      &.hintButton {
        background-image: url('/resources/UI/button/btn_tisji.png');
      }

      // 准备按钮
      &.readyButton {
        background-image: url('/resources/UI/btnready.png');
      }
    }

    .cancelButton {
      padding: 12px 20px;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 240, 240, 0.9) 100%);
      border: 2px solid rgba(255, 215, 0, 0.5);
      border-radius: 12px;
      color: #333;
      font-weight: bold;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(255, 248, 220, 1) 100%);
        border-color: rgba(255, 215, 0, 0.8);
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 6px 12px rgba(255, 215, 0, 0.3);
      }

      &:active:not(:disabled) {
        transform: translateY(-1px) scale(1.02);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }
}
