// 卡牌内容样式

// 雪碧图卡牌显示
.cardSprite {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  border-radius: 8px;
  overflow: hidden;
  position: relative;

  // 添加轻微的阴影效果
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    pointer-events: none;
  }
}

// 卡背样式
.cardBack {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a3e8c 0%, #2d5aa0 50%, #1a3e8c 100%);
  border-radius: 8px;
  position: relative;
  overflow: hidden;

  .cardBackPattern {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    background:
      radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
      repeating-linear-gradient(
        45deg,
        transparent,
        transparent 4px,
        rgba(255, 255, 255, 0.1) 4px,
        rgba(255, 255, 255, 0.1) 8px
      );
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
  }
}

// 空卡牌样式
.emptyCard {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 8px;
}

// 备用文字显示方案
.cardContentContainer {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  padding: 8px;
  position: relative;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .cardValue {
    font-size: 18px;
    font-weight: bold;
    line-height: 1;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  }

  .cardType {
    font-size: 16px;
    line-height: 1;
    align-self: flex-end;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  }

  .red {
    color: #d32f2f;
  }

  .black {
    color: #333;
  }
}

// 如果使用独立图片方案
.cardImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}