import styles from './CardContent.module.less';
import { getCardSpritePosition } from '../../utils/cardMapping';

/**
 * 获取卡牌值的显示文本
 * @param {number} value - 卡牌的数值
 * @returns {string}
 */
export function getCardValueDisplay(value) {
  if (value >= 3 && value <= 10) return String(value);
  switch (value) {
    case 11: return 'J';
    case 12: return 'Q';
    case 13: return 'K';
    case 14: return 'A';
    case 15: return '2';
    case 16: return '小王';
    case 17: return '大王';
    default: return '';
  }
}

/**
 * 获取卡牌花色的显示文本/符号
 * @param {string} type - 卡牌的花色
 * @returns {string}
 */
export function getCardTypeDisplay(type, value) {
  if (value === 16 || value === 17) return '';
  switch (type) {
    case 'spade': return '♠';
    case 'heart': return '♥';
    case 'diamond': return '♦';
    case 'club': return '♣';
    default: return '';
  }
}

export function CardContent({ card, isBack = false }) {
  if (isBack) {
    // 显示卡背
    return (
      <div className={styles.cardBack}>
        <div className={styles.cardBackPattern}></div>
      </div>
    );
  }

  if (!card) {
    return <div className={styles.emptyCard}></div>;
  }

  // 获取雪碧图位置
  const spritePos = getCardSpritePosition(card);

  if (spritePos) {
    // 使用雪碧图显示卡牌
    const backgroundStyle = {
      backgroundImage: 'url(/resources/UI/card/card.png)',
      backgroundPosition: `-${spritePos.x}px -${spritePos.y}px`,
      backgroundSize: '1536px 807px', // 雪碧图的总尺寸
      width: '100%',
      height: '100%',
    };

    return (
      <div className={styles.cardSprite} style={backgroundStyle}>
        {/* 雪碧图已包含完整卡牌设计，无需额外内容 */}
      </div>
    );
  }

  // 备用方案：使用文字和符号显示
  const isRed = card.type === 'heart' || card.type === 'diamond';

  return (
    <div className={styles.cardContentContainer}>
      <div className={`${styles.cardValue} ${isRed ? styles.red : styles.black}`}>
        {getCardValueDisplay(card.value)}
      </div>
      <div className={`${styles.cardType} ${isRed ? styles.red : styles.black}`}>
        {getCardTypeDisplay(card.type, card.value)}
      </div>
    </div>
  );
}