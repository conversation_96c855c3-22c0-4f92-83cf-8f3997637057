import { Avatar } from 'antd';
import styles from './index.module.less';

export function PlayerInfo({
  player,
  isCurrentUser = false,
  isLandlord = false,
  remainingCardsCount = 0,
  position = 'bottom' // 'bottom', 'left', 'right'
}) {
  if (!player) return null;

  // 根据玩家索引选择头像
  const avatarIndex = (player.index % 4) + 1;
  const avatarSrc = `/resources/UI/headimage/avatar_${avatarIndex}.png`;

  // 玩家名称，如果是当前用户显示"我"
  const playerName = isCurrentUser ? '我' : `玩家${player.index + 1}`;

  // 根据位置确定样式类
  const positionClass = `position-${position}`;

  return (
    <div className={`${styles.playerInfo} ${isCurrentUser ? styles.currentUser : ''} ${styles[positionClass]}`}>
      {/* 头像区域 */}
      <div className={styles.avatarContainer}>
        {/* 头像框架背景 */}
        <div className={styles.avatarFrame}>
          <Avatar
            src={avatarSrc}
            size={isCurrentUser ? 80 : 60}
            className={styles.avatar}
          />
          
          {/* 地主标识 */}
          {isLandlord && (
            <div className={styles.landlordBadge}>
              <img
                src="/resources/UI/headimage/img_Card_dizhu.png"
                alt="地主"
                className={styles.landlordIcon}
              />
            </div>
          )}
        </div>
        
        {/* VIP框架装饰 */}
        <div className={styles.vipFrame}>
          <img
            src="/resources/UI/frame_vip.png"
            alt="VIP框架"
            className={styles.frameImage}
          />
        </div>
      </div>

      {/* 玩家信息 */}
      <div className={styles.playerDetails}>
        {/* 玩家名称 */}
        <div className={styles.playerName}>
          {playerName}
        </div>
        
        {/* 剩余牌数 */}
        <div className={styles.cardCount}>
          <span className={styles.countText}>
            {remainingCardsCount}张
          </span>
        </div>
        
        {/* 玩家状态指示器 */}
        <div className={styles.playerStatus}>
          {/* 在线状态 */}
          <div className={styles.onlineStatus}>
            <div className={styles.statusDot}></div>
            <span className={styles.statusText}>在线</span>
          </div>

          {/* 准备状态 */}
          {!isCurrentUser && (
            <div className={styles.readyStatus}>
              <img
                src="/resources/UI/pm_ready.png"
                alt="准备"
                className={styles.readyIcon}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
