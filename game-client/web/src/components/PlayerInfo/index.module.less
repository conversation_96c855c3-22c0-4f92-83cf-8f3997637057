// 玩家信息组件样式
.playerInfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  border: 2px solid rgba(255, 215, 0, 0.3);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(255, 215, 0, 0.6);
    background: rgba(0, 0, 0, 0.4);
  }
  
  // 当前用户样式
  &.currentUser {
    border-color: rgba(255, 215, 0, 0.8);
    background: rgba(139, 0, 0, 0.2);
    
    .playerName {
      color: #ffd700;
      font-weight: bold;
    }
  }
}

// 头像容器
.avatarContainer {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 头像框架
.avatarFrame {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .avatar {
    border: 3px solid #ffd700;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 12px rgba(255, 215, 0, 0.4);
    }
  }
}

// VIP框架装饰
.vipFrame {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  pointer-events: none;
  
  .frameImage {
    width: 100%;
    height: 100%;
    object-fit: contain;
    opacity: 0.8;
  }
}

// 地主标识
.landlordBadge {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 30px;
  height: 30px;
  background: radial-gradient(circle, #ff6b6b, #ff4757);
  border-radius: 50%;
  border: 2px solid #ffd700;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  animation: landlordPulse 2s infinite;
  
  .landlordIcon {
    width: 20px;
    height: 20px;
    object-fit: contain;
  }
}

// 地主标识动画
@keyframes landlordPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.6);
  }
}

// 玩家详细信息
.playerDetails {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  width: 100%;
}

// 玩家名称
.playerName {
  font-size: 14px;
  font-weight: 600;
  color: #fff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
}

// 牌数显示
.cardCount {
  background: rgba(139, 69, 19, 0.8);
  border: 2px solid #ffd700;
  border-radius: 12px;
  padding: 2px 8px;
  min-width: 40px;
  text-align: center;
  
  .countText {
    font-size: 12px;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  }
}

// 在线状态
.onlineStatus {
  display: flex;
  align-items: center;
  justify-content: center;
  
  .statusDot {
    width: 8px;
    height: 8px;
    background: #52c41a;
    border-radius: 50%;
    border: 1px solid #fff;
    box-shadow: 0 0 4px rgba(82, 196, 26, 0.6);
    animation: onlinePulse 2s infinite;
  }
}

// 在线状态动画
@keyframes onlinePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .playerInfo {
    padding: 8px;
    gap: 6px;
    
    .avatarFrame .avatar {
      width: 50px !important;
      height: 50px !important;
    }
    
    .playerName {
      font-size: 12px;
    }
    
    .cardCount .countText {
      font-size: 10px;
    }
    
    .landlordBadge {
      width: 24px;
      height: 24px;
      top: -8px;
      right: -8px;
      
      .landlordIcon {
        width: 16px;
        height: 16px;
      }
    }
  }
}
