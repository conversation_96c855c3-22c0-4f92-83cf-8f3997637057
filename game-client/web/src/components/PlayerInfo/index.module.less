// 玩家信息组件样式
.playerInfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background:
    linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 100%),
    radial-gradient(circle at center, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
  border-radius: 15px;
  border: 2px solid rgba(255, 215, 0, 0.4);
  backdrop-filter: blur(8px);
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
  min-width: 120px;

  // 添加装饰光效
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg,
      rgba(255, 215, 0, 0.2) 0%,
      transparent 25%,
      transparent 75%,
      rgba(255, 215, 0, 0.2) 100%);
    border-radius: 16px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    border-color: rgba(255, 215, 0, 0.7);
    background:
      linear-gradient(135deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.3) 100%),
      radial-gradient(circle at center, rgba(255, 215, 0, 0.15) 0%, transparent 70%);
    transform: translateY(-2px);
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);

    &::before {
      opacity: 1;
    }
  }

  // 当前用户样式
  &.currentUser {
    border-color: rgba(255, 215, 0, 0.9);
    background:
      linear-gradient(135deg, rgba(139, 0, 0, 0.4) 0%, rgba(139, 0, 0, 0.2) 100%),
      radial-gradient(circle at center, rgba(255, 215, 0, 0.2) 0%, transparent 70%);
    box-shadow:
      0 6px 20px rgba(255, 215, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);

    .playerName {
      color: #ffd700;
      font-weight: bold;
      text-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
    }

    &::before {
      opacity: 0.7;
    }
  }

  // 不同位置的适配
  &.position-left {
    align-items: flex-start;

    .playerDetails {
      align-items: flex-start;
    }
  }

  &.position-right {
    align-items: flex-end;

    .playerDetails {
      align-items: flex-end;
    }
  }

  &.position-bottom {
    align-items: center;

    .playerDetails {
      align-items: center;
    }
  }
}

// 头像容器
.avatarContainer {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 头像框架
.avatarFrame {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .avatar {
    border: 3px solid #ffd700;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 12px rgba(255, 215, 0, 0.4);
    }
  }
}

// VIP框架装饰
.vipFrame {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  pointer-events: none;
  
  .frameImage {
    width: 100%;
    height: 100%;
    object-fit: contain;
    opacity: 0.8;
  }
}

// 地主标识
.landlordBadge {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 30px;
  height: 30px;
  background: radial-gradient(circle, #ff6b6b, #ff4757);
  border-radius: 50%;
  border: 2px solid #ffd700;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  animation: landlordPulse 2s infinite;
  
  .landlordIcon {
    width: 20px;
    height: 20px;
    object-fit: contain;
  }
}

// 地主标识动画
@keyframes landlordPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.6);
  }
}

// 玩家详细信息
.playerDetails {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  width: 100%;
}

// 玩家名称
.playerName {
  font-size: 14px;
  font-weight: 600;
  color: #fff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
}

// 牌数显示
.cardCount {
  background: rgba(139, 69, 19, 0.8);
  border: 2px solid #ffd700;
  border-radius: 12px;
  padding: 2px 8px;
  min-width: 40px;
  text-align: center;
  
  .countText {
    font-size: 12px;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  }
}

// 玩家状态区域
.playerStatus {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  width: 100%;
}

// 在线状态
.onlineStatus {
  display: flex;
  align-items: center;
  gap: 4px;

  .statusDot {
    width: 8px;
    height: 8px;
    background: #52c41a;
    border-radius: 50%;
    border: 1px solid #fff;
    box-shadow: 0 0 6px rgba(82, 196, 26, 0.8);
    animation: onlinePulse 2s infinite;
  }

  .statusText {
    font-size: 10px;
    color: #52c41a;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  }
}

// 准备状态
.readyStatus {
  display: flex;
  align-items: center;
  justify-content: center;

  .readyIcon {
    width: 24px;
    height: 24px;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
    animation: readyBounce 1.5s ease-in-out infinite;
  }
}

// 在线状态动画
@keyframes onlinePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 0 6px rgba(82, 196, 26, 0.8);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.3);
    box-shadow: 0 0 12px rgba(82, 196, 26, 1);
  }
}

// 准备状态动画
@keyframes readyBounce {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-3px) scale(1.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .playerInfo {
    padding: 8px;
    gap: 6px;
    
    .avatarFrame .avatar {
      width: 50px !important;
      height: 50px !important;
    }
    
    .playerName {
      font-size: 12px;
    }
    
    .cardCount .countText {
      font-size: 10px;
    }
    
    .landlordBadge {
      width: 24px;
      height: 24px;
      top: -8px;
      right: -8px;
      
      .landlordIcon {
        width: 16px;
        height: 16px;
      }
    }
  }
}
