export const CARD_TYPES = {
  HEART: 'heart',
  DIAMOND: 'diamond',
  CLUB: 'club',
  SPADE: 'spade',
  J<PERSON><PERSON>: 'joker'
};

export const HAND_TYPES = {
  SINGLE: 'single',
  PAIR: 'pair',
  TRIPLE: 'triple',
  TRIPLE_WITH_SINGLE: 'triple_with_single',
  TRIPLE_WITH_PAIR: 'triple_with_pair',
  STRAIGHT: 'straight',
  CONSECUTIVE_PAIRS: 'consecutive_pairs',
  // CONSECUTIVE_TRIPLES is often called AIRPLANE
  AIRPLANE: 'airplane', // For three-card sequences
  AIRPLANE_WITH_SINGLES: 'airplane_with_singles', // Airplane with single card wings
  AIRPLANE_WITH_PAIRS: 'airplane_with_pairs',     // Airplane with pair wings
  BOMB: 'bomb',
  QUAD_WITH_TWO_SINGLES: 'quad_with_two_singles', // Four of a kind with two single cards
  QUAD_WITH_TWO_PAIRS: 'quad_with_two_pairs',     // Four of a kind with two pairs
  KING_BOMB: 'king_bomb', // Also known as ROCKET
  ROCKET: 'rocket' // Explicitly ROCKET for King Bomb
};

export const GAME_STATES = {
  IDLE: 'IDLE',
  DEALING: 'DEALING',
  BIDDING: 'BIDDING',
  PLAYING: 'PLAYING',
  GAME_OVER: 'GAME_OVER'
};

// Timers from the other constants file
export const TIMERS = {
  BID_TIMEOUT: 20000, // milliseconds - 20秒叫牌时间
  PLAY_TIMEOUT: 20000 // milliseconds - 20秒出牌时间
};
