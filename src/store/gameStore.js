import { makeAutoObservable } from 'mobx'

class GameStore {
  // 用户信息
  user = null
  token = null
  
  // 游戏状态
  currentGame = null
  currentRoom = null
  gameState = 'idle' // idle, waiting, playing, finished
  
  // 房间信息
  roomInfo = null
  players = []
  currentPlayer = null
  landlordId = null
  
  // 手牌信息
  handCards = []
  lastPlayedCards = null
  
  // 叫牌信息
  currentBidScore = 0
  bidHistory = []
  
  // 游戏配置
  gameConfig = null
  
  constructor() {
    makeAutoObservable(this)
    this.loadFromStorage()
  }
  
  // 用户相关
  setUser(user) {
    this.user = user
    this.saveToStorage()
  }
  
  setToken(token) {
    this.token = token
    localStorage.setItem('token', token)
  }
  
  logout() {
    this.user = null
    this.token = null
    this.currentGame = null
    this.currentRoom = null
    this.gameState = 'idle'
    this.resetGameState()
    localStorage.removeItem('token')
    localStorage.removeItem('gameStore')
  }
  
  // 游戏状态相关
  setGameState(state) {
    this.gameState = state
  }
  
  setCurrentGame(game) {
    this.currentGame = game
    this.saveToStorage()
  }
  
  setCurrentRoom(room) {
    this.currentRoom = room
    this.saveToStorage()
  }
  
  setRoomInfo(roomInfo) {
    this.roomInfo = roomInfo
  }
  
  setPlayers(players) {
    this.players = players
  }
  
  setCurrentPlayer(playerId) {
    this.currentPlayer = playerId
  }
  
  setLandlord(landlordId) {
    this.landlordId = landlordId
  }
  
  // 手牌相关
  setHandCards(cards) {
    this.handCards = cards
  }
  
  removeCards(cards) {
    this.handCards = this.handCards.filter(card => !cards.includes(card))
  }
  
  setLastPlayedCards(cards) {
    this.lastPlayedCards = cards
  }
  
  // 叫牌相关
  setBidScore(score) {
    this.currentBidScore = score
  }
  
  addBidHistory(bid) {
    this.bidHistory.push(bid)
  }
  
  clearBidHistory() {
    this.bidHistory = []
  }
  
  // 游戏配置
  setGameConfig(config) {
    this.gameConfig = config
  }
  
  // 存储相关
  saveToStorage() {
    const data = {
      user: this.user,
      currentGame: this.currentGame,
      currentRoom: this.currentRoom,
      gameState: this.gameState
    }
    localStorage.setItem('gameStore', JSON.stringify(data))
  }
  
  loadFromStorage() {
    try {
      const token = localStorage.getItem('token')
      if (token) {
        this.token = token
      }

      const data = localStorage.getItem('gameStore')
      if (data) {
        const parsed = JSON.parse(data)
        this.user = parsed.user
        this.currentGame = parsed.currentGame
        this.currentRoom = parsed.currentRoom
        this.gameState = parsed.gameState || 'idle'
      }
    } catch (error) {
      console.error('Failed to load from storage:', error)
      // 清理损坏的数据
      localStorage.removeItem('gameStore')
    }
  }

  // 检查登录状态
  isLoggedIn() {
    return !!(this.user && this.token)
  }

  // 获取用户显示名称
  getUserDisplayName() {
    if (!this.user) return '游客'
    return this.user.nickname || `玩家${this.user.id}` || '匿名玩家'
  }
  
  // 重置游戏状态
  resetGameState() {
    this.currentRoom = null
    this.roomInfo = null
    this.players = []
    this.currentPlayer = null
    this.landlordId = null
    this.handCards = []
    this.lastPlayedCards = null
    this.currentBidScore = 0
    this.bidHistory = []
    this.gameState = 'idle'
  }


}

export const gameStore = new GameStore()
