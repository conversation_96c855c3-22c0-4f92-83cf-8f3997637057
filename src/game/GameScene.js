import Phaser from 'phaser'
import { GameState } from './GameState.js'

// 游戏场景类
class GameMainScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameMainScene' })
    this.gameState = new GameState()
    this.cardSprites = new Map() // 存储卡牌精灵
    this.playerHandAreas = {} // 玩家手牌区域
    this.uiElements = {} // UI元素
    this.selectedCards = [] // 当前选中的牌
  }

  preload() {
    // 预加载资源
    console.log('GameMainScene: 预加载资源')

    // 加载游戏背景
    this.load.image('table_bg', '/resources/table_bg_1.jpg')

    // 加载玩家头像 - 只加载必需的
    this.load.image('avatar_1', '/resources/UI/headimage/avatar_1.png')
    this.load.image('avatar_2', '/resources/UI/headimage/avatar_2.png')
    this.load.image('avatar_3', '/resources/UI/headimage/avatar_3.png')

    // 加载卡牌雪碧图
    this.load.image('cardSprite', '/resources/UI/card/card.png')
    console.log('🎴 加载卡牌雪碧图')

    // 暂时跳过音效加载，避免404错误
    console.log('GameMainScene: 资源预加载完成')
  }

  create() {
    // 游戏初始化逻辑
    console.log('🎮 GameMainScene: 创建游戏场景')

    try {
      // 创建标准斗地主游戏背景
      console.log('🎨 创建背景')
      this.createStandardBackground()

      // 添加标题 - 更华丽的设计
      console.log('📝 创建标题')
      this.createGameTitle()

      // 创建游戏桌面
      console.log('🎲 创建游戏桌面')
      this.createGameTable()

      // 添加玩家位置
      console.log('👥 创建玩家位置')
      this.createPlayerPositions()

      // 创建UI界面
      console.log('🖥️ 创建UI界面')
      this.createGameUI()

      // 初始化音效
      console.log('🔊 初始化音效')
      this.initializeAudio()

      // 初始化UI状态，等待用户点击开始
      console.log('🔄 更新UI状态')
      this.updateUI()

      console.log('✅ 游戏场景创建完成')
    } catch (error) {
      console.error('❌ 游戏场景创建失败:', error)
    }
  }

  createGameTitle() {
    // 创建简洁的游戏标题，类似参考图
    const titleContainer = this.add.container(600, 40)

    // 简化的标题文字
    const mainTitle = this.add.text(0, 0, '斗地主', {
      fontSize: '24px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold',
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)
    titleContainer.add(mainTitle)

    this.uiElements.title = titleContainer
  }






  initializeAudio() {
    // 初始化音效系统
    try {
      // 播放背景音乐
      if (this.cache.audio.exists('bg_music')) {
        const bgMusic = this.sound.add('bg_music', {
          volume: 0.2,
          loop: true
        })
        bgMusic.play()
        this.uiElements.bgMusic = bgMusic
      }

      // 预加载其他音效
      this.soundEffects = {
        cardSound: this.cache.audio.exists('card_sound') ? this.sound.add('card_sound', { volume: 0.3 }) : null,
        chupaiSound: this.cache.audio.exists('chupai_sound') ? this.sound.add('chupai_sound', { volume: 0.4 }) : null,
        fapaiSound: this.cache.audio.exists('fapai_sound') ? this.sound.add('fapai_sound', { volume: 0.3 }) : null
      }

      console.log('音效系统初始化完成')
    } catch (error) {
      console.warn('音效初始化失败:', error)
    }
  }

  playSound(soundName, volume = 1.0) {
    // 播放音效的统一方法 - 暂时禁用避免404错误
    console.log(`🔊 播放音效: ${soundName} (音量: ${volume})`)
    // 暂时注释掉音效播放，专注于核心功能
    /*
    try {
      if (this.soundEffects && this.soundEffects[soundName]) {
        this.soundEffects[soundName].play({ volume })
      } else if (this.cache.audio.exists(soundName)) {
        this.sound.play(soundName, { volume })
      }
    } catch (error) {
      console.warn(`播放音效失败: ${soundName}`, error)
    }
    */
  }



  createStandardBackground() {
    // 创建蓝色渐变背景，类似标准斗地主
    const bg = this.add.graphics()
    bg.fillGradientStyle(0x1E3A8A, 0x1E3A8A, 0x1E40AF, 0x2563EB, 1)
    bg.fillRect(0, 0, 1200, 800)

    // 添加纹理效果
    const texture = this.add.graphics()
    texture.fillStyle(0xFFFFFF, 0.05)
    for (let i = 0; i < 50; i++) {
      const x = Math.random() * 1200
      const y = Math.random() * 800
      texture.fillCircle(x, y, Math.random() * 2)
    }

    // 创建简化的游戏区域
    this.createSimpleGameArea()
  }

  createSimpleGameArea() {
    // 创建中央出牌区域 - 简化版本
    const centerX = 600
    const centerY = 300

    // 中央区域背景
    const centerArea = this.add.graphics()
    centerArea.fillStyle(0x000000, 0.2)
    centerArea.fillRoundedRect(centerX - 150, centerY - 80, 300, 160, 20)

    // 存储中央区域引用
    this.uiElements.centerPlayArea = {
      x: centerX,
      y: centerY
    }
  }

  createGameTable() {
    // 创建标准斗地主游戏桌面
    const centerX = 600
    const centerY = 350

    // 主游戏区域 - 椭圆形桌面
    const tableGraphics = this.add.graphics()

    // 桌面阴影
    tableGraphics.fillStyle(0x000000, 0.3)
    tableGraphics.fillEllipse(centerX + 5, centerY + 5, 500, 280)

    // 桌面主体 - 深绿色毛毡质感
    tableGraphics.fillGradientStyle(0x0F5132, 0x0F5132, 0x0A3D26, 0x0A3D26, 1)
    tableGraphics.fillEllipse(centerX, centerY, 500, 280)

    // 桌面边框 - 金色装饰
    tableGraphics.lineStyle(4, 0xDAA520, 1)
    tableGraphics.strokeEllipse(centerX, centerY, 500, 280)

    // 内边框装饰
    tableGraphics.lineStyle(2, 0xFFD700, 0.8)
    tableGraphics.strokeEllipse(centerX, centerY, 480, 260)

    // 中央出牌区域
    this.createCenterPlayArea(centerX, centerY)

    // 添加桌面装饰元素
    this.createTableDecorations(centerX, centerY)
  }

  createCenterPlayArea(centerX, centerY) {
    // 创建中央出牌区域
    const playAreaGraphics = this.add.graphics()

    // 出牌区域背景
    playAreaGraphics.fillStyle(0x1A5D3A, 0.6)
    playAreaGraphics.fillRoundedRect(centerX - 120, centerY - 60, 240, 120, 15)

    // 出牌区域边框
    playAreaGraphics.lineStyle(2, 0xFFD700, 0.6)
    playAreaGraphics.strokeRoundedRect(centerX - 120, centerY - 60, 240, 120, 15)

    // 出牌区域标识
    const playAreaText = this.add.text(centerX, centerY, '出牌区', {
      fontSize: '18px',
      color: '#FFD700',
      fontFamily: 'Arial',
      fontWeight: 'bold',
      alpha: 0.7
    }).setOrigin(0.5)

    // 存储出牌区域引用
    this.uiElements.centerPlayArea = {
      graphics: playAreaGraphics,
      text: playAreaText,
      x: centerX,
      y: centerY
    }
  }

  createTableDecorations(centerX, centerY) {
    // 添加桌面装饰元素

    // 四个角的装饰图案
    const decorPositions = [
      { x: centerX - 200, y: centerY - 100, rotation: 0 },
      { x: centerX + 200, y: centerY - 100, rotation: Math.PI / 2 },
      { x: centerX + 200, y: centerY + 100, rotation: Math.PI },
      { x: centerX - 200, y: centerY + 100, rotation: -Math.PI / 2 }
    ]

    decorPositions.forEach(pos => {
      const decor = this.add.graphics()
      decor.fillStyle(0xDAA520, 0.4)

      // 创建花纹装饰
      decor.fillTriangle(0, -8, -6, 6, 6, 6)
      decor.fillCircle(0, -4, 3)

      decor.setPosition(pos.x, pos.y)
      decor.setRotation(pos.rotation)
    })

    // 添加中央logo区域
    const logoArea = this.add.graphics()
    logoArea.fillStyle(0xDAA520, 0.3)
    logoArea.fillCircle(centerX, centerY - 180, 25)
    logoArea.lineStyle(2, 0xFFD700, 0.8)
    logoArea.strokeCircle(centerX, centerY - 180, 25)

    // Logo文字
    this.add.text(centerX, centerY - 180, '斗', {
      fontSize: '20px',
      color: '#FFD700',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)
  }

  createAmbientEffects() {
    // 创建环境装饰效果

    // 添加飘落的光点效果
    this.createFloatingParticles()

    // 添加边角装饰
    this.createAmbientCornerDecorations()
  }

  createFloatingParticles() {
    // 创建飘落的光点粒子效果
    for (let i = 0; i < 15; i++) {
      setTimeout(() => {
        this.createFloatingParticle()
      }, i * 800)
    }

    // 定期创建新的粒子
    this.time.addEvent({
      delay: 3000,
      callback: () => {
        this.createFloatingParticle()
      },
      loop: true
    })
  }

  createFloatingParticle() {
    const particle = this.add.graphics()
    const colors = [0xFFD700, 0xFFA500, 0xFFE4B5, 0xF0E68C]
    const color = colors[Math.floor(Math.random() * colors.length)]

    particle.fillStyle(color, 0.6)
    particle.fillCircle(0, 0, 2 + Math.random() * 3)

    const startX = Math.random() * 1200
    const startY = -20
    particle.setPosition(startX, startY)

    // 飘落动画
    this.tweens.add({
      targets: particle,
      y: 820,
      x: startX + (Math.random() - 0.5) * 100,
      alpha: 0,
      duration: 8000 + Math.random() * 4000,
      ease: 'Linear',
      onComplete: () => {
        particle.destroy()
      }
    })

    // 旋转动画
    this.tweens.add({
      targets: particle,
      rotation: Math.PI * 2,
      duration: 2000 + Math.random() * 2000,
      repeat: -1,
      ease: 'Linear'
    })
  }

  createAmbientCornerDecorations() {
    // 创建四个角落的装饰
    const corners = [
      { x: 50, y: 50, rotation: 0 },
      { x: 1150, y: 50, rotation: Math.PI / 2 },
      { x: 1150, y: 750, rotation: Math.PI },
      { x: 50, y: 750, rotation: -Math.PI / 2 }
    ]

    corners.forEach(corner => {
      const decoration = this.add.graphics()
      decoration.fillStyle(0xFFD700, 0.3)
      decoration.lineStyle(2, 0xDAA520, 0.8)

      // 创建装饰图案
      decoration.fillTriangle(0, -20, -15, 10, 15, 10)
      decoration.strokeTriangle(0, -20, -15, 10, 15, 10)
      decoration.fillCircle(0, -5, 8)
      decoration.strokeCircle(0, -5, 8)

      decoration.setPosition(corner.x, corner.y)
      decoration.setRotation(corner.rotation)

      // 添加缓慢的脉冲动画
      this.tweens.add({
        targets: decoration,
        alpha: 0.1,
        duration: 3000,
        yoyo: true,
        repeat: -1,
        ease: 'Sine.easeInOut'
      })
    })
  }

  createGameBorder() {
    // 创建游戏边框装饰
    const borderGraphics = this.add.graphics()

    // 外边框 - 金色
    borderGraphics.lineStyle(4, 0xFFD700, 1)
    borderGraphics.strokeRoundedRect(15, 15, 1170, 770, 20)

    // 内边框 - 深色
    borderGraphics.lineStyle(2, 0x8B4513, 1)
    borderGraphics.strokeRoundedRect(20, 20, 1160, 760, 18)

    // 角落装饰
    this.createCornerDecorations()
  }

  createCornerDecorations() {
    // 在四个角落添加装饰图案
    const corners = [
      { x: 40, y: 40 },      // 左上
      { x: 1160, y: 40 },    // 右上
      { x: 40, y: 760 },     // 左下
      { x: 1160, y: 760 }    // 右下
    ]

    corners.forEach((corner) => {
      const decoration = this.add.graphics()
      decoration.fillStyle(0xFFD700, 0.8)
      decoration.fillCircle(corner.x, corner.y, 10)
      decoration.lineStyle(2, 0x8B4513, 1)
      decoration.strokeCircle(corner.x, corner.y, 10)
    })
  }

  createAncientPatterns() {
    // 创建古代纹样装饰
    const patterns = this.add.graphics()

    // 四角装饰纹样
    const corners = [
      {x: 50, y: 50}, {x: 750, y: 50},
      {x: 50, y: 550}, {x: 750, y: 550}
    ]

    corners.forEach(corner => {
      patterns.lineStyle(3, 0xFFD700, 0.8)
      patterns.strokeRect(corner.x - 30, corner.y - 30, 60, 60)
      patterns.lineStyle(2, 0xFF6347, 0.6)
      patterns.strokeRect(corner.x - 20, corner.y - 20, 40, 40)

      // 中心装饰
      patterns.fillStyle(0xFFD700, 0.7)
      patterns.fillCircle(corner.x, corner.y, 8)
    })

    // 顶部和底部边框装饰
    patterns.lineStyle(4, 0xFFD700, 0.9)
    patterns.moveTo(100, 20)
    patterns.lineTo(700, 20)
    patterns.moveTo(100, 580)
    patterns.lineTo(700, 580)
    patterns.strokePath()
  }

  createCloudDecorations() {
    // 添加卡通云朵装饰
    const clouds = [
      {x: 150, y: 80, scale: 0.8},
      {x: 650, y: 100, scale: 0.6},
      {x: 100, y: 500, scale: 0.7},
      {x: 700, y: 520, scale: 0.5}
    ]

    clouds.forEach(cloud => {
      this.createCloud(cloud.x, cloud.y, cloud.scale)
    })
  }

  createCloud(x, y, scale) {
    const cloud = this.add.graphics()
    cloud.fillStyle(0xFFFFFF, 0.3)

    // 云朵主体
    cloud.fillCircle(x, y, 20 * scale)
    cloud.fillCircle(x - 15 * scale, y + 5 * scale, 15 * scale)
    cloud.fillCircle(x + 15 * scale, y + 5 * scale, 15 * scale)
    cloud.fillCircle(x - 8 * scale, y - 8 * scale, 12 * scale)
    cloud.fillCircle(x + 8 * scale, y - 8 * scale, 12 * scale)
  }



  createPlayerPositions() {
    // 标准斗地主布局 - 左右玩家垂直对齐
    const positions = [
      { x: 600, y: 720, name: '农民', position: 'bottom', playerId: 1 },
      { x: 80, y: 300, name: '地主', position: 'left', playerId: 2 },  // 左侧玩家
      { x: 1120, y: 300, name: '农民', position: 'right', playerId: 3 }  // 右侧玩家与左侧对齐
    ]

    positions.forEach((pos, index) => {
      // 创建简化的玩家头像区域
      this.createSimplePlayerAvatar(pos, index + 1)
    })
  }

  createSimplePlayerAvatar(pos, playerNum) {
    // 创建玩家信息容器
    const playerContainer = this.add.container(pos.x, pos.y)

    // 简化的头像背景 - 类似参考图
    const avatarBg = this.add.graphics()
    avatarBg.fillStyle(0xFFFFFF, 1)
    avatarBg.lineStyle(2, 0x666666, 1)
    avatarBg.fillCircle(0, 0, 30)
    avatarBg.strokeCircle(0, 0, 30)
    playerContainer.add(avatarBg)

    // 玩家头像 - 简化版本
    const avatar = this.add.image(0, 0, `avatar_${playerNum}`)
    avatar.setDisplaySize(50, 50)
    avatar.setOrigin(0.5, 0.5)
    playerContainer.add(avatar)

    // 玩家名称 - 简化显示
    const nameText = this.add.text(0, 45, pos.name, {
      fontSize: '12px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5, 0.5)
    playerContainer.add(nameText)

    // 手牌数量显示 - 简化版本
    const cardCountBg = this.add.graphics()
    cardCountBg.fillStyle(0xFF4444, 1)
    cardCountBg.lineStyle(1, 0xFFFFFF, 1)
    cardCountBg.fillCircle(25, -25, 12)
    cardCountBg.strokeCircle(25, -25, 12)
    playerContainer.add(cardCountBg)

    const cardCountText = this.add.text(25, -25, '17', {
      fontSize: '10px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5, 0.5)
    playerContainer.add(cardCountText)

    // 金币显示 - 类似参考图
    if (pos.position === 'bottom') {
      const coinBg = this.add.graphics()
      coinBg.fillStyle(0xFFD700, 1)
      coinBg.lineStyle(1, 0xFFA500, 1)
      coinBg.fillRoundedRect(-40, -50, 80, 20, 10)
      coinBg.strokeRoundedRect(-40, -50, 80, 20, 10)
      playerContainer.add(coinBg)

      const coinText = this.add.text(0, -40, '2900', {
        fontSize: '12px',
        color: '#000000',
        fontFamily: 'Arial',
        fontWeight: 'bold'
      }).setOrigin(0.5, 0.5)
      playerContainer.add(coinText)
    }

    // 添加玩家等级显示
    const levelBg = this.add.graphics()
    levelBg.fillStyle(0x4169E1, 0.9)
    levelBg.lineStyle(2, 0xFFD700, 1)
    levelBg.fillRoundedRect(-45, -55, 30, 20, 10)
    levelBg.strokeRoundedRect(-45, -55, 30, 20, 10)
    playerContainer.add(levelBg)

    const levelText = this.add.text(-30, -45, 'LV.1', {
      fontSize: '12px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5, 0.5)
    playerContainer.add(levelText)

    // 添加在线状态指示器
    const statusIndicator = this.add.graphics()
    statusIndicator.fillStyle(0x00FF00, 1)
    statusIndicator.fillCircle(40, 40, 8)
    statusIndicator.lineStyle(2, 0xFFFFFF, 1)
    statusIndicator.strokeCircle(40, 40, 8)
    playerContainer.add(statusIndicator)

    // 存储引用以便后续更新
    this.uiElements[`player${playerNum}Container`] = playerContainer
    this.uiElements[`player${playerNum}CardCount`] = cardCountText
    this.uiElements[`player${playerNum}Name`] = nameText
    this.uiElements[`player${playerNum}Level`] = levelText
    this.uiElements[`player${playerNum}Status`] = statusIndicator

    // 设置手牌区域
    this.playerHandAreas[pos.position] = {
      x: pos.x,
      y: pos.y,
      cards: []
    }
  }



  createGameUI() {
    // 创建现代化游戏状态显示
    const statusContainer = this.add.container(600, 100)

    // 状态背景
    const statusBg = this.add.graphics()
    statusBg.fillStyle(0x000000, 0.7)
    statusBg.lineStyle(2, 0xFFD700, 1)
    statusBg.fillRoundedRect(-150, -25, 300, 50, 25)
    statusBg.strokeRoundedRect(-150, -25, 300, 50, 25)
    statusContainer.add(statusBg)

    // 游戏状态文字
    this.uiElements.gamePhase = this.add.text(0, 0, '等待开始...', {
      fontSize: '22px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)
    statusContainer.add(this.uiElements.gamePhase)





    // 创建操作按钮区域
    this.createModernActionButtons()
  }

  createTimerDisplay() {
    // 创建计时器容器 - 位置调整到右上角
    const timerContainer = this.add.container(1100, 100)

    // 创建圆形计时器背景
    const timerBg = this.add.graphics()

    // 外圈 - 深色边框
    timerBg.lineStyle(4, 0x333333, 1)
    timerBg.strokeCircle(0, 0, 35)

    // 内圈 - 渐变背景
    timerBg.fillStyle(0x1a1a1a, 0.9)
    timerBg.fillCircle(0, 0, 32)

    // 内边框 - 金色装饰
    timerBg.lineStyle(2, 0xFFD700, 0.8)
    timerBg.strokeCircle(0, 0, 28)

    timerContainer.add(timerBg)

    // 创建进度圆环（用于显示倒计时进度）
    this.uiElements.timerProgress = this.add.graphics()
    timerContainer.add(this.uiElements.timerProgress)

    // 计时器数字
    this.uiElements.timer = this.add.text(0, 0, '30', {
      fontSize: '20px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold',
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)
    timerContainer.add(this.uiElements.timer)

    // 添加计时器图标（时钟符号）
    const clockIcon = this.add.text(0, -15, '⏰', {
      fontSize: '12px'
    }).setOrigin(0.5)
    timerContainer.add(clockIcon)

    this.uiElements.timerContainer = timerContainer
    timerContainer.setVisible(false)
  }

  // 移除游戏信息面板，保持界面简洁

  createPlayHistoryPanel() {
    // 创建出牌历史面板
    const historyContainer = this.add.container(600, 250)

    // 历史面板背景
    const historyBg = this.add.graphics()
    historyBg.fillStyle(0x000000, 0.6)
    historyBg.lineStyle(1, 0x888888, 1)
    historyBg.fillRoundedRect(-200, -40, 400, 80, 10)
    historyBg.strokeRoundedRect(-200, -40, 400, 80, 10)
    historyContainer.add(historyBg)

    // 历史标题
    const historyTitle = this.add.text(0, -15, '上次出牌', {
      fontSize: '12px',
      color: '#CCCCCC',
      fontFamily: 'Arial'
    }).setOrigin(0.5)
    historyContainer.add(historyTitle)

    // 历史内容
    this.uiElements.playHistory = this.add.text(0, 5, '暂无', {
      fontSize: '14px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)
    historyContainer.add(this.uiElements.playHistory)

    this.uiElements.historyContainer = historyContainer
    historyContainer.setVisible(false)
  }

  createModernActionButtons() {
    // 按钮放在底部中央区域，类似参考图布局
    console.log('🔘 创建游戏按钮')
    const buttonY = 520
    const centerX = 600

    // 开始游戏按钮 - 居中显示
    console.log('🎮 创建标准开始游戏按钮')
    this.uiElements.startButton = this.createStandardButton(centerX, buttonY, '开始游戏', 'orange', () => this.startNewGame())
    console.log('✅ 开始游戏按钮创建完成:', this.uiElements.startButton)

    // 叫地主按钮
    this.uiElements.bidLandlordButton = this.createStandardButton(centerX - 80, buttonY, '叫地主', 'orange', () => this.bidLandlord())
    this.uiElements.bidLandlordButton.setVisible(false)

    // 不叫按钮
    this.uiElements.passBidButton = this.createStandardButton(centerX + 80, buttonY, '不出', 'blue', () => this.passBid())
    this.uiElements.passBidButton.setVisible(false)

    // 出牌按钮
    this.uiElements.playCardsButton = this.createStandardButton(centerX - 80, buttonY, '出牌', 'orange', () => this.playSelectedCards())
    this.uiElements.playCardsButton.setVisible(false)

    // 过牌按钮
    this.uiElements.passButton = this.createStandardButton(centerX + 80, buttonY, '不出', 'blue', () => this.passCards())
    this.uiElements.passButton.setVisible(false)

    // 提示按钮
    this.uiElements.hintButton = this.createStandardButton(centerX, buttonY + 50, '提示', 'blue', () => this.showHint())
    this.uiElements.hintButton.setVisible(false)
  }

  createButtonAreaDecoration(centerX, centerY) {
    // 创建按钮区域的装饰背景
    const decorBg = this.add.graphics()

    // 半透明背景
    decorBg.fillStyle(0x000000, 0.3)
    decorBg.fillRoundedRect(centerX - 100, centerY - 60, 200, 120, 15)

    // 装饰边框
    decorBg.lineStyle(2, 0xFFD700, 0.6)
    decorBg.strokeRoundedRect(centerX - 100, centerY - 60, 200, 120, 15)

    // 内边框
    decorBg.lineStyle(1, 0xFFFFFF, 0.3)
    decorBg.strokeRoundedRect(centerX - 98, centerY - 58, 196, 116, 13)

    // 角落装饰
    const corners = [
      { x: centerX - 90, y: centerY - 50 },
      { x: centerX + 90, y: centerY - 50 },
      { x: centerX - 90, y: centerY + 50 },
      { x: centerX + 90, y: centerY + 50 }
    ]

    corners.forEach(corner => {
      decorBg.fillStyle(0xFFD700, 0.4)
      decorBg.fillCircle(corner.x, corner.y, 4)
    })
  }

  createImageButton(x, y, imageKey, text, callback) {
    // 创建现代化的游戏按钮
    const buttonContainer = this.add.container(x, y)

    // 按钮阴影
    const shadow = this.add.graphics()
    shadow.fillStyle(0x000000, 0.4)
    shadow.fillRoundedRect(-55, -22, 110, 44, 22)
    buttonContainer.add(shadow)

    // 按钮背景 - 如果有图片资源则使用，否则创建现代风格按钮
    if (this.cache.image.exists(imageKey)) {
      const buttonImage = this.add.image(-2, -2, imageKey)
      buttonImage.setDisplaySize(110, 44)
      buttonContainer.add(buttonImage)
    } else {
      // 创建现代风格按钮背景
      const buttonBg = this.add.graphics()

      // 渐变背景
      buttonBg.fillGradientStyle(0x4CAF50, 0x4CAF50, 0x45A049, 0x45A049, 1)
      buttonBg.fillRoundedRect(-52, -20, 104, 40, 20)

      // 外边框
      buttonBg.lineStyle(2, 0xFFD700, 1)
      buttonBg.strokeRoundedRect(-52, -20, 104, 40, 20)

      // 内边框高光
      buttonBg.lineStyle(1, 0xFFFFFF, 0.6)
      buttonBg.strokeRoundedRect(-50, -18, 100, 36, 18)

      buttonContainer.add(buttonBg)
    }

    // 按钮文字 - 更现代的样式
    if (text && text !== '') {
      const buttonText = this.add.text(0, 0, text, {
        fontSize: '16px',
        color: '#FFFFFF',
        fontFamily: 'Arial',
        fontWeight: 'bold',
        stroke: '#000000',
        strokeThickness: 2
      }).setOrigin(0.5)
      buttonContainer.add(buttonText)
    }

    // 设置交互
    buttonContainer.setSize(110, 44)
    buttonContainer.setInteractive()

    // 添加点击效果
    buttonContainer.on('pointerdown', () => {
      buttonContainer.setScale(0.95)
      // 播放点击音效
      this.playSound('chupaiSound', 0.3)

      // 添加点击粒子效果
      this.createButtonClickEffect(x, y)

      callback()

      // 恢复按钮样式
      setTimeout(() => {
        buttonContainer.setScale(1)
      }, 150)
    })

    // 添加悬停效果
    buttonContainer.on('pointerover', () => {
      buttonContainer.setScale(1.05)
      buttonContainer.setTint(0xDDDDDD)

      // 添加悬停光晕效果
      this.createButtonHoverEffect(buttonContainer)
    })

    buttonContainer.on('pointerout', () => {
      buttonContainer.setScale(1)
      buttonContainer.clearTint()

      // 清除悬停效果
      this.clearButtonHoverEffect(buttonContainer)
    })

    return buttonContainer
  }

  createStandardButton(x, y, text, colorType, callback) {
    // 创建标准斗地主按钮样式，类似参考图
    console.log(`🔘 创建按钮: ${text} 位置:(${x}, ${y})`)
    const buttonContainer = this.add.container(x, y)

    // 按钮颜色配置
    const colors = {
      blue: { bg: 0x2563EB, border: 0x1D4ED8, text: '#FFFFFF' },
      orange: { bg: 0xF97316, border: 0xEA580C, text: '#FFFFFF' }
    }

    const color = colors[colorType] || colors.blue

    // 按钮阴影
    const shadow = this.add.graphics()
    shadow.fillStyle(0x000000, 0.3)
    shadow.fillRoundedRect(-32, -12, 64, 24, 12)
    buttonContainer.add(shadow)

    // 按钮背景
    const buttonBg = this.add.graphics()
    buttonBg.fillStyle(color.bg, 1)
    buttonBg.fillRoundedRect(-30, -10, 60, 20, 10)
    buttonBg.lineStyle(1, color.border, 1)
    buttonBg.strokeRoundedRect(-30, -10, 60, 20, 10)
    buttonContainer.add(buttonBg)

    // 按钮文字
    const buttonText = this.add.text(0, 0, text, {
      fontSize: '14px',
      color: color.text,
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)
    buttonContainer.add(buttonText)

    // 设置交互 - 增大交互区域
    buttonContainer.setSize(80, 40)
    buttonContainer.setInteractive()

    // 添加点击效果
    buttonContainer.on('pointerdown', () => {
      console.log(`🖱️ 按钮被点击: ${text}`)
      console.log('🔄 执行按钮回调函数')
      buttonContainer.setScale(0.95)

      try {
        callback()
        console.log('✅ 按钮回调执行成功')
      } catch (error) {
        console.error('❌ 按钮回调执行失败:', error)
      }

      setTimeout(() => {
        buttonContainer.setScale(1)
      }, 100)
    })

    // 添加悬停效果
    buttonContainer.on('pointerover', () => {
      buttonContainer.setScale(1.05)
    })

    buttonContainer.on('pointerout', () => {
      buttonContainer.setScale(1)
    })

    return buttonContainer
  }

  createButtonClickEffect(x, y) {
    // 创建按钮点击粒子效果
    for (let i = 0; i < 8; i++) {
      const particle = this.add.graphics()
      particle.fillStyle(0xFFD700, 1)
      particle.fillCircle(x, y, 3)

      const angle = (i / 8) * Math.PI * 2
      const distance = 30 + Math.random() * 20
      const targetX = x + Math.cos(angle) * distance
      const targetY = y + Math.sin(angle) * distance

      this.tweens.add({
        targets: particle,
        x: targetX,
        y: targetY,
        alpha: 0,
        scaleX: 0.1,
        scaleY: 0.1,
        duration: 400,
        ease: 'Power2',
        onComplete: () => {
          particle.destroy()
        }
      })
    }
  }

  createButtonHoverEffect(buttonContainer) {
    // 创建按钮悬停光晕效果
    if (buttonContainer.hoverGlow) {
      buttonContainer.hoverGlow.destroy()
    }

    const glow = this.add.graphics()
    glow.lineStyle(3, 0xFFD700, 0.6)
    glow.strokeRoundedRect(-55, -22, 110, 44, 22)
    glow.lineStyle(2, 0xFFFFFF, 0.4)
    glow.strokeRoundedRect(-53, -20, 106, 40, 20)

    buttonContainer.add(glow)
    buttonContainer.hoverGlow = glow

    // 添加脉冲动画
    this.tweens.add({
      targets: glow,
      alpha: 0.3,
      duration: 800,
      yoyo: true,
      repeat: -1,
      ease: 'Sine.easeInOut'
    })
  }

  clearButtonHoverEffect(buttonContainer) {
    // 清除按钮悬停效果
    if (buttonContainer.hoverGlow) {
      buttonContainer.hoverGlow.destroy()
      buttonContainer.hoverGlow = null
    }
  }

  startNewGame() {
    console.log('🎮 开始新游戏')

    try {
      // 播放发牌音效
      this.playSound('fapaiSound', 0.4)

      console.log('📋 调用 gameState.startGame()')
      this.gameState.startGame()

      // 立即显示手牌，确保发牌后能看到牌
      setTimeout(() => {
        console.log('🃏 开始显示手牌')

        try {
          // 检查游戏状态
          const gameInfo = this.gameState.getGameInfo()
          console.log('🎮 当前游戏状态:', gameInfo)

          // 检查所有玩家
          console.log('👥 检查所有玩家:')
          for (let i = 1; i <= 3; i++) {
            const player = this.gameState.getPlayer(i)
            console.log(`👤 玩家${i}:`, player)
            if (player && player.hand) {
              console.log(`🎴 玩家${i}手牌数量:`, player.hand.getCards().length)
            }
          }



          this.displayPlayerCards()
          console.log('🔄 更新UI')
          this.updateUI()
        } catch (error) {
          console.error('❌ 显示手牌时出错:', error)
        }
      }, 100)
    } catch (error) {
      console.error('❌ 开始游戏时出错:', error)
    }
  }

  updateUI() {
    const gameInfo = this.gameState.getGameInfo()
    console.log('更新UI，当前游戏状态:', gameInfo)

    // 更新游戏阶段显示
    const phaseTexts = {
      'waiting': '等待开始...',
      'dealing': '发牌中...',
      'bidding': '叫牌阶段',
      'playing': '游戏进行中',
      'finished': '游戏结束'
    }
    this.uiElements.gamePhase.setText(phaseTexts[gameInfo.phase] || gameInfo.phase)

    // 更新玩家手牌数量
    gameInfo.players.forEach((player, index) => {
      const playerNum = index + 1
      const cardCountElement = this.uiElements[`player${playerNum}CardCount`]
      if (cardCountElement) {
        cardCountElement.setText(player.cardCount.toString())

        // 如果是地主，显示特殊标识
        if (player.isLandlord) {
          cardCountElement.setStyle({ color: '#FF0000' })
          // 添加地主标识
          this.showLandlordIndicator(playerNum)
        }
      }
    })

    // 更新当前玩家指示器
    this.updateCurrentPlayerIndicator(gameInfo.currentPlayer)

    // 更新按钮显示
    this.updateActionButtons(gameInfo)

    // 更新计时器
    this.updateTimer(gameInfo)



    // 更新出牌历史
    this.updatePlayHistory(gameInfo)
  }

  showLandlordIndicator(playerNum) {
    // 如果已经有地主标识，先清除
    if (this.uiElements[`landlord${playerNum}`]) {
      this.uiElements[`landlord${playerNum}`].destroy()
    }

    // 创建地主标识容器
    const playerContainer = this.uiElements[`player${playerNum}Container`]
    if (playerContainer) {
      const landlordContainer = this.add.container(-35, -35)

      // 地主标识背景
      const landlordBg = this.add.graphics()
      landlordBg.fillStyle(0xFF0000, 0.9)
      landlordBg.lineStyle(2, 0xFFD700, 1)
      landlordBg.fillCircle(0, 0, 20)
      landlordBg.strokeCircle(0, 0, 20)
      landlordContainer.add(landlordBg)

      // 地主图标或文字
      if (this.cache && this.cache.image && this.cache.image.exists && this.cache.image.exists('img_Card_dizhu')) {
        const landlordIcon = this.add.image(0, 0, 'img_Card_dizhu')
        landlordIcon.setDisplaySize(32, 32)
        landlordContainer.add(landlordIcon)
      } else {
        const landlordText = this.add.text(0, 0, '地\n主', {
          fontSize: '12px',
          color: '#FFFFFF',
          fontFamily: 'Arial',
          fontWeight: 'bold',
          align: 'center',
          lineSpacing: -2
        }).setOrigin(0.5)
        landlordContainer.add(landlordText)
      }

      // 添加闪烁效果
      this.tweens.add({
        targets: landlordContainer,
        scaleX: 1.1,
        scaleY: 1.1,
        duration: 800,
        yoyo: true,
        repeat: 2,
        ease: 'Sine.easeInOut'
      })

      playerContainer.add(landlordContainer)
      this.uiElements[`landlord${playerNum}`] = landlordContainer
    }
  }

  updateCurrentPlayerIndicator(currentPlayerIndex) {
    // 清除所有玩家的当前指示器
    for (let i = 1; i <= 3; i++) {
      if (this.uiElements[`currentIndicator${i}`]) {
        this.uiElements[`currentIndicator${i}`].setVisible(false)
      }
    }

    // 显示当前玩家的指示器
    const playerNum = currentPlayerIndex + 1
    if (!this.uiElements[`currentIndicator${playerNum}`]) {
      const playerContainer = this.uiElements[`player${playerNum}Container`]
      if (playerContainer) {
        const indicator = this.add.graphics()

        // 创建多层指示器效果
        indicator.lineStyle(5, 0x00FF00, 0.8)
        indicator.strokeCircle(0, 0, 65)
        indicator.lineStyle(3, 0x32CD32, 0.9)
        indicator.strokeCircle(0, 0, 60)
        indicator.lineStyle(2, 0x90EE90, 1)
        indicator.strokeCircle(0, 0, 55)

        playerContainer.add(indicator)
        this.uiElements[`currentIndicator${playerNum}`] = indicator

        // 添加旋转和脉冲效果
        this.tweens.add({
          targets: indicator,
          rotation: Math.PI * 2,
          duration: 2000,
          repeat: -1,
          ease: 'Linear'
        })

        this.tweens.add({
          targets: indicator,
          alpha: 0.4,
          scaleX: 1.1,
          scaleY: 1.1,
          duration: 1000,
          yoyo: true,
          repeat: -1,
          ease: 'Sine.easeInOut'
        })
      }
    } else {
      this.uiElements[`currentIndicator${playerNum}`].setVisible(true)
    }
  }

  updateTimer(gameInfo) {
    if (gameInfo.phase === 'bidding' || gameInfo.phase === 'playing') {
      if (this.uiElements.timerContainer) {
        this.uiElements.timerContainer.setVisible(true)

        // 初始化计时器
        if (!this.gameTimer) {
          this.gameTimer = 30
          this.maxTime = 30
        }

        if (this.uiElements.timer) {
          this.uiElements.timer.setText(this.gameTimer.toString())

          // 更新进度圆环
          this.updateTimerProgress(this.gameTimer, this.maxTime)

          // 时间不足时变红并添加警告效果
          if (this.gameTimer <= 10) {
            this.uiElements.timer.setStyle({
              color: '#FF0000',
              fontSize: '22px' // 时间紧急时字体稍大
            })

            // 添加紧急闪烁效果
            if (this.gameTimer <= 5) {
              this.tweens.add({
                targets: this.uiElements.timer,
                alpha: 0.3,
                duration: 300,
                yoyo: true,
                repeat: 1,
                ease: 'Power2'
              })
            }
          } else {
            this.uiElements.timer.setStyle({
              color: '#FFFFFF',
              fontSize: '20px'
            })
          }
        }
      }
    } else {
      if (this.uiElements.timerContainer) {
        this.uiElements.timerContainer.setVisible(false)
      }
    }
  }

  updateTimerProgress(currentTime, maxTime) {
    if (!this.uiElements.timerProgress) return

    // 清除之前的进度圆环
    this.uiElements.timerProgress.clear()

    // 计算进度百分比
    const progress = currentTime / maxTime
    const angle = progress * 2 * Math.PI

    // 根据剩余时间选择颜色
    let progressColor = 0x00FF00 // 绿色
    if (progress < 0.5) {
      progressColor = 0xFFFF00 // 黄色
    }
    if (progress < 0.3) {
      progressColor = 0xFF6600 // 橙色
    }
    if (progress < 0.2) {
      progressColor = 0xFF0000 // 红色
    }

    // 绘制进度圆环
    this.uiElements.timerProgress.lineStyle(4, progressColor, 0.8)
    this.uiElements.timerProgress.beginPath()
    this.uiElements.timerProgress.arc(0, 0, 25, -Math.PI/2, -Math.PI/2 + angle, false)
    this.uiElements.timerProgress.strokePath()

    // 添加进度圆环的光晕效果
    if (progress < 0.3) {
      this.uiElements.timerProgress.lineStyle(2, progressColor, 0.4)
      this.uiElements.timerProgress.strokeCircle(0, 0, 27)
    }
  }

  updateActionButtons(gameInfo) {
    console.log('🔄 更新按钮显示，游戏阶段:', gameInfo.phase, '当前玩家:', gameInfo.currentPlayer)

    // 隐藏所有游戏按钮
    const gameButtons = [
      'startButton', 'bidLandlordButton', 'passBidButton',
      'playCardsButton', 'passButton', 'hintButton'
    ]

    gameButtons.forEach(buttonName => {
      if (this.uiElements[buttonName]) {
        console.log(`🔘 隐藏按钮: ${buttonName}`)
        this.uiElements[buttonName].setVisible(false)
      }
    })

    if (gameInfo.phase === 'waiting') {
      console.log('🎮 显示开始游戏按钮')
      if (this.uiElements.startButton) {
        console.log('✅ 开始游戏按钮存在，设置为可见')
        this.uiElements.startButton.setVisible(true)
      } else {
        console.log('❌ 开始游戏按钮不存在！')
      }
    } else if (gameInfo.phase === 'bidding' && gameInfo.currentPlayer === 0) {
      // 轮到玩家1叫牌
      console.log('显示叫地主按钮')
      if (this.uiElements.bidLandlordButton) {
        this.uiElements.bidLandlordButton.setVisible(true)
        this.addButtonPulseEffect(this.uiElements.bidLandlordButton)
      }

      // 确保在叫牌阶段显示手牌
      console.log('🃏 叫牌阶段 - 确保显示手牌')
      this.displayPlayerCards()
      if (this.uiElements.passBidButton) {
        this.uiElements.passBidButton.setVisible(true)
        this.addButtonPulseEffect(this.uiElements.passBidButton)
      }
    } else if (gameInfo.phase === 'playing' && gameInfo.currentPlayer === 0) {
      // 轮到玩家1出牌
      console.log('显示出牌按钮')
      if (this.uiElements.playCardsButton) {
        this.uiElements.playCardsButton.setVisible(true)
        this.addButtonPulseEffect(this.uiElements.playCardsButton)
      }
      if (this.uiElements.passButton) {
        this.uiElements.passButton.setVisible(true)
      }
      if (this.uiElements.hintButton) {
        this.uiElements.hintButton.setVisible(true)
      }
    }
  }

  addButtonPulseEffect(button) {
    // 为按钮添加脉冲效果
    this.tweens.add({
      targets: button,
      scaleX: 1.1,
      scaleY: 1.1,
      duration: 600,
      yoyo: true,
      repeat: -1,
      ease: 'Sine.easeInOut'
    })
  }

  displayPlayerCards() {
    console.log('🃏 开始显示所有玩家手牌')

    // 显示玩家1（底部）的手牌
    const player1 = this.gameState.getPlayer(1)
    console.log('👤 玩家1信息:', player1)
    if (player1 && player1.hand) {
      const cards = player1.hand.getCards()
      console.log('🎴 玩家1手牌数量:', cards.length)
      console.log('🎴 手牌详情:', cards.map(card => card.getDisplayName()))
      this.displayBottomPlayerCards(cards)
    } else {
      console.log('❌ 玩家1或手牌不存在')
    }

    // 显示玩家2（左侧）的手牌背面
    const player2 = this.gameState.getPlayer(2)
    console.log('👤 玩家2信息:', player2)
    if (player2 && player2.hand) {
      const cardCount = player2.hand.getCards().length
      console.log('🎴 玩家2手牌数量:', cardCount)
      this.displayLeftPlayerCards(cardCount)
    }

    // 显示玩家3（右侧）的手牌背面
    const player3 = this.gameState.getPlayer(3)
    console.log('👤 玩家3信息:', player3)
    if (player3 && player3.hand) {
      const cardCount = player3.hand.getCards().length
      console.log('🎴 玩家3手牌数量:', cardCount)
      this.displayRightPlayerCards(cardCount)
    }
  }

  displayBottomPlayerCards(cards) {
    console.log(`🎴 显示底部玩家手牌，数量: ${cards.length}`)

    // 确保手牌区域已初始化
    if (!this.playerHandAreas.bottom) {
      this.playerHandAreas.bottom = { cards: [] }
      console.log('🔧 初始化底部手牌区域')
    }

    // 清除之前的卡牌
    console.log(`🗑️ 清除之前的 ${this.playerHandAreas.bottom.cards.length} 张卡牌`)
    this.playerHandAreas.bottom.cards.forEach(cardSprite => {
      cardSprite.destroy()
    })
    this.playerHandAreas.bottom.cards = []

    // 修复手牌布局 - 确保卡牌整齐排列
    const totalCards = cards.length
    const gameWidth = 1200 // 游戏画布宽度
    const cardWidth = 60 // 单张卡牌宽度
    const cardHeight = 84 // 单张卡牌高度

    // 计算重叠间距 - 根据卡牌数量动态调整
    let cardSpacing = 50 // 基础间距
    if (totalCards > 15) {
      cardSpacing = 40 // 卡牌多时减少间距
    } else if (totalCards > 20) {
      cardSpacing = 30 // 卡牌很多时进一步减少间距
    }

    // 计算总宽度和起始位置 - 确保居中
    const totalWidth = (totalCards - 1) * cardSpacing + cardWidth
    const startX = (gameWidth - totalWidth) / 2 + cardWidth / 2
    const y = 650 // 底部位置

    console.log(`📐 布局计算: 总卡牌${totalCards}张, 间距${cardSpacing}px, 总宽度${totalWidth.toFixed(1)}px, 起始X${startX.toFixed(1)}px`)

    console.log(`🎴 开始创建 ${totalCards} 张手牌`)
    cards.forEach((card, index) => {
      const x = startX + index * cardSpacing
      console.log(`🎴 创建第 ${index + 1} 张牌: ${card.getDisplayName()} 位置:(${x.toFixed(1)}, ${y})`)

      const cardSprite = this.createCardSprite(x, y, card)

      // 检查卡牌精灵是否创建成功
      if (cardSprite) {
        console.log(`✅ 卡牌精灵创建成功: ${card.getDisplayName()}`)

        // 调整卡牌层级 - 后面的卡牌层级更高
        cardSprite.setDepth(1000 + index)

        // 确保卡牌可见
        cardSprite.setVisible(true)

        // 检查卡牌位置
        console.log(`📍 卡牌最终位置: (${cardSprite.x}, ${cardSprite.y}), 可见性: ${cardSprite.visible}`)

        this.playerHandAreas.bottom.cards.push(cardSprite)
      } else {
        console.error(`❌ 卡牌精灵创建失败: ${card.getDisplayName()}`)
      }
    })

    console.log(`✅ 手牌显示完成，共 ${this.playerHandAreas.bottom.cards.length} 张`)
  }

  displayLeftPlayerCards(cardCount) {
    console.log(`🎴 显示左侧玩家手牌背面，数量: ${cardCount}`)

    // 确保手牌区域已初始化
    if (!this.playerHandAreas.left) {
      this.playerHandAreas.left = { cards: [] }
    }

    // 清除之前的卡牌
    this.playerHandAreas.left.cards.forEach(cardSprite => {
      cardSprite.destroy()
    })
    this.playerHandAreas.left.cards = []

    // 左侧玩家手牌垂直排列
    const cardWidth = 35 // 侧面卡牌较小
    const cardHeight = 50
    const cardOverlap = 20 // 垂直重叠距离

    const totalHeight = cardHeight + (cardCount - 1) * cardOverlap
    const startY = (700 - totalHeight) / 2 + cardHeight / 2 // 垂直居中
    const x = 150 // 左侧位置，避开头像区域

    for (let i = 0; i < cardCount; i++) {
      const y = startY + i * cardOverlap

      // 创建卡背精灵
      const cardBack = this.createCardBackSprite(x, y, cardWidth, cardHeight)
      if (cardBack) {
        cardBack.setDepth(500 + i)
        cardBack.setRotation(-Math.PI / 2) // 旋转90度
        this.playerHandAreas.left.cards.push(cardBack)
      }
    }

    console.log(`✅ 左侧玩家手牌背面显示完成，共 ${this.playerHandAreas.left.cards.length} 张`)
  }

  displayRightPlayerCards(cardCount) {
    console.log(`🎴 显示右侧玩家手牌背面，数量: ${cardCount}`)

    // 确保手牌区域已初始化
    if (!this.playerHandAreas.right) {
      this.playerHandAreas.right = { cards: [] }
    }

    // 清除之前的卡牌
    this.playerHandAreas.right.cards.forEach(cardSprite => {
      cardSprite.destroy()
    })
    this.playerHandAreas.right.cards = []

    // 右侧玩家手牌垂直排列
    const cardWidth = 35 // 侧面卡牌较小
    const cardHeight = 50
    const cardOverlap = 20 // 垂直重叠距离

    const totalHeight = cardHeight + (cardCount - 1) * cardOverlap
    const startY = (700 - totalHeight) / 2 + cardHeight / 2 // 垂直居中
    const x = 1050 // 右侧位置，避开头像区域

    for (let i = 0; i < cardCount; i++) {
      const y = startY + i * cardOverlap

      // 创建卡背精灵
      const cardBack = this.createCardBackSprite(x, y, cardWidth, cardHeight)
      if (cardBack) {
        cardBack.setDepth(500 + i)
        cardBack.setRotation(Math.PI / 2) // 旋转-90度
        this.playerHandAreas.right.cards.push(cardBack)
      }
    }

    console.log(`✅ 右侧玩家手牌背面显示完成，共 ${this.playerHandAreas.right.cards.length} 张`)
  }

  createCardBackSprite(x, y, width = 60, height = 84) {
    try {
      // 创建卡背图形
      const cardBack = this.add.graphics()

      // 卡牌背景 - 深蓝色渐变
      cardBack.fillGradientStyle(0x1a3e8c, 0x2d5aa0, 0x1a3e8c, 0x2d5aa0, 1)
      cardBack.fillRoundedRect(-width/2, -height/2, width, height, 6)

      // 卡牌边框
      cardBack.lineStyle(2, 0x4a6fa5, 1)
      cardBack.strokeRoundedRect(-width/2, -height/2, width, height, 6)

      // 中央装饰图案
      cardBack.fillStyle(0x6b8bc3, 0.6)
      cardBack.fillCircle(0, 0, Math.min(width, height) * 0.25)

      // 设置位置
      cardBack.x = x
      cardBack.y = y

      return cardBack
    } catch (error) {
      console.error('❌ 创建卡背精灵失败:', error)
      return null
    }
  }



  createCardSprite(x, y, card) {
    console.log(`🎴 创建卡牌精灵: ${card.getDisplayName()} 位置:(${x}, ${y})`)

    const displayWidth = 60   // 显示宽度
    const displayHeight = 84  // 显示高度

    // 创建卡牌容器
    const cardContainer = this.add.container(x, y)

    // 卡牌阴影
    const shadow = this.add.graphics()
    shadow.fillStyle(0x000000, 0.3)
    shadow.fillRoundedRect(-displayWidth/2 + 2, -displayHeight/2 + 2, displayWidth, displayHeight, 8)
    cardContainer.add(shadow)

    // 尝试使用雪碧图显示卡牌
    const spritePos = this.getCardSpritePosition(card)
    if (spritePos && this.cache && this.cache.image && this.cache.image.exists('cardSprite')) {
      console.log(`🎴 使用雪碧图显示卡牌: ${card.getDisplayName()}`, spritePos)

      // 创建雪碧图卡牌
      const cardSprite = this.add.image(0, 0, 'cardSprite')

      // 设置裁剪区域显示特定卡牌
      cardSprite.setCrop(spritePos.x, spritePos.y, spritePos.width, spritePos.height)
      cardSprite.setDisplaySize(displayWidth, displayHeight)

      cardContainer.add(cardSprite)
      console.log(`✅ 雪碧图卡牌创建成功: ${card.getDisplayName()}`)
    } else {
      console.log(`⚠️ 雪碧图不可用，使用备用显示: ${card.getDisplayName()}`)

      // 备用方案：创建简单的卡牌背景
      const cardBg = this.add.graphics()
      cardBg.fillStyle(0xFFFFFF, 1)
      cardBg.lineStyle(2, 0x333333, 1)
      cardBg.fillRoundedRect(-displayWidth/2, -displayHeight/2, displayWidth, displayHeight, 8)
      cardBg.strokeRoundedRect(-displayWidth/2, -displayHeight/2, displayWidth, displayHeight, 8)
      cardContainer.add(cardBg)

      // 卡牌内容 - 按标准扑克牌样式
      const isRed = card.suit === 'hearts' || card.suit === 'diamonds'
      const cardColor = isRed ? '#DC143C' : '#000000'

      // 处理王牌特殊显示
      if (card.suit === 'joker') {
        const jokerColor = card.rank === 'small_joker' ? '#000000' : '#DC143C'
        const jokerText = card.rank === 'small_joker' ? '小王' : '大王'

        // 王牌中央显示
        const jokerLabel = this.add.text(0, 0, jokerText, {
          fontSize: '14px',
          color: jokerColor,
          fontFamily: 'Arial',
          fontWeight: 'bold'
        }).setOrigin(0.5)
        cardContainer.add(jokerLabel)
      } else {
        // 普通牌显示
        // 左上角数字/字母
        const topText = this.add.text(-displayWidth/2 + 4, -displayHeight/2 + 4, card.rank, {
          fontSize: '10px',
          color: cardColor,
          fontFamily: 'Arial',
          fontWeight: 'bold'
        }).setOrigin(0, 0)
        cardContainer.add(topText)

        // 左上角花色
        const topSuit = this.add.text(-displayWidth/2 + 4, -displayHeight/2 + 16, this.getSuitSymbol(card.suit), {
          fontSize: '10px',
          color: cardColor,
          fontFamily: 'Arial'
        }).setOrigin(0, 0)
        cardContainer.add(topSuit)

        // 右下角数字/字母（倒置）
        const bottomText = this.add.text(displayWidth/2 - 4, displayHeight/2 - 4, card.rank, {
          fontSize: '10px',
          color: cardColor,
          fontFamily: 'Arial',
          fontWeight: 'bold'
        }).setOrigin(1, 1).setRotation(Math.PI)
        cardContainer.add(bottomText)

        // 右下角花色（倒置）
        const bottomSuit = this.add.text(displayWidth/2 - 4, displayHeight/2 - 16, this.getSuitSymbol(card.suit), {
          fontSize: '10px',
          color: cardColor,
          fontFamily: 'Arial'
        }).setOrigin(1, 1).setRotation(Math.PI)
        cardContainer.add(bottomSuit)

        // 中央花色图案 - 根据点数显示不同数量的花色
        this.addCenterPattern(cardContainer, card, cardColor, displayWidth, displayHeight)
      }
    }

    // 设置交互
    cardContainer.setSize(displayWidth, displayHeight)
    cardContainer.setInteractive()
    cardContainer.cardData = card
    cardContainer.selected = false
    cardContainer.originalY = y

    // 添加点击事件
    cardContainer.on('pointerdown', () => {
      console.log(`🖱️ 点击了卡牌: ${card.getDisplayName()}`)
      this.selectCard(cardContainer)
      // 播放卡牌音效
      if (this.playSound) {
        this.playSound('cardSound', 0.2)
      }
    })

    // 添加悬停效果
    cardContainer.on('pointerover', () => {
      if (!cardContainer.selected) {
        cardContainer.setScale(1.05)
        cardContainer.setTint(0xdddddd) // 轻微变暗
      }
    })

    cardContainer.on('pointerout', () => {
      if (!cardContainer.selected) {
        cardContainer.setScale(1)
        cardContainer.clearTint() // 恢复原色
      }
    })

    console.log(`✅ 卡牌容器创建成功: ${card.getDisplayName()} 位置:(${x}, ${y})`)
    return cardContainer
  }

  getCardSpritePosition(card) {
    // 卡牌雪碧图映射表
    const CARD_SPRITE_MAP = {
      // 黑桃 (Spades)
      'spades_3': { x: 238, y: 646, width: 116, height: 159 },
      'spades_4': { x: 120, y: 646, width: 116, height: 159 },
      'spades_5': { x: 2, y: 646, width: 116, height: 159 },
      'spades_6': { x: 1418, y: 485, width: 116, height: 159 },
      'spades_7': { x: 1300, y: 485, width: 116, height: 159 },
      'spades_8': { x: 1182, y: 485, width: 116, height: 159 },
      'spades_9': { x: 1064, y: 485, width: 116, height: 159 },
      'spades_10': { x: 946, y: 485, width: 116, height: 159 },
      'spades_J': { x: 828, y: 485, width: 116, height: 159 }, // J
      'spades_Q': { x: 710, y: 485, width: 116, height: 159 }, // Q
      'spades_K': { x: 592, y: 485, width: 116, height: 159 }, // K
      'spades_A': { x: 474, y: 485, width: 116, height: 159 }, // A
      'spades_2': { x: 356, y: 485, width: 116, height: 159 }, // 2

      // 红桃 (Hearts)
      'hearts_3': { x: 238, y: 485, width: 116, height: 159 },
      'hearts_4': { x: 120, y: 485, width: 116, height: 159 },
      'hearts_5': { x: 2, y: 485, width: 116, height: 159 },
      'hearts_6': { x: 1418, y: 324, width: 116, height: 159 },
      'hearts_7': { x: 1300, y: 324, width: 116, height: 159 },
      'hearts_8': { x: 1182, y: 324, width: 116, height: 159 },
      'hearts_9': { x: 1064, y: 324, width: 116, height: 159 },
      'hearts_10': { x: 946, y: 324, width: 116, height: 159 },
      'hearts_J': { x: 828, y: 324, width: 116, height: 159 }, // J
      'hearts_Q': { x: 710, y: 324, width: 116, height: 159 }, // Q
      'hearts_K': { x: 592, y: 324, width: 116, height: 159 }, // K
      'hearts_A': { x: 474, y: 324, width: 116, height: 159 }, // A
      'hearts_2': { x: 356, y: 324, width: 116, height: 159 }, // 2

      // 梅花 (Clubs)
      'clubs_3': { x: 238, y: 324, width: 116, height: 159 },
      'clubs_4': { x: 120, y: 324, width: 116, height: 159 },
      'clubs_5': { x: 2, y: 324, width: 116, height: 159 },
      'clubs_6': { x: 1418, y: 163, width: 116, height: 159 },
      'clubs_7': { x: 1300, y: 163, width: 116, height: 159 },
      'clubs_8': { x: 1182, y: 163, width: 116, height: 159 },
      'clubs_9': { x: 1064, y: 163, width: 116, height: 159 },
      'clubs_10': { x: 946, y: 163, width: 116, height: 159 },
      'clubs_J': { x: 828, y: 163, width: 116, height: 159 }, // J
      'clubs_Q': { x: 710, y: 163, width: 116, height: 159 }, // Q
      'clubs_K': { x: 592, y: 163, width: 116, height: 159 }, // K
      'clubs_A': { x: 474, y: 163, width: 116, height: 159 }, // A
      'clubs_2': { x: 356, y: 163, width: 116, height: 159 }, // 2

      // 方块 (Diamonds)
      'diamonds_3': { x: 238, y: 163, width: 116, height: 159 },
      'diamonds_4': { x: 120, y: 163, width: 116, height: 159 },
      'diamonds_5': { x: 2, y: 163, width: 116, height: 159 },
      'diamonds_6': { x: 1418, y: 2, width: 116, height: 159 },
      'diamonds_7': { x: 1300, y: 2, width: 116, height: 159 },
      'diamonds_8': { x: 1182, y: 2, width: 116, height: 159 },
      'diamonds_9': { x: 1064, y: 2, width: 116, height: 159 },
      'diamonds_10': { x: 946, y: 2, width: 116, height: 159 },
      'diamonds_J': { x: 828, y: 2, width: 116, height: 159 }, // J
      'diamonds_Q': { x: 710, y: 2, width: 116, height: 159 }, // Q
      'diamonds_K': { x: 592, y: 2, width: 116, height: 159 }, // K
      'diamonds_A': { x: 474, y: 2, width: 116, height: 159 }, // A
      'diamonds_2': { x: 356, y: 2, width: 116, height: 159 }, // 2

      // 王牌
      'joker_16': { x: 238, y: 2, width: 116, height: 159 }, // 小王
      'joker_17': { x: 120, y: 2, width: 116, height: 159 }, // 大王
    }

    let key
    if (card.suit === 'joker') {
      // 王牌：小王=16，大王=17
      const jokerValue = card.rank === 'small_joker' ? 16 : 17
      key = `joker_${jokerValue}`
    } else {
      // 普通牌：使用suit和rank
      key = `${card.suit}_${card.rank}`
    }

    console.log(`🔍 查找卡牌映射: ${card.getDisplayName()} -> ${key}`)
    const result = CARD_SPRITE_MAP[key]
    if (!result) {
      console.error(`❌ 未找到映射: ${key}`)
      console.log('可用的映射键:', Object.keys(CARD_SPRITE_MAP).slice(0, 10))
    }
    return result
  }

  getCardImageKey(card) {
    // 生成卡牌图片的键名
    const suitMap = {
      'hearts': 'h',
      'diamonds': 'd',
      'clubs': 'c',
      'spades': 's'
    }

    const rankMap = {
      'A': '1', '2': '2', '3': '3', '4': '4', '5': '5', '6': '6', '7': '7',
      '8': '8', '9': '9', '10': '10', 'J': '11', 'Q': '12', 'K': '13'
    }

    if (card.rank === 'Joker') {
      return card.suit === 'red' ? 'card_joker_red' : 'card_joker_black'
    }

    const suit = suitMap[card.suit] || 'h'
    const rank = rankMap[card.rank] || '1'
    return `card_${suit}_${rank}`
  }

  createHighQualityCard(container, width, height, card) {
    // 创建高质量的自定义卡牌

    // 卡牌背景 - 渐变效果
    const cardBg = this.add.graphics()
    cardBg.fillGradientStyle(0xFFFFF8, 0xFFFFF8, 0xF8F8F0, 0xF8F8F0, 1)
    cardBg.fillRoundedRect(-width/2, -height/2, width, height, 10)

    // 外边框 - 深色边框
    cardBg.lineStyle(2, 0x333333, 1)
    cardBg.strokeRoundedRect(-width/2, -height/2, width, height, 10)

    // 内边框 - 装饰边框
    cardBg.lineStyle(1, 0x666666, 0.5)
    cardBg.strokeRoundedRect(-width/2 + 3, -height/2 + 3, width - 6, height - 6, 8)

    container.add(cardBg)

    // 卡牌内容
    const isRed = card.suit === 'hearts' || card.suit === 'diamonds'
    const cardColor = isRed ? '#DC143C' : '#000000'

    // 左上角数字/字母 - 更大更清晰
    const topText = this.add.text(-width/2 + 8, -height/2 + 8, card.rank, {
      fontSize: '16px',
      color: cardColor,
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(0, 0)
    container.add(topText)

    // 左上角花色 - 更大更清晰
    const topSuit = this.add.text(-width/2 + 8, -height/2 + 26, this.getSuitSymbol(card.suit), {
      fontSize: '16px',
      color: cardColor,
      fontFamily: 'Arial'
    }).setOrigin(0, 0)
    container.add(topSuit)

    // 中央大号显示 - 更突出
    const centerText = this.add.text(0, -12, card.rank, {
      fontSize: '28px',
      color: cardColor,
      fontFamily: 'Arial',
      fontWeight: 'bold',
      stroke: isRed ? '#8B0000' : '#333333',
      strokeThickness: 1
    }).setOrigin(0.5)
    container.add(centerText)

    const centerSuit = this.add.text(0, 12, this.getSuitSymbol(card.suit), {
      fontSize: '32px',
      color: cardColor,
      fontFamily: 'Arial',
      stroke: isRed ? '#8B0000' : '#333333',
      strokeThickness: 1
    }).setOrigin(0.5)
    container.add(centerSuit)

    // 右下角数字/字母（倒置）
    const bottomText = this.add.text(width/2 - 8, height/2 - 8, card.rank, {
      fontSize: '16px',
      color: cardColor,
      fontFamily: 'Arial',
      fontWeight: 'bold'
    }).setOrigin(1, 1).setRotation(Math.PI)
    container.add(bottomText)

    // 右下角花色（倒置）
    const bottomSuit = this.add.text(width/2 - 8, height/2 - 26, this.getSuitSymbol(card.suit), {
      fontSize: '16px',
      color: cardColor,
      fontFamily: 'Arial'
    }).setOrigin(1, 1).setRotation(Math.PI)
    container.add(bottomSuit)
  }

  getSuitSymbol(suit) {
    const symbols = {
      'hearts': '♥',
      'diamonds': '♦',
      'clubs': '♣',
      'spades': '♠'
    }
    return symbols[suit] || '?'
  }

  addCenterPattern(cardContainer, card, cardColor, _displayWidth, _displayHeight) {
    // 根据牌的点数在中央添加相应的花色图案
    const suitSymbol = this.getSuitSymbol(card.suit)
    const fontSize = '12px'

    // 根据不同点数显示不同的图案布局
    switch(card.rank) {
      case 'A':
        // A：中央一个大花色
        const aceText = this.add.text(0, 0, suitSymbol, {
          fontSize: '16px',
          color: cardColor,
          fontFamily: 'Arial'
        }).setOrigin(0.5)
        cardContainer.add(aceText)
        break

      case '2':
        // 2：上下各一个
        const top2 = this.add.text(0, -12, suitSymbol, {
          fontSize: fontSize,
          color: cardColor,
          fontFamily: 'Arial'
        }).setOrigin(0.5)
        cardContainer.add(top2)

        const bottom2 = this.add.text(0, 12, suitSymbol, {
          fontSize: fontSize,
          color: cardColor,
          fontFamily: 'Arial'
        }).setOrigin(0.5).setRotation(Math.PI)
        cardContainer.add(bottom2)
        break

      case '3':
        // 3：上中下各一个
        const top3 = this.add.text(0, -16, suitSymbol, {
          fontSize: fontSize,
          color: cardColor,
          fontFamily: 'Arial'
        }).setOrigin(0.5)
        cardContainer.add(top3)

        const center3 = this.add.text(0, 0, suitSymbol, {
          fontSize: fontSize,
          color: cardColor,
          fontFamily: 'Arial'
        }).setOrigin(0.5)
        cardContainer.add(center3)

        const bottom3 = this.add.text(0, 16, suitSymbol, {
          fontSize: fontSize,
          color: cardColor,
          fontFamily: 'Arial'
        }).setOrigin(0.5).setRotation(Math.PI)
        cardContainer.add(bottom3)
        break

      case 'J':
      case 'Q':
      case 'K':
        // 人头牌：中央显示字母
        const faceText = this.add.text(0, 0, card.rank, {
          fontSize: '20px',
          color: cardColor,
          fontFamily: 'Arial',
          fontWeight: 'bold'
        }).setOrigin(0.5)
        cardContainer.add(faceText)
        break

      default:
        // 其他数字牌：简化显示中央花色
        const defaultText = this.add.text(0, 0, suitSymbol, {
          fontSize: '14px',
          color: cardColor,
          fontFamily: 'Arial'
        }).setOrigin(0.5)
        cardContainer.add(defaultText)
        break
    }
  }

  selectCard(cardContainer) {
    // 卡牌选择逻辑
    console.log('选择卡牌:', cardContainer.cardData.getDisplayName())

    // 切换选中状态
    if (cardContainer.selected) {
      // 取消选中 - 添加平滑动画
      this.tweens.add({
        targets: cardContainer,
        y: cardContainer.originalY,
        scaleX: 1,
        scaleY: 1,
        duration: 200,
        ease: 'Back.easeOut'
      })

      cardContainer.selected = false
      cardContainer.clearTint()

      // 移除选中光环效果
      this.removeSelectionGlow(cardContainer)

      // 从选中列表中移除
      const index = this.selectedCards.findIndex(card => card.id === cardContainer.cardData.id)
      if (index !== -1) {
        this.selectedCards.splice(index, 1)
      }
    } else {
      // 选中卡牌 - 添加平滑动画
      if (!cardContainer.originalY) {
        cardContainer.originalY = cardContainer.y
      }

      this.tweens.add({
        targets: cardContainer,
        y: cardContainer.originalY - 20,
        scaleX: 1.1,
        scaleY: 1.1,
        duration: 200,
        ease: 'Back.easeOut'
      })

      cardContainer.selected = true
      cardContainer.setTint(0xFFFF99) // 淡黄色高亮

      // 添加选中光环效果
      this.createSelectionGlow(cardContainer)

      // 添加到选中列表
      this.selectedCards.push(cardContainer.cardData)
    }

    console.log('当前选中的牌:', this.selectedCards.map(card => card.getDisplayName()))

    // 验证选中的牌是否可以出
    this.validateSelectedCards()

    // 显示牌型提示
    this.showCardPatternHint()
  }

  createSelectionGlow(cardContainer) {
    // 创建选中光环效果 - 适应新的卡牌尺寸
    if (cardContainer.glowEffect) {
      cardContainer.glowEffect.destroy()
    }

    const glow = this.add.graphics()

    // 外层光环 - 金色，适应50x70的卡牌
    glow.lineStyle(3, 0xFFD700, 0.9)
    glow.strokeRoundedRect(-27, -37, 54, 74, 8)

    // 内层光环 - 白色
    glow.lineStyle(2, 0xFFFFFF, 0.7)
    glow.strokeRoundedRect(-25, -35, 50, 70, 7)

    // 添加到卡牌容器
    cardContainer.add(glow)
    cardContainer.glowEffect = glow

    // 添加脉冲动画
    this.tweens.add({
      targets: glow,
      alpha: 0.4,
      scaleX: 1.05,
      scaleY: 1.05,
      duration: 600,
      yoyo: true,
      repeat: -1,
      ease: 'Sine.easeInOut'
    })
  }

  removeSelectionGlow(cardContainer) {
    // 移除选中光环效果
    if (cardContainer.glowEffect) {
      cardContainer.glowEffect.destroy()
      cardContainer.glowEffect = null
    }
  }

  showCardPatternHint() {
    // 清除之前的提示
    if (this.uiElements.patternHint) {
      this.uiElements.patternHint.destroy()
      this.uiElements.patternHint = null
    }

    if (this.selectedCards.length === 0) {
      return
    }

    // 识别牌型
    const pattern = this.gameState.getCardPattern().identifyPattern(this.selectedCards)
    let hintText = ''

    if (pattern) {
      const patternNames = {
        'single': '单牌',
        'pair': '对子',
        'triple': '三张',
        'triple_with_single': '三带一',
        'triple_with_pair': '三带二',
        'straight': '顺子',
        'pair_straight': '连对',
        'triple_straight': '飞机',
        'bomb': '炸弹',
        'rocket': '火箭'
      }
      hintText = patternNames[pattern.type] || pattern.type
    } else {
      hintText = '无效牌型'
    }

    // 创建华丽的提示容器
    const hintContainer = this.add.container(600, 620)

    // 提示背景
    const hintBg = this.add.graphics()
    const bgColor = pattern ? 0x00AA00 : 0xAA0000
    hintBg.fillStyle(bgColor, 0.8)
    hintBg.lineStyle(2, 0xFFD700, 1)
    hintBg.fillRoundedRect(-80, -20, 160, 40, 20)
    hintBg.strokeRoundedRect(-80, -20, 160, 40, 20)
    hintContainer.add(hintBg)

    // 提示文字
    const hintTextObj = this.add.text(0, 0, hintText, {
      fontSize: '18px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontWeight: 'bold',
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)
    hintContainer.add(hintTextObj)

    this.uiElements.patternHint = hintContainer

    // 添加弹出动画
    hintContainer.setScale(0)
    this.tweens.add({
      targets: hintContainer,
      scaleX: 1,
      scaleY: 1,
      duration: 300,
      ease: 'Back.easeOut'
    })

    // 添加脉冲效果
    this.tweens.add({
      targets: hintContainer,
      scaleX: 1.1,
      scaleY: 1.1,
      duration: 800,
      yoyo: true,
      repeat: -1,
      ease: 'Sine.easeInOut'
    })
  }

  // 验证选中的牌
  validateSelectedCards() {
    if (this.selectedCards.length === 0) {
      // 没有选中牌，禁用出牌按钮
      if (this.uiElements.playCardsButton) {
        this.uiElements.playCardsButton.setAlpha(0.5)
      }
      return
    }

    const canPlay = this.gameState.validateSelectedCards(this.selectedCards)
    if (this.uiElements.playCardsButton) {
      this.uiElements.playCardsButton.setAlpha(canPlay ? 1 : 0.5)
    }

    // 显示牌型提示
    const pattern = this.gameState.getCardPattern().identifyPattern(this.selectedCards)
    if (pattern) {
      console.log('选中牌型:', pattern.type)
    } else {
      console.log('无效牌型')
    }
  }

  bidLandlord() {
    console.log('叫地主')
    this.gameState.bid(1, 'landlord')
    this.updateUI()

    // 模拟其他玩家的叫牌
    setTimeout(() => {
      this.simulateAIBidding()
    }, 1000)
  }

  passBid() {
    console.log('不叫')
    this.gameState.bid(1, 'pass')
    this.updateUI()

    // 模拟其他玩家的叫牌
    setTimeout(() => {
      this.simulateAIBidding()
    }, 1000)
  }

  simulateAIBidding() {
    const gameInfo = this.gameState.getGameInfo()
    if (gameInfo.phase !== 'bidding') return

    const currentPlayer = gameInfo.currentPlayer
    if (currentPlayer === 0) return // 轮到玩家1，不需要模拟

    // 智能AI叫牌逻辑：根据手牌质量决定
    const aiPlayer = this.gameState.getPlayer(currentPlayer + 1)
    const shouldBid = this.evaluateHandForBidding(aiPlayer.hand.getCards())
    const bidType = shouldBid ? 'landlord' : 'pass'

    console.log(`AI玩家${currentPlayer + 1} ${bidType === 'landlord' ? '叫地主' : '不叫'}`)
    this.gameState.bid(currentPlayer + 1, bidType)
    this.updateUI()

    // 如果还在叫牌阶段，继续模拟
    if (this.gameState.phase === 'bidding') {
      setTimeout(() => {
        this.simulateAIBidding()
      }, 1000)
    } else if (this.gameState.phase === 'playing') {
      // 叫牌结束，开始游戏
      this.displayPlayerCards()
      setTimeout(() => {
        this.simulateAIPlaying()
      }, 1000)
    }
  }

  evaluateHandForBidding(cards) {
    // 评估手牌质量，决定是否叫地主
    let score = 0

    // 统计各种牌型
    const cardCounts = {}
    const suitCounts = { hearts: 0, diamonds: 0, clubs: 0, spades: 0, joker: 0 }

    cards.forEach(card => {
      const key = card.rank
      cardCounts[key] = (cardCounts[key] || 0) + 1
      suitCounts[card.suit]++
    })

    // 王牌加分
    if (suitCounts.joker > 0) score += suitCounts.joker * 8

    // 炸弹加分
    Object.values(cardCounts).forEach(count => {
      if (count >= 4) score += 15 // 炸弹
      else if (count === 3) score += 5 // 三张
      else if (count === 2) score += 2 // 对子
    })

    // 大牌加分（A, K, Q, J, 2）
    const bigCards = ['A', 'K', 'Q', 'J', '2']
    bigCards.forEach(rank => {
      if (cardCounts[rank]) score += cardCounts[rank] * 3
    })

    // 根据分数决定是否叫地主
    return score >= 25 // 分数达到25分以上才叫地主
  }

  // 出牌
  playSelectedCards() {
    if (this.selectedCards.length === 0) {
      console.log('没有选中任何牌')
      return
    }

    const result = this.gameState.playCards(1, this.selectedCards)
    if (result.success) {
      console.log('出牌成功')

      // 播放出牌动画
      this.playCardAnimation(this.selectedCards, () => {
        this.selectedCards = []
        this.updateUI()
        this.displayPlayerCards()

        if (result.gameOver) {
          console.log('游戏结束，玩家获胜！')
          this.uiElements.gamePhase.setText('游戏结束 - 你赢了！')
          this.showGameOverEffect()
        } else {
          // 模拟AI出牌
          setTimeout(() => {
            this.simulateAIPlaying()
          }, 1000)
        }
      })
    } else {
      console.log('出牌失败:', result.message)
      // 显示错误提示
      this.showErrorMessage(result.message)
    }
  }

  playCardAnimation(cards, callback) {
    // 找到选中的卡牌精灵
    const selectedSprites = this.playerHandAreas.bottom.cards.filter(sprite =>
      sprite.selected && cards.some(card => card.id === sprite.cardData.id)
    )

    if (selectedSprites.length === 0) {
      callback()
      return
    }

    // 播放出牌音效
    this.playSound('chupaiSound', 0.5)

    // 创建出牌动画
    selectedSprites.forEach((sprite, index) => {
      this.tweens.add({
        targets: sprite,
        x: 400 + (index - selectedSprites.length / 2) * 70,
        y: 250,
        scaleX: 1.2,
        scaleY: 1.2,
        alpha: 0.8,
        duration: 500,
        ease: 'Power2',
        delay: index * 50,
        onComplete: () => {
          if (index === selectedSprites.length - 1) {
            // 最后一张牌动画完成后，等待一下再清理
            setTimeout(() => {
              selectedSprites.forEach(s => s.destroy())
              callback()
            }, 300)
          }
        }
      })
    })

    // 添加粒子效果
    this.createCardPlayEffect(400, 250)
  }

  updateGameInfoPanel(gameInfo) {
    // 更新轮次信息
    if (this.uiElements.roundInfo) {
      this.uiElements.roundInfo.setText(`第${gameInfo.round || 1}轮`)
    }

    // 更新底牌信息
    if (this.uiElements.landlordCardsInfo) {
      if (gameInfo.phase === 'playing' && gameInfo.landlordCards) {
        const cardsText = gameInfo.landlordCards.map(card => card.getDisplayName()).join(' ')
        this.uiElements.landlordCardsInfo.setText(`底牌: ${cardsText}`)
        this.uiElements.landlordCardsInfo.setStyle({ color: '#FFD700' })
      } else {
        this.uiElements.landlordCardsInfo.setText('底牌: 未翻开')
        this.uiElements.landlordCardsInfo.setStyle({ color: '#CCCCCC' })
      }
    }

    // 更新倍数信息
    if (this.uiElements.multiplierInfo) {
      const multiplier = gameInfo.multiplier || 1
      this.uiElements.multiplierInfo.setText(`倍数: x${multiplier}`)

      // 根据倍数改变颜色
      if (multiplier >= 4) {
        this.uiElements.multiplierInfo.setStyle({ color: '#FF0000' })
      } else if (multiplier >= 2) {
        this.uiElements.multiplierInfo.setStyle({ color: '#FFA500' })
      } else {
        this.uiElements.multiplierInfo.setStyle({ color: '#00FF00' })
      }
    }
  }

  updatePlayHistory(gameInfo) {
    if (gameInfo.lastPlayedCards && gameInfo.lastPlayedCards.length > 0) {
      if (this.uiElements.historyContainer) {
        this.uiElements.historyContainer.setVisible(true)
      }

      if (this.uiElements.playHistory) {
        const cardsText = gameInfo.lastPlayedCards.map(card => card.getDisplayName()).join(' ')
        const playerName = gameInfo.lastPlayerName || `玩家${gameInfo.lastPlayerId}`
        this.uiElements.playHistory.setText(`${playerName}: ${cardsText}`)
      }
    } else {
      if (this.uiElements.historyContainer) {
        this.uiElements.historyContainer.setVisible(false)
      }
    }
  }

  createCardPlayEffect(x, y) {
    // 创建简单的粒子效果
    const particles = []
    for (let i = 0; i < 10; i++) {
      const particle = this.add.graphics()
      particle.fillStyle(0xFFD700, 1)
      particle.fillCircle(x, y, 3)
      particles.push(particle)

      this.tweens.add({
        targets: particle,
        x: x + (Math.random() - 0.5) * 100,
        y: y + (Math.random() - 0.5) * 100,
        alpha: 0,
        duration: 800,
        ease: 'Power2',
        onComplete: () => {
          particle.destroy()
        }
      })
    }
  }

  showErrorMessage(message) {
    // 显示错误消息
    const errorText = this.add.text(400, 300, message, {
      fontSize: '20px',
      color: '#FF4444',
      fontFamily: 'Arial',
      fontWeight: 'bold',
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)

    // 添加闪烁效果
    this.tweens.add({
      targets: errorText,
      alpha: 0,
      duration: 300,
      yoyo: true,
      repeat: 3,
      onComplete: () => {
        errorText.destroy()
      }
    })
  }

  showGameOverEffect() {
    // 游戏结束特效
    const winText = this.add.text(400, 300, '恭喜获胜！', {
      fontSize: '36px',
      color: '#FFD700',
      fontFamily: 'Arial',
      fontWeight: 'bold',
      stroke: '#000000',
      strokeThickness: 3
    }).setOrigin(0.5)

    // 缩放动画
    this.tweens.add({
      targets: winText,
      scaleX: 1.5,
      scaleY: 1.5,
      duration: 1000,
      ease: 'Bounce.easeOut'
    })

    // 创建庆祝粒子效果
    this.createCelebrationEffect()
  }

  createCelebrationEffect() {
    // 创建庆祝烟花效果
    for (let i = 0; i < 5; i++) {
      setTimeout(() => {
        this.createFirework(
          300 + Math.random() * 600,
          200 + Math.random() * 400
        )
      }, i * 300)
    }

    // 创建飘落的金币效果
    this.createGoldCoinEffect()
  }

  createFirework(x, y) {
    // 创建单个烟花效果
    const colors = [0xFFD700, 0xFF6347, 0x32CD32, 0x1E90FF, 0xFF69B4, 0xFFA500]
    const particleCount = 20

    for (let i = 0; i < particleCount; i++) {
      const particle = this.add.graphics()
      const color = colors[Math.floor(Math.random() * colors.length)]
      particle.fillStyle(color, 1)
      particle.fillCircle(x, y, 4)

      const angle = (i / particleCount) * Math.PI * 2
      const speed = 100 + Math.random() * 100
      const targetX = x + Math.cos(angle) * speed
      const targetY = y + Math.sin(angle) * speed

      this.tweens.add({
        targets: particle,
        x: targetX,
        y: targetY,
        alpha: 0,
        scaleX: 0.1,
        scaleY: 0.1,
        duration: 1000 + Math.random() * 500,
        ease: 'Power2',
        onComplete: () => {
          particle.destroy()
        }
      })
    }
  }

  createGoldCoinEffect() {
    // 创建金币飘落效果
    for (let i = 0; i < 15; i++) {
      setTimeout(() => {
        const coin = this.add.graphics()
        coin.fillStyle(0xFFD700, 1)
        coin.lineStyle(2, 0xFFA500, 1)
        coin.fillCircle(0, 0, 8)
        coin.strokeCircle(0, 0, 8)

        const startX = Math.random() * 1200
        coin.setPosition(startX, -20)

        this.tweens.add({
          targets: coin,
          y: 820,
          rotation: Math.PI * 4,
          duration: 3000 + Math.random() * 1000,
          ease: 'Power1',
          onComplete: () => {
            coin.destroy()
          }
        })
      }, i * 200)
    }
  }

  // 过牌
  passCards() {
    const result = this.gameState.pass(1)
    if (result.success) {
      console.log('过牌成功')
      this.selectedCards = []
      this.updateUI()

      // 模拟AI出牌
      setTimeout(() => {
        this.simulateAIPlaying()
      }, 1000)
    }
  }

  // 提示功能
  showHint() {
    console.log('显示提示')
    // TODO: 实现提示逻辑
  }

  // 模拟AI出牌
  simulateAIPlaying() {
    const gameInfo = this.gameState.getGameInfo()
    if (gameInfo.phase !== 'playing') return

    const currentPlayer = gameInfo.currentPlayer
    if (currentPlayer === 0) return // 轮到玩家1，不需要模拟

    const aiPlayer = this.gameState.getPlayer(currentPlayer + 1)
    if (!aiPlayer || aiPlayer.hand.getCardCount() === 0) return

    // 智能AI出牌逻辑
    const bestPlay = this.findBestAIPlay(aiPlayer.hand.getCards(), gameInfo.lastPlayedCards)

    if (bestPlay && bestPlay.length > 0) {
      const result = this.gameState.playCards(currentPlayer + 1, bestPlay)

      if (result.success) {
        console.log(`AI玩家${currentPlayer + 1} 出牌成功:`, bestPlay.map(c => c.getDisplayName()).join(' '))
        this.updateUI()

        if (result.gameOver) {
          console.log(`AI玩家${currentPlayer + 1} 获胜！`)
          this.uiElements.gamePhase.setText(`游戏结束 - 玩家${currentPlayer + 1}赢了！`)
          return
        }
      } else {
        // 出牌失败，改为过牌
        this.gameState.pass(currentPlayer + 1)
        console.log(`AI玩家${currentPlayer + 1} 过牌（出牌失败）`)
      }
    } else {
      // AI选择过牌
      this.gameState.pass(currentPlayer + 1)
      console.log(`AI玩家${currentPlayer + 1} 过牌`)
    }

    this.updateUI()

    // 继续下一轮
    setTimeout(() => {
      this.simulateAIPlaying()
    }, 1000)
  }

  findBestAIPlay(cards, lastPlayedCards) {
    // 寻找AI的最佳出牌
    console.log('AI寻找最佳出牌，手牌:', cards.map(c => c.getDisplayName()).join(' '))

    // 如果是首出，选择最小的单牌
    if (!lastPlayedCards || lastPlayedCards.length === 0) {
      return this.findSmallestSingle(cards)
    }

    // 尝试找到能够压过上家的牌
    const lastPattern = this.gameState.getCardPattern().identifyPattern(lastPlayedCards)
    if (!lastPattern) return null

    // 根据上家牌型寻找对应的牌型
    switch (lastPattern.type) {
      case 'single':
        return this.findBiggerSingle(cards, lastPlayedCards[0])
      case 'pair':
        return this.findBiggerPair(cards, lastPattern)
      case 'triple':
        return this.findBiggerTriple(cards, lastPattern)
      case 'bomb':
        return this.findBiggerBomb(cards, lastPattern)
      default:
        // 对于复杂牌型，暂时选择过牌
        return null
    }
  }

  findSmallestSingle(cards) {
    // 找到最小的单牌
    const sortedCards = [...cards].sort((a, b) => this.getCardValue(a) - this.getCardValue(b))
    return [sortedCards[0]]
  }

  findBiggerSingle(cards, lastCard) {
    // 找到比上家更大的单牌
    const lastValue = this.getCardValue(lastCard)
    const biggerCards = cards.filter(card => this.getCardValue(card) > lastValue)

    if (biggerCards.length > 0) {
      // 选择最小的能压过的牌
      const sortedBigger = biggerCards.sort((a, b) => this.getCardValue(a) - this.getCardValue(b))
      return [sortedBigger[0]]
    }
    return null
  }

  findBiggerPair(cards, lastPattern) {
    // 找到比上家更大的对子
    const pairs = this.findPairs(cards)
    const biggerPairs = pairs.filter(pair => this.getCardValue(pair[0]) > lastPattern.value)

    if (biggerPairs.length > 0) {
      const sortedPairs = biggerPairs.sort((a, b) => this.getCardValue(a[0]) - this.getCardValue(b[0]))
      return sortedPairs[0]
    }
    return null
  }

  findBiggerTriple(cards, lastPattern) {
    // 找到比上家更大的三张
    const triples = this.findTriples(cards)
    const biggerTriples = triples.filter(triple => this.getCardValue(triple[0]) > lastPattern.value)

    if (biggerTriples.length > 0) {
      const sortedTriples = biggerTriples.sort((a, b) => this.getCardValue(a[0]) - this.getCardValue(b[0]))
      return sortedTriples[0]
    }
    return null
  }

  findBiggerBomb(cards, lastPattern) {
    // 找到比上家更大的炸弹
    const bombs = this.findBombs(cards)
    const biggerBombs = bombs.filter(bomb => this.getCardValue(bomb[0]) > lastPattern.value)

    if (biggerBombs.length > 0) {
      const sortedBombs = biggerBombs.sort((a, b) => this.getCardValue(a[0]) - this.getCardValue(b[0]))
      return sortedBombs[0]
    }
    return null
  }

  findPairs(cards) {
    // 找到所有对子
    const cardGroups = {}
    cards.forEach(card => {
      const key = card.rank
      if (!cardGroups[key]) cardGroups[key] = []
      cardGroups[key].push(card)
    })

    const pairs = []
    Object.values(cardGroups).forEach(group => {
      if (group.length >= 2) {
        pairs.push([group[0], group[1]])
      }
    })
    return pairs
  }

  findTriples(cards) {
    // 找到所有三张
    const cardGroups = {}
    cards.forEach(card => {
      const key = card.rank
      if (!cardGroups[key]) cardGroups[key] = []
      cardGroups[key].push(card)
    })

    const triples = []
    Object.values(cardGroups).forEach(group => {
      if (group.length >= 3) {
        triples.push([group[0], group[1], group[2]])
      }
    })
    return triples
  }

  findBombs(cards) {
    // 找到所有炸弹
    const cardGroups = {}
    cards.forEach(card => {
      const key = card.rank
      if (!cardGroups[key]) cardGroups[key] = []
      cardGroups[key].push(card)
    })

    const bombs = []
    Object.values(cardGroups).forEach(group => {
      if (group.length >= 4) {
        bombs.push(group)
      }
    })
    return bombs
  }

  getCardValue(card) {
    // 获取卡牌的数值用于比较
    const rankValues = {
      '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
      'J': 11, 'Q': 12, 'K': 13, 'A': 14, '2': 15,
      'small_joker': 16, 'big_joker': 17
    }
    return rankValues[card.rank] || 0
  }
}

// 游戏管理器类
export default class GameScene {
  constructor(container) {
    this.container = container
    this.game = null
    this.init()
  }

  init() {
    try {
      console.log('GameScene: 初始化游戏')

      const config = {
        type: Phaser.AUTO,
        width: 1200,
        height: 800,
        parent: this.container,
        backgroundColor: '#0f5132',
        scene: [GameMainScene],
        scale: {
          mode: Phaser.Scale.FIT,
          autoCenter: Phaser.Scale.CENTER_BOTH,
          min: {
            width: 800,
            height: 600
          },
          max: {
            width: 1600,
            height: 1200
          }
        }
      }

      this.game = new Phaser.Game(config)
      console.log('GameScene: 游戏初始化成功')
    } catch (error) {
      console.error('GameScene: 游戏初始化失败', error)
      throw error
    }
  }

  destroy() {
    if (this.game) {
      console.log('GameScene: 销毁游戏实例')
      this.game.destroy(true)
      this.game = null
    }
  }
}
