import Phaser from 'phaser'
import { GameState } from './GameState.js'

// 游戏场景类
class GameMainScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameMainScene' })
    this.gameState = new GameState()
    this.cardSprites = new Map() // 存储卡牌精灵
    this.playerHandAreas = {} // 玩家手牌区域
    this.uiElements = {} // UI元素
    this.selectedCards = [] // 当前选中的牌
  }

  preload() {
    // 预加载资源
    console.log('GameMainScene: 预加载资源')
    // 使用标准斗地主界面设计，不需要加载外部图片
  }

  create() {
    // 游戏初始化逻辑
    console.log('GameMainScene: 创建游戏场景')

    // 创建标准斗地主游戏背景
    this.createStandardBackground()

    // 添加标题
    this.uiElements.title = this.add.text(400, 30, '斗地主', {
      fontSize: '28px',
      color: '#FFD700',
      fontFamily: 'Arial Black, Arial',
      stroke: '#8B4513',
      strokeThickness: 3,
      shadow: {
        offsetX: 2,
        offsetY: 2,
        color: '#000000',
        blur: 4,
        fill: true
      }
    }).setOrigin(0.5)

    // 创建游戏桌面
    this.createGameTable()

    // 添加玩家位置
    this.createPlayerPositions()

    // 创建UI界面
    this.createGameUI()

    // 开始游戏
    this.startNewGame()
  }



  createStandardBackground() {
    // 创建古代卡通风格背景
    const bg = this.add.graphics()

    // 主背景 - 古代宫廷风格渐变（深红到暗金）
    bg.fillGradientStyle(0x8B0000, 0x8B0000, 0x4A4A00, 0x4A4A00, 1)
    bg.fillRect(0, 0, 800, 600)

    // 添加古代纹样装饰
    this.createAncientPatterns()

    // 添加云朵装饰
    this.createCloudDecorations()
  }

  createAncientPatterns() {
    // 创建古代纹样装饰
    const patterns = this.add.graphics()

    // 四角装饰纹样
    const corners = [
      {x: 50, y: 50}, {x: 750, y: 50},
      {x: 50, y: 550}, {x: 750, y: 550}
    ]

    corners.forEach(corner => {
      patterns.lineStyle(3, 0xFFD700, 0.8)
      patterns.strokeRect(corner.x - 30, corner.y - 30, 60, 60)
      patterns.lineStyle(2, 0xFF6347, 0.6)
      patterns.strokeRect(corner.x - 20, corner.y - 20, 40, 40)

      // 中心装饰
      patterns.fillStyle(0xFFD700, 0.7)
      patterns.fillCircle(corner.x, corner.y, 8)
    })

    // 顶部和底部边框装饰
    patterns.lineStyle(4, 0xFFD700, 0.9)
    patterns.moveTo(100, 20)
    patterns.lineTo(700, 20)
    patterns.moveTo(100, 580)
    patterns.lineTo(700, 580)
    patterns.strokePath()
  }

  createCloudDecorations() {
    // 添加卡通云朵装饰
    const clouds = [
      {x: 150, y: 80, scale: 0.8},
      {x: 650, y: 100, scale: 0.6},
      {x: 100, y: 500, scale: 0.7},
      {x: 700, y: 520, scale: 0.5}
    ]

    clouds.forEach(cloud => {
      this.createCloud(cloud.x, cloud.y, cloud.scale)
    })
  }

  createCloud(x, y, scale) {
    const cloud = this.add.graphics()
    cloud.fillStyle(0xFFFFFF, 0.3)

    // 云朵主体
    cloud.fillCircle(x, y, 20 * scale)
    cloud.fillCircle(x - 15 * scale, y + 5 * scale, 15 * scale)
    cloud.fillCircle(x + 15 * scale, y + 5 * scale, 15 * scale)
    cloud.fillCircle(x - 8 * scale, y - 8 * scale, 12 * scale)
    cloud.fillCircle(x + 8 * scale, y - 8 * scale, 12 * scale)
  }

  createGameTable() {
    // 创建古代风格游戏桌面
    const centerX = 400
    const centerY = 300
    const tableWidth = 480
    const tableHeight = 280

    // 桌面阴影
    const shadow = this.add.graphics()
    shadow.fillStyle(0x000000, 0.4)
    shadow.fillEllipse(centerX + 6, centerY + 6, tableWidth, tableHeight)

    // 桌面主体 - 古代木桌风格
    const table = this.add.graphics()
    table.fillGradientStyle(0x8B4513, 0x8B4513, 0x654321, 0x654321, 1)
    table.fillEllipse(centerX, centerY, tableWidth, tableHeight)

    // 外边框 - 金色装饰
    table.lineStyle(5, 0xFFD700, 1)
    table.strokeEllipse(centerX, centerY, tableWidth, tableHeight)

    // 内边框装饰
    table.lineStyle(3, 0xFF6347, 0.8)
    table.strokeEllipse(centerX, centerY, tableWidth - 30, tableHeight - 30)

    // 桌面中央装饰 - 古代印章风格
    this.createCenterDecoration(centerX, centerY)
  }

  createCenterDecoration(centerX, centerY) {
    // 中央古代印章装饰
    const decor = this.add.graphics()

    // 印章背景
    decor.fillStyle(0x8B0000, 0.7)
    decor.fillCircle(centerX, centerY, 50)

    // 印章边框
    decor.lineStyle(3, 0xFFD700, 1)
    decor.strokeCircle(centerX, centerY, 50)
    decor.strokeCircle(centerX, centerY, 35)

    // 中央文字装饰
    const _centerText = this.add.text(centerX, centerY, '斗\n地\n主', {
      fontSize: '16px',
      color: '#FFD700',
      fontFamily: 'Arial Black, Arial',
      align: 'center',
      lineSpacing: -5
    }).setOrigin(0.5)
  }

  createPlayerPositions() {
    // 标准斗地主布局 - 确保在600px高度内
    const positions = [
      { x: 150, y: 520, name: '我', position: 'bottom', color: '#FFD700' },
      { x: 80, y: 150, name: '玩家2', position: 'left', color: '#87CEEB' },
      { x: 720, y: 150, name: '玩家3', position: 'right', color: '#FFA07A' }
    ]

    positions.forEach((pos, index) => {
      // 创建标准斗地主头像区域
      this.createPlayerAvatar(pos, index + 1)
    })
  }

  createPlayerAvatar(pos, playerNum) {
    // 古代风格头像阴影
    const shadow = this.add.graphics()
    shadow.fillStyle(0x000000, 0.4)
    shadow.fillCircle(pos.x + 3, pos.y + 3, 35)

    // 头像背景 - 古代服饰风格颜色
    const avatarBg = this.add.graphics()
    const colors = [0x8B0000, 0x4B0082, 0x006400] // 深红、深紫、深绿
    avatarBg.fillStyle(colors[playerNum - 1] || 0x8B0000, 1)
    avatarBg.fillCircle(pos.x, pos.y, 35)

    // 外边框 - 金色装饰
    avatarBg.lineStyle(4, 0xFFD700, 1)
    avatarBg.strokeCircle(pos.x, pos.y, 35)

    // 内边框装饰 - 红色
    avatarBg.lineStyle(2, 0xFF6347, 0.9)
    avatarBg.strokeCircle(pos.x, pos.y, 28)

    // 头像内容 - 古代风格数字
    const _avatarText = this.add.text(pos.x, pos.y, playerNum.toString(), {
      fontSize: '22px',
      color: '#FFD700',
      fontFamily: 'Arial Black, Arial',
      stroke: '#8B0000',
      strokeThickness: 3
    }).setOrigin(0.5)

    // 玩家名称 - 古代风格
    const nameY = pos.position === 'bottom' ? pos.y - 55 : pos.y + 55
    const _nameText = this.add.text(pos.x, nameY, pos.name, {
      fontSize: '14px',
      color: '#FFD700',
      fontFamily: 'Arial Black, Arial',
      stroke: '#8B0000',
      strokeThickness: 2
    }).setOrigin(0.5)

    // 添加古代装饰元素
    this.createAvatarDecoration(pos.x, pos.y, playerNum)

    // 手牌数量显示背景 - 古代风格
    const countY = pos.position === 'bottom' ? pos.y - 30 : pos.y + 70
    const countBg = this.add.graphics()

    // 背景阴影
    countBg.fillStyle(0x000000, 0.4)
    countBg.fillRoundedRect(pos.x - 27, countY - 8, 54, 18, 12)

    // 主背景 - 古代卷轴风格
    countBg.fillStyle(0x8B4513, 0.9)
    countBg.fillRoundedRect(pos.x - 25, countY - 10, 50, 20, 12)

    // 金色边框
    countBg.lineStyle(2, 0xFFD700, 1)
    countBg.strokeRoundedRect(pos.x - 25, countY - 10, 50, 20, 12)

    // 手牌数量文字 - 古代风格
    this.uiElements[`${pos.position}CardCount`] = this.add.text(pos.x, countY, '17张', {
      fontSize: '12px',
      color: '#FFD700',
      fontFamily: 'Arial Black, Arial',
      stroke: '#8B0000',
      strokeThickness: 2
    }).setOrigin(0.5)

    // 设置手牌区域
    this.playerHandAreas[pos.position] = {
      x: pos.x,
      y: pos.y,
      cards: []
    }
  }

  createAvatarDecoration(x, y, playerNum) {
    // 为头像添加古代装饰元素
    const decor = this.add.graphics()

    // 根据玩家编号添加不同装饰
    if (playerNum === 1) {
      // 皇冠装饰 - 地主风格
      decor.fillStyle(0xFFD700, 0.9)
      decor.fillTriangle(x - 10, y - 40, x, y - 50, x + 10, y - 40)
      decor.fillCircle(x - 8, y - 38, 3)
      decor.fillCircle(x, y - 42, 4)
      decor.fillCircle(x + 8, y - 38, 3)
    } else if (playerNum === 2) {
      // 帽子装饰 - 农民风格
      decor.fillStyle(0x4B0082, 0.8)
      decor.fillRect(x - 15, y - 42, 30, 10)
      decor.fillCircle(x, y - 37, 8)
    } else {
      // 头巾装饰 - 农民风格
      decor.fillStyle(0x006400, 0.8)
      decor.fillEllipse(x, y - 38, 25, 12)
      decor.fillStyle(0xFFD700, 0.6)
      decor.fillEllipse(x, y - 38, 15, 6)
    }
  }

  createGameUI() {
    // 创建古代风格游戏状态显示
    // 状态背景装饰
    const statusBg = this.add.graphics()
    statusBg.fillStyle(0x8B0000, 0.8)
    statusBg.fillRoundedRect(300, 5, 200, 30, 15)
    statusBg.lineStyle(3, 0xFFD700, 1)
    statusBg.strokeRoundedRect(300, 5, 200, 30, 15)

    this.uiElements.gamePhase = this.add.text(400, 20, '等待开始...', {
      fontSize: '16px',
      color: '#FFD700',
      fontFamily: 'Arial Black, Arial',
      stroke: '#8B0000',
      strokeThickness: 2
    }).setOrigin(0.5)

    // 创建操作按钮区域
    this.createActionButtons()
  }

  createActionButtons() {
    // 按钮放在右下角区域，确保在屏幕内
    const buttonY = 520
    const rightX = 600

    // 开始游戏按钮 - 使用标准斗地主按钮设计
    this.uiElements.startButton = this.createStandardButton(rightX, buttonY, '开始游戏', '#32CD32', () => this.startNewGame())

    // 叫地主按钮 - 使用标准斗地主按钮设计
    this.uiElements.bidLandlordButton = this.createStandardButton(rightX - 100, buttonY, '叫地主', '#FF6347', () => this.bidLandlord())
    this.uiElements.bidLandlordButton.setVisible(false)

    // 不叫按钮 - 使用标准斗地主按钮设计
    this.uiElements.passBidButton = this.createStandardButton(rightX + 100, buttonY, '不叫', '#87CEEB', () => this.passBid())
    this.uiElements.passBidButton.setVisible(false)

    // 出牌按钮
    this.uiElements.playCardsButton = this.createStandardButton(rightX - 50, buttonY, '出牌', '#FF6347', () => this.playSelectedCards())
    this.uiElements.playCardsButton.setVisible(false)

    // 过牌按钮
    this.uiElements.passButton = this.createStandardButton(rightX + 50, buttonY, '过牌', '#87CEEB', () => this.passCards())
    this.uiElements.passButton.setVisible(false)

    // 提示按钮
    this.uiElements.hintButton = this.createStandardButton(rightX - 150, buttonY, '提示', '#FFA500', () => this.showHint())
    this.uiElements.hintButton.setVisible(false)
  }

  createStandardButton(x, y, text, color, callback) {
    // 创建古代风格按钮
    const buttonWidth = 100
    const buttonHeight = 40

    // 按钮阴影
    const shadow = this.add.graphics()
    shadow.fillStyle(0x000000, 0.5)
    shadow.fillRoundedRect(x - buttonWidth/2 + 3, y - buttonHeight/2 + 3, buttonWidth, buttonHeight, 12)

    // 按钮背景 - 古代风格渐变
    const buttonBg = this.add.graphics()
    const baseColor = Phaser.Display.Color.HexStringToColor(color)
    buttonBg.fillGradientStyle(
      baseColor.lighten(20).color,
      baseColor.lighten(20).color,
      baseColor.darken(20).color,
      baseColor.darken(20).color,
      1
    )
    buttonBg.fillRoundedRect(x - buttonWidth/2, y - buttonHeight/2, buttonWidth, buttonHeight, 12)

    // 外边框 - 金色
    buttonBg.lineStyle(3, 0xFFD700, 1)
    buttonBg.strokeRoundedRect(x - buttonWidth/2, y - buttonHeight/2, buttonWidth, buttonHeight, 12)

    // 内边框 - 红色装饰
    buttonBg.lineStyle(2, 0xFF6347, 0.8)
    buttonBg.strokeRoundedRect(x - buttonWidth/2 + 4, y - buttonHeight/2 + 4, buttonWidth - 8, buttonHeight - 8, 8)

    // 按钮文字 - 古代风格
    const buttonText = this.add.text(x, y, text, {
      fontSize: '16px',
      color: '#FFD700',
      fontFamily: 'Arial Black, Arial',
      stroke: '#8B0000',
      strokeThickness: 3
    }).setOrigin(0.5)

    // 创建按钮容器
    const button = this.add.container(0, 0, [shadow, buttonBg, buttonText])
    button.setSize(buttonWidth, buttonHeight)
    button.setInteractive()

    // 添加点击效果
    button.on('pointerdown', () => {
      button.setScale(0.95)
      callback()

      // 恢复按钮样式
      setTimeout(() => {
        button.setScale(1)
      }, 100)
    })

    // 添加悬停效果
    button.on('pointerover', () => {
      button.setScale(1.05)
    })

    button.on('pointerout', () => {
      button.setScale(1)
    })

    return button
  }

  startNewGame() {
    console.log('开始新游戏')
    this.gameState.startGame()
    this.updateUI()
    this.displayPlayerCards()
  }

  updateUI() {
    const gameInfo = this.gameState.getGameInfo()

    // 更新游戏阶段显示
    const phaseTexts = {
      'waiting': '等待开始...',
      'dealing': '发牌中...',
      'bidding': '叫牌阶段',
      'playing': '游戏进行中',
      'finished': '游戏结束'
    }
    this.uiElements.gamePhase.setText(phaseTexts[gameInfo.phase] || gameInfo.phase)

    // 更新玩家手牌数量
    gameInfo.players.forEach(player => {
      const countText = this.uiElements[`${player.position}CardCount`]
      if (countText) {
        countText.setText(`${player.cardCount}张`)
        if (player.isLandlord) {
          countText.setColor('#ff0000')
        }
      }
    })

    // 更新按钮显示
    this.updateActionButtons(gameInfo)
  }

  updateActionButtons(gameInfo) {
    // 隐藏所有按钮
    Object.values(this.uiElements).forEach(element => {
      if (element && element.setVisible) {
        element.setVisible(false)
      }
    })

    if (gameInfo.phase === 'waiting') {
      if (this.uiElements.startButton) this.uiElements.startButton.setVisible(true)
    } else if (gameInfo.phase === 'bidding' && gameInfo.currentPlayer === 0) {
      // 轮到玩家1叫牌
      if (this.uiElements.bidLandlordButton) this.uiElements.bidLandlordButton.setVisible(true)
      if (this.uiElements.passBidButton) this.uiElements.passBidButton.setVisible(true)
    } else if (gameInfo.phase === 'playing' && gameInfo.currentPlayer === 0) {
      // 轮到玩家1出牌
      if (this.uiElements.playCardsButton) this.uiElements.playCardsButton.setVisible(true)
      if (this.uiElements.passButton) this.uiElements.passButton.setVisible(true)
      if (this.uiElements.hintButton) this.uiElements.hintButton.setVisible(true)
    }
  }

  displayPlayerCards() {
    // 显示玩家1（底部）的手牌
    const player1 = this.gameState.getPlayer(1)
    if (player1) {
      this.displayBottomPlayerCards(player1.hand.getCards())
    }
  }

  displayBottomPlayerCards(cards) {
    // 清除之前的卡牌
    this.playerHandAreas.bottom.cards.forEach(cardSprite => {
      cardSprite.destroy()
    })
    this.playerHandAreas.bottom.cards = []

    // 显示新的手牌
    const totalCards = cards.length
    const cardSpacing = Math.min(22, 480 / totalCards) // 动态调整间距
    const startX = 400 - (totalCards - 1) * cardSpacing / 2 // 居中显示
    const y = 340 // 手牌位置，避免与玩家信息重叠

    cards.forEach((card, index) => {
      const x = startX + index * cardSpacing
      const cardSprite = this.createCardSprite(x, y, card)
      this.playerHandAreas.bottom.cards.push(cardSprite)
    })
  }

  createCardSprite(x, y, card) {
    // 创建古代风格卡牌
    const cardWidth = 48
    const cardHeight = 68

    // 卡牌阴影
    const shadow = this.add.graphics()
    shadow.fillStyle(0x000000, 0.4)
    shadow.fillRoundedRect(x - cardWidth/2 + 3, y - cardHeight/2 + 3, cardWidth, cardHeight, 8)

    // 卡牌背景 - 古代纸张风格
    const cardBg = this.add.graphics()
    cardBg.fillGradientStyle(0xFFFAF0, 0xFFFAF0, 0xF5F5DC, 0xF5F5DC, 1)
    cardBg.fillRoundedRect(x - cardWidth/2, y - cardHeight/2, cardWidth, cardHeight, 8)

    // 外边框 - 金色
    cardBg.lineStyle(2, 0xDAA520, 1)
    cardBg.strokeRoundedRect(x - cardWidth/2, y - cardHeight/2, cardWidth, cardHeight, 8)

    // 内边框装饰
    cardBg.lineStyle(1, 0x8B4513, 0.6)
    cardBg.strokeRoundedRect(x - cardWidth/2 + 3, y - cardHeight/2 + 3, cardWidth - 6, cardHeight - 6, 5)

    // 卡牌花色和数字 - 古代风格
    const isRed = card.suit === 'hearts' || card.suit === 'diamonds'
    const cardColor = isRed ? '#DC143C' : '#2F4F4F'

    // 主要数字/字母 - 古代书法风格
    const mainText = this.add.text(x, y - 10, card.getDisplayName(), {
      fontSize: '18px',
      color: cardColor,
      fontFamily: 'Arial Black, Arial',
      fontWeight: 'bold',
      stroke: isRed ? '#8B0000' : '#000000',
      strokeThickness: 1
    }).setOrigin(0.5)

    // 花色符号 - 古代风格
    const suitSymbol = this.getSuitSymbol(card.suit)
    const suitText = this.add.text(x, y + 15, suitSymbol, {
      fontSize: '16px',
      color: cardColor,
      fontFamily: 'Arial Black, Arial',
      stroke: isRed ? '#8B0000' : '#000000',
      strokeThickness: 1
    }).setOrigin(0.5)

    // 创建卡牌容器
    const cardContainer = this.add.container(0, 0, [shadow, cardBg, mainText, suitText])
    cardContainer.setSize(cardWidth, cardHeight)
    cardContainer.setInteractive()
    cardContainer.cardData = card

    // 添加点击事件
    cardContainer.on('pointerdown', () => {
      this.selectCard(cardContainer)
    })

    // 添加悬停效果
    cardContainer.on('pointerover', () => {
      cardContainer.setScale(1.05)
    })

    cardContainer.on('pointerout', () => {
      if (!cardContainer.selected) {
        cardContainer.setScale(1)
      }
    })

    return cardContainer
  }

  getSuitSymbol(suit) {
    const symbols = {
      'hearts': '♥',
      'diamonds': '♦',
      'clubs': '♣',
      'spades': '♠'
    }
    return symbols[suit] || '?'
  }

  selectCard(cardContainer) {
    // 卡牌选择逻辑
    console.log('选择卡牌:', cardContainer.cardData.getDisplayName())

    // 切换选中状态
    if (cardContainer.selected) {
      cardContainer.y = 340 // 恢复到原位置
      cardContainer.selected = false
      cardContainer.setScale(1)

      // 从选中列表中移除
      const index = this.selectedCards.findIndex(card => card.id === cardContainer.cardData.id)
      if (index !== -1) {
        this.selectedCards.splice(index, 1)
      }
    } else {
      cardContainer.y = 320 // 向上移动表示选中
      cardContainer.selected = true
      cardContainer.setScale(1.1)

      // 添加到选中列表
      this.selectedCards.push(cardContainer.cardData)
    }

    console.log('当前选中的牌:', this.selectedCards.map(card => card.getDisplayName()))

    // 验证选中的牌是否可以出
    this.validateSelectedCards()
  }

  // 验证选中的牌
  validateSelectedCards() {
    if (this.selectedCards.length === 0) {
      // 没有选中牌，禁用出牌按钮
      if (this.uiElements.playCardsButton) {
        this.uiElements.playCardsButton.setAlpha(0.5)
      }
      return
    }

    const canPlay = this.gameState.validateSelectedCards(this.selectedCards)
    if (this.uiElements.playCardsButton) {
      this.uiElements.playCardsButton.setAlpha(canPlay ? 1 : 0.5)
    }

    // 显示牌型提示
    const pattern = this.gameState.getCardPattern().identifyPattern(this.selectedCards)
    if (pattern) {
      console.log('选中牌型:', pattern.type)
    } else {
      console.log('无效牌型')
    }
  }

  bidLandlord() {
    console.log('叫地主')
    this.gameState.bid(1, 'landlord')
    this.updateUI()

    // 模拟其他玩家的叫牌
    setTimeout(() => {
      this.simulateAIBidding()
    }, 1000)
  }

  passBid() {
    console.log('不叫')
    this.gameState.bid(1, 'pass')
    this.updateUI()

    // 模拟其他玩家的叫牌
    setTimeout(() => {
      this.simulateAIBidding()
    }, 1000)
  }

  simulateAIBidding() {
    const gameInfo = this.gameState.getGameInfo()
    if (gameInfo.phase !== 'bidding') return

    const currentPlayer = gameInfo.currentPlayer
    if (currentPlayer === 0) return // 轮到玩家1，不需要模拟

    // 简单的AI逻辑：随机决定叫牌或不叫
    const shouldBid = Math.random() > 0.7 // 30%概率叫地主
    const bidType = shouldBid ? 'landlord' : 'pass'

    console.log(`AI玩家${currentPlayer + 1} ${bidType === 'landlord' ? '叫地主' : '不叫'}`)
    this.gameState.bid(currentPlayer + 1, bidType)
    this.updateUI()

    // 如果还在叫牌阶段，继续模拟
    if (this.gameState.phase === 'bidding') {
      setTimeout(() => {
        this.simulateAIBidding()
      }, 1000)
    } else if (this.gameState.phase === 'playing') {
      // 叫牌结束，开始游戏
      this.displayPlayerCards()
      setTimeout(() => {
        this.simulateAIPlaying()
      }, 1000)
    }
  }

  // 出牌
  playSelectedCards() {
    if (this.selectedCards.length === 0) {
      console.log('没有选中任何牌')
      return
    }

    const result = this.gameState.playCards(1, this.selectedCards)
    if (result.success) {
      console.log('出牌成功')
      this.selectedCards = []
      this.updateUI()
      this.displayPlayerCards()

      if (result.gameOver) {
        console.log('游戏结束，玩家获胜！')
        this.uiElements.gamePhase.setText('游戏结束 - 你赢了！')
      } else {
        // 模拟AI出牌
        setTimeout(() => {
          this.simulateAIPlaying()
        }, 1000)
      }
    } else {
      console.log('出牌失败:', result.message)
    }
  }

  // 过牌
  passCards() {
    const result = this.gameState.pass(1)
    if (result.success) {
      console.log('过牌成功')
      this.selectedCards = []
      this.updateUI()

      // 模拟AI出牌
      setTimeout(() => {
        this.simulateAIPlaying()
      }, 1000)
    }
  }

  // 提示功能
  showHint() {
    console.log('显示提示')
    // TODO: 实现提示逻辑
  }

  // 模拟AI出牌
  simulateAIPlaying() {
    const gameInfo = this.gameState.getGameInfo()
    if (gameInfo.phase !== 'playing') return

    const currentPlayer = gameInfo.currentPlayer
    if (currentPlayer === 0) return // 轮到玩家1，不需要模拟

    // 简单的AI逻辑：随机选择过牌或出牌
    const shouldPlay = Math.random() > 0.3 // 70%概率出牌

    if (shouldPlay) {
      // 模拟AI出牌（这里简化处理，实际需要AI算法）
      const aiPlayer = this.gameState.getPlayer(currentPlayer + 1)
      if (aiPlayer && aiPlayer.hand.getCardCount() > 0) {
        const cards = aiPlayer.hand.getCards()
        // 随机选择一张牌出
        const randomCard = [cards[Math.floor(Math.random() * cards.length)]]
        const result = this.gameState.playCards(currentPlayer + 1, randomCard)

        if (result.success) {
          console.log(`AI玩家${currentPlayer + 1} 出牌成功`)
          this.updateUI()

          if (result.gameOver) {
            console.log(`AI玩家${currentPlayer + 1} 获胜！`)
            this.uiElements.gamePhase.setText(`游戏结束 - 玩家${currentPlayer + 1}赢了！`)
            return
          }
        } else {
          // 出牌失败，改为过牌
          this.gameState.pass(currentPlayer + 1)
          console.log(`AI玩家${currentPlayer + 1} 过牌`)
        }
      }
    } else {
      // AI选择过牌
      this.gameState.pass(currentPlayer + 1)
      console.log(`AI玩家${currentPlayer + 1} 过牌`)
    }

    this.updateUI()

    // 继续下一轮
    setTimeout(() => {
      this.simulateAIPlaying()
    }, 1000)
  }
}

// 游戏管理器类
export default class GameScene {
  constructor(container) {
    this.container = container
    this.game = null
    this.init()
  }

  init() {
    try {
      console.log('GameScene: 初始化游戏')

      const config = {
        type: Phaser.AUTO,
        width: 800,
        height: 600,
        parent: this.container,
        backgroundColor: '#0f5132',
        scene: [GameMainScene],
        scale: {
          mode: Phaser.Scale.FIT,
          autoCenter: Phaser.Scale.CENTER_BOTH
        }
      }

      this.game = new Phaser.Game(config)
      console.log('GameScene: 游戏初始化成功')
    } catch (error) {
      console.error('GameScene: 游戏初始化失败', error)
      throw error
    }
  }

  destroy() {
    if (this.game) {
      console.log('GameScene: 销毁游戏实例')
      this.game.destroy(true)
      this.game = null
    }
  }
}
