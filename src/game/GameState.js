import { Deck, PlayerHand } from './Card.js'
import { CardPattern } from './CardPattern.js'

// 游戏状态管理类
export class GameState {
  constructor() {
    this.phase = 'waiting' // waiting, dealing, bidding, playing, finished
    this.players = [
      { id: 1, name: '玩家1 (我)', hand: new PlayerHand(), isLandlord: false, position: 'bottom' },
      { id: 2, name: '玩家2', hand: new PlayerHand(), isLandlord: false, position: 'left' },
      { id: 3, name: '玩家3', hand: new PlayerHand(), isLandlord: false, position: 'right' }
    ]
    this.deck = new Deck()
    this.landlordCards = [] // 地主牌（底牌）
    this.currentPlayer = 0 // 当前操作玩家索引
    this.biddingRound = 0 // 叫牌轮次
    this.biddingHistory = [] // 叫牌历史
    this.landlordId = null // 地主ID
    this.lastPlayedCards = [] // 上一次出的牌
    this.lastPlayedPattern = null // 上一次出牌的牌型
    this.lastPlayerId = null // 上一次出牌的玩家ID
    this.passCount = 0 // 连续过牌次数
    this.cardPattern = new CardPattern() // 牌型识别器
  }

  // 开始游戏
  startGame() {
    console.log('GameState: 开始游戏')
    this.phase = 'dealing'
    this.deck.shuffle()
    this.dealCards()
  }

  // 发牌
  dealCards() {
    console.log('GameState: 发牌')
    
    // 每个玩家发17张牌
    this.players.forEach(player => {
      const cards = this.deck.deal(17)
      player.hand.addCards(cards)
    })
    
    // 剩余3张作为地主牌
    this.landlordCards = this.deck.deal(3)
    
    console.log('GameState: 发牌完成，开始叫牌')
    this.phase = 'bidding'
    this.currentPlayer = 0
  }

  // 叫牌
  bid(playerId, bidType) {
    console.log(`GameState: 玩家${playerId} 叫牌: ${bidType}`)
    
    this.biddingHistory.push({
      playerId,
      bidType,
      round: this.biddingRound
    })

    if (bidType === 'landlord') {
      // 有人叫地主
      this.landlordId = playerId
      this.setLandlord(playerId)
      this.phase = 'playing'
      this.currentPlayer = this.players.findIndex(p => p.id === playerId)
      console.log(`GameState: 玩家${playerId} 成为地主，游戏开始`)
    } else {
      // 不叫，轮到下一个玩家
      this.currentPlayer = (this.currentPlayer + 1) % 3
      
      // 检查是否所有人都不叫
      const currentRoundBids = this.biddingHistory.filter(bid => bid.round === this.biddingRound)
      if (currentRoundBids.length === 3 && currentRoundBids.every(bid => bid.bidType === 'pass')) {
        // 所有人都不叫，重新开始
        console.log('GameState: 所有人都不叫，重新开始')
        this.restartGame()
      }
    }
  }

  // 设置地主
  setLandlord(playerId) {
    this.players.forEach(player => {
      player.isLandlord = player.id === playerId
    })
    
    // 地主获得底牌
    const landlord = this.players.find(p => p.id === playerId)
    if (landlord) {
      landlord.hand.addCards(this.landlordCards)
    }
  }

  // 出牌
  playCards(playerId, cards) {
    console.log(`GameState: 玩家${playerId} 出牌`, cards)

    const player = this.players.find(p => p.id === playerId)
    if (!player) return { success: false, message: '玩家不存在' }

    // 验证牌型
    const pattern = this.cardPattern.identifyPattern(cards)
    if (!pattern) {
      return { success: false, message: '无效的牌型' }
    }

    // 验证是否可以出牌
    if (!this.cardPattern.canPlay(cards, this.lastPlayedPattern)) {
      return { success: false, message: '牌型不符合规则' }
    }

    // 验证玩家是否拥有这些牌
    const hasAllCards = cards.every(card =>
      player.hand.hasCard(card.id)
    )
    if (!hasAllCards) {
      return { success: false, message: '玩家没有这些牌' }
    }

    // 移除玩家手中的牌
    player.hand.removeCards(cards)

    // 更新游戏状态
    this.lastPlayedCards = cards
    this.lastPlayedPattern = pattern
    this.lastPlayerId = playerId
    this.passCount = 0

    console.log(`GameState: 玩家${playerId} 出牌成功，牌型: ${pattern.type}`)

    // 检查是否有玩家出完牌
    if (player.hand.getCardCount() === 0) {
      this.phase = 'finished'
      console.log(`GameState: 玩家${playerId} 获胜！`)
      return { success: true, gameOver: true, winner: playerId }
    }

    // 轮到下一个玩家
    this.currentPlayer = (this.currentPlayer + 1) % 3
    return { success: true, pattern: pattern }
  }

  // 过牌
  pass(playerId) {
    console.log(`GameState: 玩家${playerId} 过牌`)

    this.passCount++

    // 如果连续两个玩家过牌，清空上次出牌记录
    if (this.passCount >= 2) {
      this.lastPlayedCards = []
      this.lastPlayedPattern = null
      this.lastPlayerId = null
      this.passCount = 0
      console.log('GameState: 清空上次出牌记录，新一轮开始')
    }

    // 轮到下一个玩家
    this.currentPlayer = (this.currentPlayer + 1) % 3
    return { success: true }
  }

  // 重新开始游戏
  restartGame() {
    this.phase = 'waiting'
    this.players.forEach(player => {
      player.hand = new PlayerHand()
      player.isLandlord = false
    })
    this.deck = new Deck()
    this.landlordCards = []
    this.currentPlayer = 0
    this.biddingRound++
    this.biddingHistory = []
    this.landlordId = null
    this.lastPlayedCards = []
    this.lastPlayedPattern = null
    this.lastPlayerId = null
    this.passCount = 0
  }

  // 获取当前玩家
  getCurrentPlayer() {
    return this.players[this.currentPlayer]
  }

  // 获取玩家
  getPlayer(id) {
    return this.players.find(p => p.id === id)
  }

  // 获取游戏状态
  getGameInfo() {
    return {
      phase: this.phase,
      currentPlayer: this.currentPlayer,
      landlordId: this.landlordId,
      biddingHistory: this.biddingHistory,
      lastPlayedCards: this.lastPlayedCards,
      lastPlayedPattern: this.lastPlayedPattern,
      lastPlayerId: this.lastPlayerId,
      passCount: this.passCount,
      players: this.players.map(p => ({
        id: p.id,
        name: p.name,
        cardCount: p.hand.getCardCount(),
        isLandlord: p.isLandlord,
        position: p.position
      }))
    }
  }

  // 获取牌型识别器
  getCardPattern() {
    return this.cardPattern
  }

  // 验证选中的牌是否可以出
  validateSelectedCards(cards) {
    return this.cardPattern.canPlay(cards, this.lastPlayedPattern)
  }
}
