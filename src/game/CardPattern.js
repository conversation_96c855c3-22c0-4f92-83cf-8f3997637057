// 牌型识别和验证类
export class CardPattern {
  constructor() {
    this.patterns = {
      SINGLE: 'single',           // 单牌
      PAIR: 'pair',              // 对子
      TRIPLE: 'triple',          // 三张
      TRIPLE_WITH_SINGLE: 'triple_with_single',  // 三带一
      TRIPLE_WITH_PAIR: 'triple_with_pair',      // 三带二
      STRAIGHT: 'straight',      // 顺子
      PAIR_STRAIGHT: 'pair_straight',  // 连对
      TRIPLE_STRAIGHT: 'triple_straight', // 飞机
      BOMB: 'bomb',              // 炸弹
      ROCKET: 'rocket'           // 火箭（双王）
    }
  }

  // 识别牌型
  identifyPattern(cards) {
    if (!cards || cards.length === 0) {
      return null
    }

    // 按点数分组
    const groups = this.groupByRank(cards)
    const groupSizes = Object.values(groups).map(group => group.length).sort((a, b) => b - a)
    const _groupCount = Object.keys(groups).length

    // 火箭（双王）
    if (cards.length === 2 && this.isRocket(cards)) {
      return {
        type: this.patterns.ROCKET,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.ROCKET)
      }
    }

    // 炸弹
    if (cards.length === 4 && groupSizes[0] === 4) {
      return {
        type: this.patterns.BOMB,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.BOMB)
      }
    }

    // 单牌
    if (cards.length === 1) {
      return {
        type: this.patterns.SINGLE,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.SINGLE)
      }
    }

    // 对子
    if (cards.length === 2 && groupSizes[0] === 2) {
      return {
        type: this.patterns.PAIR,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.PAIR)
      }
    }

    // 三张
    if (cards.length === 3 && groupSizes[0] === 3) {
      return {
        type: this.patterns.TRIPLE,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.TRIPLE)
      }
    }

    // 三带一
    if (cards.length === 4 && groupSizes[0] === 3 && groupSizes[1] === 1) {
      return {
        type: this.patterns.TRIPLE_WITH_SINGLE,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.TRIPLE_WITH_SINGLE)
      }
    }

    // 三带二
    if (cards.length === 5 && groupSizes[0] === 3 && groupSizes[1] === 2) {
      return {
        type: this.patterns.TRIPLE_WITH_PAIR,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.TRIPLE_WITH_PAIR)
      }
    }

    // 顺子
    if (cards.length >= 5 && this.isStraight(cards)) {
      return {
        type: this.patterns.STRAIGHT,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.STRAIGHT)
      }
    }

    // 连对
    if (cards.length >= 6 && cards.length % 2 === 0 && this.isPairStraight(cards)) {
      return {
        type: this.patterns.PAIR_STRAIGHT,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.PAIR_STRAIGHT)
      }
    }

    // 飞机（三张连续）
    if (cards.length >= 6 && this.isTripleStraight(cards)) {
      return {
        type: this.patterns.TRIPLE_STRAIGHT,
        cards: cards,
        weight: this.calculateWeight(cards, this.patterns.TRIPLE_STRAIGHT)
      }
    }

    return null // 无效牌型
  }

  // 按点数分组
  groupByRank(cards) {
    const groups = {}
    cards.forEach(card => {
      const rank = card.rank
      if (!groups[rank]) {
        groups[rank] = []
      }
      groups[rank].push(card)
    })
    return groups
  }

  // 检查是否为火箭
  isRocket(cards) {
    if (cards.length !== 2) return false
    const ranks = cards.map(card => card.rank).sort()
    return ranks[0] === 'small_joker' && ranks[1] === 'big_joker'
  }

  // 检查是否为顺子
  isStraight(cards) {
    if (cards.length < 5) return false
    
    // 王不能组成顺子
    if (cards.some(card => card.suit === 'joker')) return false
    
    const values = cards.map(card => card.value).sort((a, b) => a - b)
    
    // 检查是否连续
    for (let i = 1; i < values.length; i++) {
      if (values[i] !== values[i-1] + 1) {
        return false
      }
    }
    
    // 2不能在顺子中（除了A-2的特殊情况）
    if (values.includes(15) && values[values.length - 1] !== 15) {
      return false
    }
    
    return true
  }

  // 检查是否为连对
  isPairStraight(cards) {
    if (cards.length < 6 || cards.length % 2 !== 0) return false
    
    const groups = this.groupByRank(cards)
    const ranks = Object.keys(groups)
    
    // 每个点数必须恰好有2张
    if (!ranks.every(rank => groups[rank].length === 2)) {
      return false
    }
    
    // 检查点数是否连续
    const values = ranks.map(rank => {
      const card = groups[rank][0]
      return card.value
    }).sort((a, b) => a - b)
    
    return this.isConsecutive(values)
  }

  // 检查是否为飞机
  isTripleStraight(cards) {
    const groups = this.groupByRank(cards)
    const triples = []
    const others = []
    
    Object.entries(groups).forEach(([rank, cardGroup]) => {
      if (cardGroup.length === 3) {
        triples.push(rank)
      } else {
        others.push(...cardGroup)
      }
    })
    
    if (triples.length < 2) return false
    
    // 检查三张是否连续
    const tripleValues = triples.map(rank => {
      const card = groups[rank][0]
      return card.value
    }).sort((a, b) => a - b)
    
    if (!this.isConsecutive(tripleValues)) return false
    
    // 检查带牌数量是否正确
    const expectedOthers = triples.length // 飞机不带或带单牌
    const expectedOthers2 = triples.length * 2 // 飞机带对子
    
    return others.length === 0 || others.length === expectedOthers || others.length === expectedOthers2
  }

  // 检查数组是否连续
  isConsecutive(values) {
    for (let i = 1; i < values.length; i++) {
      if (values[i] !== values[i-1] + 1) {
        return false
      }
    }
    return true
  }

  // 计算牌型权重（用于比较大小）
  calculateWeight(cards, patternType) {
    const baseWeights = {
      [this.patterns.SINGLE]: 1000,
      [this.patterns.PAIR]: 2000,
      [this.patterns.TRIPLE]: 3000,
      [this.patterns.TRIPLE_WITH_SINGLE]: 4000,
      [this.patterns.TRIPLE_WITH_PAIR]: 5000,
      [this.patterns.STRAIGHT]: 6000,
      [this.patterns.PAIR_STRAIGHT]: 7000,
      [this.patterns.TRIPLE_STRAIGHT]: 8000,
      [this.patterns.BOMB]: 9000,
      [this.patterns.ROCKET]: 10000
    }

    const baseWeight = baseWeights[patternType] || 0
    
    // 获取主牌的点数（用于同类型比较）
    let mainValue = 0
    if (patternType === this.patterns.ROCKET) {
      mainValue = 100 // 火箭最大
    } else if (patternType === this.patterns.BOMB) {
      mainValue = cards[0].value // 炸弹取任意一张的值
    } else {
      // 其他牌型取最大值的牌
      mainValue = Math.max(...cards.map(card => card.value))
    }

    return baseWeight + mainValue
  }

  // 比较两个牌型
  comparePatterns(pattern1, pattern2) {
    if (!pattern1 || !pattern2) return 0
    
    // 火箭最大
    if (pattern1.type === this.patterns.ROCKET) return 1
    if (pattern2.type === this.patterns.ROCKET) return -1
    
    // 炸弹大于其他牌型（除火箭）
    if (pattern1.type === this.patterns.BOMB && pattern2.type !== this.patterns.BOMB) return 1
    if (pattern2.type === this.patterns.BOMB && pattern1.type !== this.patterns.BOMB) return -1
    
    // 同类型比较权重
    if (pattern1.type === pattern2.type) {
      return pattern1.weight > pattern2.weight ? 1 : (pattern1.weight < pattern2.weight ? -1 : 0)
    }
    
    // 不同类型无法比较
    return 0
  }

  // 验证出牌是否合法
  canPlay(cards, lastPattern = null) {
    const pattern = this.identifyPattern(cards)
    if (!pattern) return false
    
    // 如果没有上家出牌，任何合法牌型都可以出
    if (!lastPattern) return true
    
    // 比较牌型
    return this.comparePatterns(pattern, lastPattern) > 0
  }
}
