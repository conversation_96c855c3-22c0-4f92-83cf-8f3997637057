import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import { BrowserRouter, Routes, Route } from 'react-router-dom'
import { ConfigProvider } from 'antd'
import AuthGuard from './components/AuthGuard'
import Layout from './components/Layout'
import LoginPage from './pages/Login'
import AuthCallback from './pages/AuthCallback'
import GameHall from './pages/GameHall'
import WaitingRoom from './pages/WaitingRoom'
import GameRoom from './pages/GameRoom'
import GameResult from './pages/GameResult'
import Ranking from './pages/Ranking'
import UITest from './pages/UITest'
import 'antd/dist/reset.css'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <ConfigProvider>
      <BrowserRouter>
        <AuthGuard>
          <Routes>
            <Route path="/" element={<Layout />}>
              <Route index element={<LoginPage />} />
              <Route path="auth/callback" element={<AuthCallback />} />
              <Route path="hall" element={<GameHall />} />
              <Route path="ui-test" element={<UITest />} />
              <Route path="waiting/:gameId" element={<WaitingRoom />} />
              <Route path="game/:gameId/:roomId" element={<GameRoom />} />
              <Route path="result/:gameId" element={<GameResult />} />
              <Route path="ranking/:gameId" element={<Ranking />} />
            </Route>
          </Routes>
        </AuthGuard>
      </BrowserRouter>
    </ConfigProvider>
  </React.StrictMode>
)
