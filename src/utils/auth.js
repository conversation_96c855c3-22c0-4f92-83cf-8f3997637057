import api from '../api'

// Token管理
export const TokenManager = {
  // 获取Token
  getToken: () => {
    return localStorage.getItem('token')
  },

  // 设置Token
  setToken: (token) => {
    localStorage.setItem('token', token)
  },

  // 移除Token
  removeToken: () => {
    localStorage.removeItem('token')
    localStorage.removeItem('gameStore')
  },

  // 检查Token是否存在
  hasToken: () => {
    return !!localStorage.getItem('token')
  },

  // 验证Token是否有效
  validateToken: async () => {
    try {
      // 在开发模式下，如果是模拟token，直接返回true
      const token = TokenManager.getToken()
      if (token && token.startsWith('mock_token_')) {
        console.log('TokenManager: 模拟token验证通过')
        return true
      }

      await api.validateToken()
      return true
    } catch (error) {
      console.log('TokenManager: token验证失败，清除token')
      TokenManager.removeToken()
      return false
    }
  }
}

// 微信授权相关
export const WechatAuth = {
  // 获取微信授权URL
  getAuthUrl: () => {
    return api.getWechatAuthUrl()
  },

  // 从URL中获取授权码
  getCodeFromUrl: () => {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get('code')
  },

  // 检查是否是微信浏览器
  isWechatBrowser: () => {
    const ua = navigator.userAgent.toLowerCase()
    return ua.includes('micromessenger')
  },

  // 模拟微信授权（开发环境使用）
  mockWechatAuth: () => {
    const mockUsers = [
      { nickname: '测试玩家A', avatar: '/images/headimage/avatar_1.png' },
      { nickname: '测试玩家B', avatar: '/images/headimage/avatar_2.png' },
      { nickname: '测试玩家C', avatar: '/images/headimage/avatar_3.png' },
      { nickname: '测试玩家D', avatar: '/images/headimage/avatar_4.png' }
    ]

    const randomUser = mockUsers[Math.floor(Math.random() * mockUsers.length)]

    return {
      code: 'mock_wechat_code_' + Date.now(),
      state: Date.now().toString(),
      mockUser: {
        ...randomUser,
        id: Date.now(),
        openid: 'mock_openid_' + Date.now(),
        unionid: 'mock_unionid_' + Date.now()
      }
    }
  }
}

// 用户信息管理
export const UserManager = {
  // 获取用户信息
  getUserInfo: () => {
    try {
      const gameStore = localStorage.getItem('gameStore')
      if (gameStore) {
        const parsed = JSON.parse(gameStore)
        return parsed.user
      }
      return null
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  },

  // 设置用户信息
  setUserInfo: (userInfo) => {
    try {
      const gameStore = localStorage.getItem('gameStore') || '{}'
      const parsed = JSON.parse(gameStore)
      parsed.user = userInfo
      localStorage.setItem('gameStore', JSON.stringify(parsed))
    } catch (error) {
      console.error('设置用户信息失败:', error)
    }
  },

  // 清除用户信息
  clearUserInfo: () => {
    localStorage.removeItem('gameStore')
  },

  // 检查用户是否已登录
  isLoggedIn: () => {
    return TokenManager.hasToken() && !!UserManager.getUserInfo()
  }
}

// 登录流程管理
export const LoginManager = {
  // 执行登录
  login: async (code) => {
    try {
      const response = await api.getUserByCode(code)
      
      if (response.code === 0) {
        // 保存用户信息
        UserManager.setUserInfo(response.result)
        
        // 保存Token
        if (response.token) {
          TokenManager.setToken(response.token.access_token)
        }
        
        return {
          success: true,
          user: response.result,
          token: response.token
        }
      } else {
        return {
          success: false,
          message: response.msg || '登录失败'
        }
      }
    } catch (error) {
      console.error('登录失败:', error)
      return {
        success: false,
        message: error.message || '登录失败，请重试'
      }
    }
  },

  // 执行登出
  logout: () => {
    TokenManager.removeToken()
    UserManager.clearUserInfo()
  },

  // 自动登录检查
  checkAutoLogin: async () => {
    if (!TokenManager.hasToken()) {
      return false
    }

    try {
      const isValid = await TokenManager.validateToken()
      if (!isValid) {
        LoginManager.logout()
        return false
      }
      
      const userInfo = UserManager.getUserInfo()
      return !!userInfo
    } catch (error) {
      console.error('自动登录检查失败:', error)
      LoginManager.logout()
      return false
    }
  }
}

// 路由守卫
export const RouteGuard = {
  // 检查是否需要登录
  requireAuth: (path) => {
    const publicPaths = ['/', '/auth/callback']
    return !publicPaths.includes(path)
  },

  // 重定向到登录页
  redirectToLogin: () => {
    window.location.href = '/'
  },

  // 重定向到指定页面
  redirectTo: (path) => {
    window.location.href = path
  }
}
