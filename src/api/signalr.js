import * as signalR from '@microsoft/signalr'

let connection = null

// SignalR连接管理类
class SignalRManager {
  constructor() {
    this.connection = null
    this.connectionState = 'disconnected'
    this.callbacks = new Map()
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
  }

  // 初始化连接
  async initConnection(hubUrl = null) {
    if (this.connection && this.connectionState === 'connected') {
      console.log('SignalR: 连接已存在')
      return this.connection
    }

    const url = hubUrl || import.meta.env.VITE_SIGNALR_HUB_URL || '/signalrhub'
    const token = localStorage.getItem('token')

    this.connectionState = 'connecting'

    this.connection = new signalR.HubConnectionBuilder()
      .withUrl(url, {
        accessTokenFactory: () => token
      })
      .configureLogging(import.meta.env.VITE_GAME_DEBUG ? signalR.LogLevel.Information : signalR.LogLevel.Warning)
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: retryContext => {
          if (retryContext.previousRetryCount < this.maxReconnectAttempts) {
            return this.reconnectInterval
          }
          return null // 停止重连
        }
      })
      .build()

    // 注册连接事件
    this.connection.onclose(this.onConnectionClosed.bind(this))
    this.connection.onreconnecting(this.onReconnecting.bind(this))
    this.connection.onreconnected(this.onReconnected.bind(this))

    try {
      await this.connection.start()
      this.connectionState = 'connected'
      this.reconnectAttempts = 0
      console.log('SignalR: 连接成功')

      // 重新注册所有回调
      this.reregisterCallbacks()

      return this.connection
    } catch (error) {
      this.connectionState = 'disconnected'
      console.error('SignalR: 连接失败', error)
      throw error
    }
  }

  // 注册事件回调
  registerCallback(eventName, handler) {
    if (!this.callbacks.has(eventName)) {
      this.callbacks.set(eventName, [])
    }
    this.callbacks.get(eventName).push(handler)

    // 如果连接已建立，立即注册
    if (this.connection && this.connectionState === 'connected') {
      this.connection.on(eventName, handler)
    }
  }

  // 批量注册回调
  registerCallbacks(callbacks) {
    Object.entries(callbacks).forEach(([eventName, handler]) => {
      this.registerCallback(eventName, handler)
    })
  }

  // 重新注册所有回调（重连后使用）
  reregisterCallbacks() {
    if (!this.connection) return

    this.callbacks.forEach((handlers, eventName) => {
      handlers.forEach(handler => {
        this.connection.on(eventName, handler)
      })
    })
  }

  // 发送消息到服务器
  async invoke(methodName, ...args) {
    if (!this.connection || this.connectionState !== 'connected') {
      throw new Error('SignalR: 连接未建立')
    }

    try {
      return await this.connection.invoke(methodName, ...args)
    } catch (error) {
      console.error(`SignalR: 调用方法 ${methodName} 失败`, error)
      throw error
    }
  }

  // 连接关闭事件
  onConnectionClosed(error) {
    this.connectionState = 'disconnected'
    console.log('SignalR: 连接已关闭', error)

    // 触发自定义重连逻辑
    if (import.meta.env.VITE_GAME_AUTO_RECONNECT === 'true') {
      this.attemptReconnect()
    }
  }

  // 重连中事件
  onReconnecting(error) {
    this.connectionState = 'reconnecting'
    console.log('SignalR: 正在重连...', error)
  }

  // 重连成功事件
  onReconnected(connectionId) {
    this.connectionState = 'connected'
    this.reconnectAttempts = 0
    console.log('SignalR: 重连成功', connectionId)
  }

  // 手动重连
  async attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('SignalR: 达到最大重连次数，停止重连')
      return
    }

    this.reconnectAttempts++
    console.log(`SignalR: 第 ${this.reconnectAttempts} 次重连尝试`)

    setTimeout(async () => {
      try {
        await this.initConnection()
      } catch (error) {
        console.error('SignalR: 重连失败', error)
        this.attemptReconnect()
      }
    }, this.reconnectInterval)
  }

  // 断开连接
  async disconnect() {
    if (this.connection) {
      this.connectionState = 'disconnected'
      await this.connection.stop()
      this.connection = null
      console.log('SignalR: 连接已断开')
    }
  }

  // 获取连接状态
  getConnectionState() {
    return this.connectionState
  }

  // 检查是否已连接
  isConnected() {
    return this.connectionState === 'connected'
  }
}

// 创建全局实例
const signalRManager = new SignalRManager()

// 导出兼容的API
export const initSignalRConnection = (hubUrl) => {
  return signalRManager.initConnection(hubUrl)
}

export const registerCallbacks = (callbacks) => {
  signalRManager.registerCallbacks(callbacks)
}

export const disconnectSignalR = () => {
  return signalRManager.disconnect()
}

// 导出管理器实例
export { signalRManager }

// 导出连接实例（向后兼容）
export { connection }
