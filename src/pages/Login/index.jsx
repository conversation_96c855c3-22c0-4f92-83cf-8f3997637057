import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { <PERSON><PERSON>, Card, message } from 'antd'
import { WechatOutlined } from '@ant-design/icons'
import { observer } from 'mobx-react-lite'
import { gameStore } from '../../store/gameStore'
import { LoginManager, WechatAuth, UserManager, TokenManager } from '../../utils/auth'
import './style.css'

const Login = observer(() => {
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)


  // 检查自动登录
  useEffect(() => {
    checkAutoLogin()
  }, [])

  const checkAutoLogin = async () => {
    if (UserManager.isLoggedIn()) {
      const isValid = await LoginManager.checkAutoLogin()
      if (isValid) {
        message.success('自动登录成功！')
        navigate('/hall')
      }
    }
  }

  // 微信授权登录
  const handleWechatLogin = async () => {
    setLoading(true)
    try {
      if (WechatAuth.isWechatBrowser()) {
        // 真实微信环境
        const authUrl = WechatAuth.getAuthUrl()
        window.location.href = authUrl
      } else {
        // 非微信环境，使用模拟登录
        await handleMockLogin()
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      message.error('微信登录失败，请重试')
      setLoading(false)
    }
  }

  // 模拟登录（开发测试用）
  const handleMockLogin = async () => {
    try {
      const mockAuth = WechatAuth.mockWechatAuth()
      const result = await LoginManager.login(mockAuth.code)

      if (result.success) {
        // 更新游戏状态
        gameStore.setUser(result.user)
        if (result.token) {
          gameStore.setToken(result.token.access_token)
          TokenManager.setToken(result.token.access_token)
        }

        message.success('登录成功！')
        navigate('/hall')
      } else {
        // 如果后端接口失败，使用完全模拟的数据
        const mockUser = {
          id: mockAuth.mockUser.id,
          openid: mockAuth.mockUser.openid,
          nickname: mockAuth.mockUser.nickname,
          headerimg: mockAuth.mockUser.avatar,
          unionid: mockAuth.mockUser.unionid,
          time: new Date().toISOString()
        }

        gameStore.setUser(mockUser)
        const mockToken = 'mock_token_' + Date.now()
        gameStore.setToken(mockToken)
        TokenManager.setToken(mockToken)
        UserManager.setUserInfo(mockUser)

        message.success(`欢迎 ${mockUser.nickname}！`)
        console.log('登录成功，准备跳转到大厅...')
        console.log('用户信息:', mockUser)
        console.log('Token:', mockToken)
        console.log('UserManager.isLoggedIn():', UserManager.isLoggedIn())

        // 延迟一下再跳转，确保状态已保存
        setTimeout(() => {
          navigate('/hall')
        }, 100)
      }
    } catch (error) {
      console.error('模拟登录失败:', error)
      message.error('登录失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="login-container">
      <div className="login-background">
        <img 
          src="/resources/bg_login.jpg" 
          alt="背景" 
          className="background-image"
        />
      </div>
      
      <div className="login-content">
        <Card className="login-card">
          <div className="login-header">
            <img
              src="/images/headimage/avatar_1.png"
              alt="游戏Logo"
              className="game-logo"
            />
            <h1 className="game-title">斗地主竞技赛</h1>
            <p className="game-subtitle">线上智力竞技比赛选拔系统</p>
          </div>
          
          <div className="login-actions">
            <Button
              type="primary"
              size="large"
              icon={<WechatOutlined />}
              loading={loading}
              onClick={handleWechatLogin}
              className="wechat-login-btn"
              block
            >
              {loading ? '登录中...' : WechatAuth.isWechatBrowser() ? '微信授权登录' : '模拟登录（测试）'}
            </Button>

            {!WechatAuth.isWechatBrowser() && (
              <div className="dev-notice">
                <p style={{ fontSize: '12px', color: '#ff6b35', marginTop: '8px' }}>
                  当前为开发模式，将使用模拟登录
                </p>
              </div>
            )}

            <p className="login-tips">
              点击登录即表示同意用户协议和隐私政策
            </p>
          </div>
        </Card>
      </div>
    </div>
  )
})

export default Login
