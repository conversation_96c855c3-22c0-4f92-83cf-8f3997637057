.ui-test-page {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.test-header h1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 16px !important;
}

.test-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
}

.test-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.test-card .ant-card-head {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px 12px 0 0;
  border-bottom: none;
}

.test-card .ant-card-head-title {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.test-card .ant-card-body {
  padding: 24px;
}

/* 游戏主题卡片特殊样式 */
.game-theme-card .ant-card-body {
  padding: 32px;
}

.theme-demo {
  padding: 24px;
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.theme-demo:hover {
  transform: scale(1.05);
  border-color: #1890ff;
}

.theme-classic {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #8b4513;
}

.theme-modern {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #2c3e50;
}

.theme-dark {
  background: linear-gradient(135deg, #434343 0%, #000000 100%);
  color: white;
}

.theme-demo h4 {
  margin-bottom: 8px !important;
  font-weight: 600;
}

.theme-demo p {
  margin-bottom: 16px !important;
  opacity: 0.8;
}

/* 按钮特效 */
.test-card .ant-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.test-card .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.test-card .ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.test-card .ant-btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 进度条样式 */
.ant-progress-line {
  margin-bottom: 8px;
}

.ant-progress-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* 标签样式 */
.ant-tag {
  border-radius: 6px;
  font-weight: 500;
  padding: 4px 12px;
  border: none;
}

/* 输入框样式 */
.test-card .ant-input,
.test-card .ant-select-selector {
  border-radius: 8px;
  border: 2px solid #e8e8e8;
  transition: all 0.3s ease;
}

.test-card .ant-input:focus,
.test-card .ant-select-focused .ant-select-selector {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 滑块样式 */
.ant-slider-rail {
  background: #e8e8e8;
}

.ant-slider-track {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.ant-slider-handle {
  border: 3px solid #667eea;
  background: white;
}

.ant-slider-handle:hover {
  border-color: #5a6fd8;
}

/* 开关样式 */
.ant-switch-checked {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 警告框样式 */
.ant-alert {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 页脚样式 */
.test-footer {
  text-align: center;
  margin-top: 32px;
  padding: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ui-test-page {
    padding: 16px;
  }
  
  .test-header {
    padding: 16px;
    margin-bottom: 24px;
  }
  
  .test-card .ant-card-body {
    padding: 16px;
  }
  
  .game-theme-card .ant-card-body {
    padding: 20px;
  }
  
  .theme-demo {
    padding: 16px;
    margin-bottom: 16px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.test-card {
  animation: fadeInUp 0.6s ease-out;
}

.test-card:nth-child(1) { animation-delay: 0.1s; }
.test-card:nth-child(2) { animation-delay: 0.2s; }
.test-card:nth-child(3) { animation-delay: 0.3s; }
.test-card:nth-child(4) { animation-delay: 0.4s; }

/* 特殊效果 */
.test-header h1 {
  position: relative;
}

.test-header h1::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}
