import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Card, Button, Progress, message, Spin, List, Avatar, Badge, Divider } from 'antd'
import {
  ClockCircleOutlined,
  UserOutlined,
  WifiOutlined,
  TeamOutlined,
  TrophyOutlined,
  CheckCircleOutlined
} from '@ant-design/icons'
import { observer } from 'mobx-react-lite'
import { gameStore } from '../../store/gameStore'
import api from '../../api'

const WaitingRoom = observer(() => {
  const { gameId } = useParams()
  const navigate = useNavigate()
  const [countdown, setCountdown] = useState(null) // 动态计算倒计时
  const [playerCount, setPlayerCount] = useState(12)
  const [totalPlayers, setTotalPlayers] = useState(18)
  const [gameConfig, setGameConfig] = useState(null)
  const [loading, setLoading] = useState(true)
  const [playerList, setPlayerList] = useState([])
  const [roomInfo, setRoomInfo] = useState(null)
  const [connectionStatus, setConnectionStatus] = useState('connected')
  const [autoRefresh, setAutoRefresh] = useState(true)

  // 初始化页面数据
  useEffect(() => {
    initializeWaitingRoom()
  }, [gameId])

  // 倒计时效果
  useEffect(() => {
    if (countdown === null) return

    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          // 倒计时结束，进入游戏
          navigate(`/game/${gameId}/1`)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [countdown, gameId, navigate])

  // 自动刷新玩家数据
  useEffect(() => {
    if (!autoRefresh) return

    const refreshTimer = setInterval(() => {
      refreshPlayerData()
    }, 5000) // 每5秒刷新一次

    return () => clearInterval(refreshTimer)
  }, [autoRefresh, gameId])

  // 模拟获取玩家列表
  const refreshPlayerData = async () => {
    try {
      // 这里应该调用真实的API获取玩家列表
      // const response = await api.getSignUpList(gameId)

      // 模拟数据
      const mockPlayers = Array.from({ length: playerCount }, (_, index) => ({
        id: index + 1,
        nickname: `玩家${index + 1}`,
        avatar: `/images/headimage/avatar_${(index % 4) + 1}.png`,
        isOnline: Math.random() > 0.1, // 90%在线率
        joinTime: new Date(Date.now() - Math.random() * 3600000).toISOString()
      }))

      setPlayerList(mockPlayers)
      setConnectionStatus('connected')
    } catch (error) {
      console.error('刷新玩家数据失败:', error)
      setConnectionStatus('error')
    }
  }

  // 初始化等待房间
  const initializeWaitingRoom = async () => {
    setLoading(true)
    try {
      // 从gameStore获取当前比赛信息
      const currentGame = gameStore.currentGame
      if (currentGame) {
        setGameConfig(currentGame)
        setTotalPlayers(currentGame.gc_maxplayers)

        // 设置固定的20秒倒计时
        setCountdown(20)

        // 模拟当前报名人数（实际应该从API获取）
        setPlayerCount(currentGame.signupCount || 12)

        // 获取房间信息
        await getRoomInfo()

        // 初始化玩家数据
        await refreshPlayerData()
      } else {
        message.error('未找到比赛信息，请返回大厅重新选择')
        navigate('/hall')
      }
    } catch (error) {
      console.error('初始化等待房间失败:', error)
      message.error('初始化失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 获取房间信息
  const getRoomInfo = async () => {
    try {
      if (gameStore.user) {
        // 这里应该调用真实的API获取房间信息
        // const response = await api.getRoom(gameStore.user.id, gameId)

        // 模拟房间信息
        const mockRoomInfo = {
          tableNumber: Math.floor(Math.random() * 20) + 1,
          round: 1,
          game: 1,
          status: 'waiting',
          estimatedStartTime: gameConfig?.gc_startime
        }

        setRoomInfo(mockRoomInfo)
        gameStore.setCurrentRoom(mockRoomInfo.tableNumber)
      }
    } catch (error) {
      console.error('获取房间信息失败:', error)
    }
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const progress = (playerCount / totalPlayers) * 100

  // 加载状态
  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '80vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <Spin size="large" />
      </div>
    )
  }

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '80vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <Card style={{
        width: 400,
        textAlign: 'center',
        borderRadius: 16,
        boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
      }}>
        <div style={{ marginBottom: 24 }}>
          <img
            src="/resources/logo_dengdai.png"
            alt="等待中"
            style={{
              width: 80,
              height: 80,
              marginBottom: 16,
              objectFit: 'contain',
              borderRadius: 8
            }}
          />
          <h2>{gameConfig?.gc_name || '等待比赛开始'}</h2>
        </div>

        <div style={{ marginBottom: 24 }}>
          <div style={{
            fontSize: 48,
            fontWeight: 'bold',
            color: countdown > 10 ? '#1890ff' : '#ff4d4f',
            marginBottom: 8
          }}>
            {countdown !== null ? formatTime(countdown) : '--:--'}
          </div>
          <div style={{ color: '#666' }}>
            <ClockCircleOutlined /> 距离比赛开始还有
          </div>
        </div>

        <div style={{ marginBottom: 24 }}>
          <Progress 
            percent={progress} 
            strokeColor="#52c41a"
            format={() => `${playerCount}/${totalPlayers}`}
          />
          <div style={{ marginTop: 8, color: '#666' }}>
            <UserOutlined /> 已入场玩家
          </div>
        </div>

        <div style={{
          background: '#f5f5f5',
          padding: 16,
          borderRadius: 8,
          marginBottom: 16
        }}>
          <div style={{ marginBottom: 12, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <h4 style={{ margin: 0, fontSize: 16, color: '#333' }}>
              <TeamOutlined style={{ marginRight: 8 }} />
              比赛信息
            </h4>
            <Badge
              status={connectionStatus === 'connected' ? 'success' : 'error'}
              text={connectionStatus === 'connected' ? '连接正常' : '连接异常'}
            />
          </div>
          <div style={{ textAlign: 'left', fontSize: 14, color: '#666' }}>
            <p style={{ margin: '4px 0' }}>
              <strong>比赛规模：</strong>{gameConfig?.gc_rounds || 2}轮 × {gameConfig?.gc_gamesperround || 3}局
            </p>
            <p style={{ margin: '4px 0' }}>
              <strong>最少人数：</strong>{gameConfig?.gc_minplayers || 6}人开赛
            </p>
            {roomInfo && (
              <p style={{ margin: '4px 0' }}>
                <strong>分配桌号：</strong>第 {roomInfo.tableNumber} 桌
              </p>
            )}
            <p style={{ margin: '4px 0' }}>
              <strong>比赛规则：</strong>{gameConfig?.gc_rules || '标准斗地主规则'}
            </p>
          </div>
        </div>

        {/* 玩家列表 */}
        {playerList.length > 0 && (
          <div style={{
            background: '#f9f9f9',
            padding: 16,
            borderRadius: 8,
            marginBottom: 16,
            maxHeight: 200,
            overflowY: 'auto'
          }}>
            <div style={{ marginBottom: 12, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <h4 style={{ margin: 0, fontSize: 16, color: '#333' }}>
                <UserOutlined style={{ marginRight: 8 }} />
                在线玩家
              </h4>
              <Button
                type="link"
                size="small"
                onClick={() => setAutoRefresh(!autoRefresh)}
                icon={<WifiOutlined />}
              >
                {autoRefresh ? '自动刷新' : '手动刷新'}
              </Button>
            </div>
            <List
              size="small"
              dataSource={playerList.slice(0, 8)} // 只显示前8个玩家
              renderItem={(player) => (
                <List.Item style={{ padding: '4px 0', border: 'none' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <Avatar
                      src={player.avatar}
                      size="small"
                      style={{ opacity: player.isOnline ? 1 : 0.5 }}
                    />
                    <span style={{
                      fontSize: 13,
                      color: player.isOnline ? '#333' : '#999',
                      textDecoration: player.isOnline ? 'none' : 'line-through'
                    }}>
                      {player.nickname}
                    </span>
                    {player.isOnline && <CheckCircleOutlined style={{ color: '#52c41a', fontSize: 12 }} />}
                  </div>
                </List.Item>
              )}
            />
            {playerList.length > 8 && (
              <div style={{ textAlign: 'center', marginTop: 8, fontSize: 12, color: '#999' }}>
                还有 {playerList.length - 8} 位玩家...
              </div>
            )}
          </div>
        )}

        <div style={{
          background: '#e6f7ff',
          padding: 12,
          borderRadius: 8,
          marginBottom: 16,
          border: '1px solid #91d5ff'
        }}>
          <p style={{ margin: 0, fontSize: 14, color: '#1890ff' }}>
            💡 比赛即将开始，请保持网络连接稳定
          </p>
        </div>

        <div style={{ display: 'flex', gap: 8 }}>
          <Button
            type="default"
            onClick={() => navigate('/hall')}
            style={{ flex: 1 }}
          >
            返回大厅
          </Button>
          <Button
            type="primary"
            disabled={countdown > 10}
            onClick={() => navigate(`/game/${gameId}/1`)}
            style={{ flex: 1 }}
          >
            {countdown > 10 ? '等待中...' : '进入游戏'}
          </Button>
        </div>
      </Card>
    </div>
  )
})

export default WaitingRoom
