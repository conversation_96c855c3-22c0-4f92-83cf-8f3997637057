.ai-test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Arial', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

.ai-test-header {
  text-align: center;
  margin-bottom: 40px;
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.ai-test-header h1 {
  font-size: 2.5em;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.ai-test-header p {
  font-size: 1.2em;
  opacity: 0.9;
  margin-bottom: 20px;
}

.test-controls {
  margin-top: 20px;
}

.test-button {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  padding: 15px 30px;
  font-size: 1.1em;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.test-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.test-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.test-button.running {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.test-results {
  margin-bottom: 40px;
}

.test-results h2 {
  font-size: 1.8em;
  margin-bottom: 20px;
  text-align: center;
}

.no-results {
  text-align: center;
  padding: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  opacity: 0.7;
}

.test-item {
  background: rgba(255, 255, 255, 0.1);
  margin-bottom: 20px;
  border-radius: 10px;
  padding: 20px;
  backdrop-filter: blur(5px);
  border-left: 5px solid transparent;
  transition: all 0.3s ease;
}

.test-item.running {
  border-left-color: #ffa500;
  animation: shimmer 2s infinite;
}

.test-item.passed {
  border-left-color: #00ff00;
}

.test-item.failed {
  border-left-color: #ff0000;
}

@keyframes shimmer {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.test-header h3 {
  margin: 0;
  font-size: 1.3em;
}

.test-status {
  font-size: 1.1em;
  font-weight: bold;
}

.test-details {
  line-height: 1.6;
}

.test-details p {
  margin: 8px 0;
}

.test-details strong {
  color: #ffd700;
}

.test-instructions {
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.test-instructions h2 {
  text-align: center;
  margin-bottom: 25px;
  font-size: 1.8em;
}

.instruction-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.instruction-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 10px;
}

.instruction-item h3 {
  margin-bottom: 15px;
  color: #ffd700;
}

.instruction-item ul {
  list-style: none;
  padding: 0;
}

.instruction-item li {
  padding: 5px 0;
  padding-left: 20px;
  position: relative;
}

.instruction-item li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #00ff00;
  font-weight: bold;
}

.real-game-link {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 15px;
}

.real-game-link h2 {
  margin-bottom: 15px;
  font-size: 1.8em;
}

.real-game-link p {
  margin-bottom: 20px;
  font-size: 1.1em;
  opacity: 0.9;
}

.game-link-button {
  display: inline-block;
  background: linear-gradient(45deg, #00c851, #007e33);
  color: white;
  text-decoration: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 1.1em;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.game-link-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  text-decoration: none;
  color: white;
}

@media (max-width: 768px) {
  .ai-test-container {
    padding: 10px;
  }
  
  .ai-test-header h1 {
    font-size: 2em;
  }
  
  .instruction-grid {
    grid-template-columns: 1fr;
  }
  
  .test-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
