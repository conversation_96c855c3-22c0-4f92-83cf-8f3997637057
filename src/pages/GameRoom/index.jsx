import React, { useEffect, useRef } from 'react'
import { useParams } from 'react-router-dom'
import { message } from 'antd'
import { observer } from 'mobx-react-lite'
import { gameStore } from '../../store/gameStore'
import GameScene from '../../game/GameScene'

const GameRoom = observer(() => {
  const { gameId: _gameId, roomId } = useParams()
  const gameContainerRef = useRef(null)
  const gameInstanceRef = useRef(null)

  useEffect(() => {
    if (gameContainerRef.current && !gameInstanceRef.current) {
      try {
        gameInstanceRef.current = new GameScene(gameContainerRef.current)
        gameStore.setGameState('playing')
        gameStore.setCurrentRoom(roomId)
      } catch (error) {
        console.error('游戏初始化失败:', error)
        message.error('游戏初始化失败，请刷新页面重试')
      }
    }

    return () => {
      if (gameInstanceRef.current) {
        gameInstanceRef.current.destroy()
        gameInstanceRef.current = null
      }
    }
  }, [roomId])

  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      background: '#000',
      overflow: 'hidden',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <div
        ref={gameContainerRef}
        style={{
          width: '100%',
          height: '100%',
          maxWidth: '1200px',
          maxHeight: '800px',
          minWidth: '800px',
          minHeight: '600px'
        }}
      />
    </div>
  )
})

export default GameRoom
