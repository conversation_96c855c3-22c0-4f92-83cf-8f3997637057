import { useState } from 'react'
import { Button, message } from 'antd'
import GameScene from './game/GameScene'
import { TestPage } from '../game-client/web/src/components/TestPage'
import './App.css'

export default function App() {
  const [gameReady, setGameReady] = useState(false)
  const [showTestPage, setShowTestPage] = useState(false)

  const handleStartGame = () => {
    try {
      new GameScene()
      setGameReady(true)
    } catch (error) {
      console.error('Game initialization error:', error)
      message.error('游戏初始化失败，请刷新页面重试')
    }
  }

  const handleShowTestPage = () => {
    setShowTestPage(true)
  }

  const handleBackToHome = () => {
    setGameReady(false)
    setShowTestPage(false)
  }

  if (showTestPage) {
    return (
      <div className="app-container">
        <div style={{ position: 'fixed', top: '10px', left: '10px', zIndex: 1000 }}>
          <Button onClick={handleBackToHome}>
            返回首页
          </Button>
        </div>
        <TestPage />
      </div>
    )
  }

  return (
    <div className="app-container">
      {!gameReady ? (
        <div className="home-page">
          <h1>斗地主游戏</h1>
          <div style={{ display: 'flex', gap: '20px', justifyContent: 'center' }}>
            <Button type="primary" size="large" onClick={handleStartGame}>
              开始游戏 (Phaser版本)
            </Button>
            <Button type="default" size="large" onClick={handleShowTestPage}>
              UI组件测试
            </Button>
          </div>
        </div>
      ) : (
        <div>
          <div style={{ position: 'fixed', top: '10px', left: '10px', zIndex: 1000 }}>
            <Button onClick={handleBackToHome}>
              返回首页
            </Button>
          </div>
          <div id="game-container"></div>
        </div>
      )}
    </div>
  )
}
