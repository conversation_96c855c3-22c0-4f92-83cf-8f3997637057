import { useState } from 'react'
import { Button, message } from 'antd'
import GameScene from './game/GameScene'
import './App.css'

// 简化的TestPage组件用于测试
const TestPage = () => {
  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <h1>UI组件测试页面</h1>
      <p>这是新的React UI组件测试页面</p>
      <div style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '40px',
        borderRadius: '10px',
        color: 'white',
        margin: '20px 0'
      }}>
        <h2>现代化UI设计</h2>
        <p>✅ 响应式布局</p>
        <p>✅ 动画效果</p>
        <p>✅ 性能优化</p>
      </div>
    </div>
  )
}

// 简化的ErrorBoundary
const ErrorBoundary = ({ children }) => {
  return children
}

export default function App() {
  const [gameReady, setGameReady] = useState(false)
  const [showTestPage, setShowTestPage] = useState(false)

  const handleStartGame = () => {
    try {
      new GameScene()
      setGameReady(true)
    } catch (error) {
      console.error('Game initialization error:', error)
      message.error('游戏初始化失败，请刷新页面重试')
    }
  }

  const handleShowTestPage = () => {
    setShowTestPage(true)
  }

  const handleBackToHome = () => {
    setGameReady(false)
    setShowTestPage(false)
  }

  if (showTestPage) {
    return (
      <ErrorBoundary>
        <div className="app-container">
          <div style={{ position: 'fixed', top: '10px', left: '10px', zIndex: 1000 }}>
            <Button onClick={handleBackToHome}>
              返回首页
            </Button>
          </div>
          <TestPage />
        </div>
      </ErrorBoundary>
    )
  }

  return (
    <ErrorBoundary>
      <div className="app-container">
        {!gameReady ? (
          <div className="home-page">
            <h1>斗地主游戏</h1>
            <div style={{ display: 'flex', gap: '20px', justifyContent: 'center' }}>
              <Button type="primary" size="large" onClick={handleStartGame}>
                开始游戏 (Phaser版本)
              </Button>
              <Button type="default" size="large" onClick={handleShowTestPage}>
                UI组件测试
              </Button>
            </div>
          </div>
        ) : (
          <div>
            <div style={{ position: 'fixed', top: '10px', left: '10px', zIndex: 1000 }}>
              <Button onClick={handleBackToHome}>
                返回首页
              </Button>
            </div>
            <div id="game-container"></div>
          </div>
        )}
      </div>
    </ErrorBoundary>
  )
}
