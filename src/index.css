/* 全局CSS变量 */
:root {
  /* 字体 */
  --font-family: 'PingFang SC', 'Microsoft YaHei', system-ui, sans-serif;

  /* 主题色彩 */
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --info-color: #13c2c2;

  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f5f5f5;
  --bg-tertiary: #fafafa;
  --bg-dark: #001529;
  --bg-game: #0f5132;

  /* 文字色彩 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8c8c8c;
  --text-disabled: #bfbfbf;
  --text-white: #ffffff;

  /* 边框色彩 */
  --border-color: #d9d9d9;
  --border-light: #f0f0f0;
  --border-dark: #434343;

  /* 阴影 */
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 6px 16px rgba(0, 0, 0, 0.12);

  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  /* 动画时间 */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;

  /* Z-index层级 */
  --z-dropdown: 1050;
  --z-modal: 1060;
  --z-popover: 1070;
  --z-tooltip: 1080;
  --z-notification: 1090;
}

/* 全局重置样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-secondary);
}

#root {
  height: 100%;
  min-height: 100vh;
}

/* 移动端适配 */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }

  body {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.hidden {
  display: none;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

/* 响应式工具类 */
.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }

  .desktop-only {
    display: none;
  }
}

/* 游戏相关样式 */
.game-container {
  width: 100%;
  height: 100vh;
  background: var(--bg-game);
  overflow: hidden;
  position: relative;
}

.game-ui-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

.game-ui-overlay > * {
  pointer-events: auto;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-light);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 卡片阴影效果 */
.card-shadow {
  box-shadow: var(--shadow-light);
  transition: box-shadow var(--transition-fast);
}

.card-shadow:hover {
  box-shadow: var(--shadow-medium);
}

/* 按钮动画效果 */
.btn-animate {
  transition: all var(--transition-fast);
  transform: translateY(0);
}

.btn-animate:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-animate:active {
  transform: translateY(0);
}

/* 渐入动画 */
.fade-in {
  animation: fadeIn var(--transition-normal) ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滑入动画 */
.slide-in-up {
  animation: slideInUp var(--transition-normal) ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 缩放动画 */
.scale-in {
  animation: scaleIn var(--transition-fast) ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
