# DDZ斗地主游戏系统 - 前端开发文档

## 目录

- [1. 接口概述](#1-接口概述)
- [2. 通用返回格式](#2-通用返回格式)
- [3. 用户管理接口](#3-用户管理接口)
- [4. 账号管理接口](#4-账号管理接口)
- [5. 比赛配置接口](#5-比赛配置接口)
- [6. 报名管理接口](#6-报名管理接口)
- [7. 比赛详情接口](#7-比赛详情接口)
- [8. 游戏核心接口](#8-游戏核心接口)
- [9. 系统配置接口](#9-系统配置接口)
- [10. 数据模型](#10-数据模型)

## 1. 接口概述

### 基础信息

- **协议**: HTTP/HTTPS
- **请求方式**: POST
- **数据格式**: JSON
- **字符编码**: UTF-8
- **路由格式**: `[Controller]/[Action]`

### 认证说明

- 部分接口需要JWT认证，请在请求头中添加：`Authorization: Bearer {token}`
- 标记为`[JwtAttribute]`的接口需要认证

## 2. 通用返回格式

所有接口统一返回格式：

```json
{
  "code": 0,           // 状态码：0-成功，1-失败，-1-异常
  "msg": "请求成功",    // 返回消息
  "result": {},        // 返回数据（可为对象、数组或基础类型）
  "count": 10          // 数据总数（分页接口使用）
}
```

### 状态码说明

- `0`: 请求成功
- `1`: 请求失败（业务逻辑错误）
- `-1`: 系统异常

## 3. 用户管理接口

### 3.1 获取用户列表

**接口地址**: `User/GetUserList`  
**请求方式**: POST  
**需要认证**: 是

**请求参数**:

```json
{
  "openid": "微信openid",
  "nickname": "微信昵称",
  "star": "2024-01-01T00:00:00",  // 开始访问时间
  "end": "2024-12-31T23:59:59",   // 结束访问时间
  "PageSize": 10,                 // 页面大小，默认10
  "PageNo": 1                     // 页码，默认1
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "result": [
    {
      "id": 1,
      "openid": "wx_openid_123",
      "headerimg": "头像URL",
      "nickname": "用户昵称",
      "unionid": "wx_unionid_123",
      "time": "2024-01-01T10:00:00"
    }
  ],
  "count": 100
}
```

### 3.2 根据ID获取用户

**接口地址**: `User/GetUserByID`  
**请求方式**: POST

**请求参数**:

```json
{
  "id": 1
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "result": {
    "id": 1,
    "openid": "wx_openid_123",
    "headerimg": "头像URL",
    "nickname": "用户昵称",
    "unionid": "wx_unionid_123",
    "time": "2024-01-01T10:00:00"
  }
}
```

### 3.3 通过Code获取用户信息

**接口地址**: `User/GetUserByCode`  
**请求方式**: POST

**请求参数**:

```json
{
  "code": "微信授权code"
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "result": {
    "id": 1,
    "openid": "wx_openid_123",
    "headerimg": "头像URL",
    "nickname": "用户昵称",
    "unionid": "wx_unionid_123",
    "time": "2024-01-01T10:00:00"
  },
  "token": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 7200,
    "token_type": "Bearer"
  }
}
```

### 3.4 通过OpenID获取用户信息

**接口地址**: `User/GetUserInfoByOpenid`  
**请求方式**: POST

**请求参数**:

```json
{
  "openid": "wx_openid_123"
}
```

**返回示例**: 同3.3

## 4. 账号管理接口

### 4.1 获取账号列表

**接口地址**: `Account/GetAccountList`  
**请求方式**: POST  
**需要认证**: 是

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "result": [
    {
      "id": 1,
      "name": "管理员",
      "time": "2024-01-01T10:00:00",
      "accountid": "admin"
    }
  ]
}
```

### 4.2 保存账号信息

**接口地址**: `Account/SaveAccount`  
**请求方式**: POST  
**需要认证**: 是

**请求参数**:

```json
{
  "id": 1,              // 可选，更新时传入
  "name": "账号名称",
  "accountid": "登录账号",
  "password": "密码"
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功"
}
```

### 4.3 账号登录

**接口地址**: `Account/Login`  
**请求方式**: POST

**请求参数**:

```json
{
  "accountid": "admin",
  "password": "123456"
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "account": {
    "accountid": "admin",
    "name": "管理员"
  },
  "token": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 7200,
    "token_type": "Bearer"
  }
}
```

### 4.4 账号登出

**接口地址**: `Account/LogOut`  
**请求方式**: POST

**请求参数**:

```json
{
  "accountid": "admin"
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功"
}
```

## 5. 比赛配置接口

### 5.1 获取比赛配置列表

**接口地址**: `GameConfig/GetGameConfigList`  
**请求方式**: POST

**请求参数**:

```json
{
  "name": "比赛名称",
  "star": "2024-01-01T00:00:00",  // 开始时间
  "end": "2024-12-31T23:59:59",   // 结束时间
  "PageSize": 10,
  "PageNo": 1
}
```

### 5.2 获取最新比赛

**接口地址**: `GameConfig/GetGameConfigLast`  
**请求方式**: POST

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "result": {
    "gc_id": 1,
    "gc_name": "春季斗地主大赛",
    "gc_time": "2024-01-01T10:00:00",
    "gc_bmstartime": "2024-01-01T09:00:00",
    "gc_bmendtime": "2024-01-01T18:00:00",
    "gc_qdstartime": "2024-01-01T19:00:00",
    "gc_qdendtime": "2024-01-01T19:30:00",
    "gc_startime": "2024-01-01T20:00:00"
  }
}
```

### 5.3 获取比赛详情

**接口地址**: `GameConfig/GetGameConfig`  
**请求方式**: POST  
**需要认证**: 是

**请求参数**:

```json
{
  "id": 1
}
```

### 5.4 保存比赛配置

**接口地址**: `GameConfig/SaveGameConfig`  
**请求方式**: POST  
**需要认证**: 是

**请求参数**: 完整的game_config对象

### 5.5 禁用比赛

**接口地址**: `GameConfig/EnableConfig`  
**请求方式**: POST  
**需要认证**: 是

**请求参数**:

```json
{
  "id": 1
}
```

## 6. 报名管理接口

### 6.1 获取报名列表

**接口地址**: `GameSign/GetGameSignList`  
**请求方式**: POST  
**需要认证**: 是

**请求参数**:

```json
{
  "uid": 1,             // 用户ID，可选
  "gcid": 1,            // 比赛ID，可选
  "star": "2024-01-01T00:00:00",
  "end": "2024-12-31T23:59:59",
  "PageSize": 10,
  "PageNo": 1
}
```

### 6.2 用户报名

**接口地址**: `GameSign/SaveGameSign`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1
}
```

### 6.3 用户签到

**接口地址**: `GameSign/SaveGameQianDao`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1
}
```

## 7. 比赛详情接口

### 7.1 获取比赛详情列表

**接口地址**: `GameDetail/GetGameDetailList`  
**请求方式**: POST  
**需要认证**: 是

**请求参数**:

```json
{
  "gcid": 1,            // 比赛ID，可选
  "uid": 1,             // 用户ID，可选
  "star": "2024-01-01T00:00:00",
  "end": "2024-12-31T23:59:59",
  "PageSize": 10,
  "PageNo": 1
}
```

## 8. 游戏核心接口

### 8.1 获取最新游戏配置

**接口地址**: `GameStar/GetGameConfig`  
**请求方式**: POST

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "data": {
    "gc_id": 1,
    "gc_name": "春季斗地主大赛",
    "gc_startime": "2024-01-01T20:00:00"
  }
}
```

### 8.2 获取报名列表

**接口地址**: `GameStar/GetSignUpList`  
**请求方式**: POST

**请求参数**:

```json
{
  "gcid": 1
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "data": [
    {
      "uid": 1,
      "nickname": "玩家1",
      "signup_time": "2024-01-01T15:00:00"
    }
  ]
}
```

### 8.3 用户报名

**接口地址**: `GameStar/SignUp`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1
}
```

### 8.4 取消报名

**接口地址**: `GameStar/CancelSignUp`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1
}
```

### 8.5 用户签到

**接口地址**: `GameStar/SignIn`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1
}
```

### 8.6 获取房间信息

**接口地址**: `GameStar/GetRoom`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "data": {
    "table_number": 1,
    "round": 1,
    "game": 1,
    "status": "Playing",
    "players": [
      {
        "uid": 1,
        "username": "玩家名称",
        "position": "West",
        "is_ready": true,
        "is_auto": false,
        "score": 0
      }
    ],
    "current_player": 1,
    "landlord_id": 2,
    "current_bid_score": 2,
    "bomb_multiplier": 1
  }
}
```

### 8.7 抢地主

**接口地址**: `GameStar/GetLandlords`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1,
  "room": 1,
  "round": 1,
  "game": 1,
  "point": 2            // 叫分：0-3
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "data": {
    "bottom_cards": [1, 2, 3],
    "hand_cards": [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
    "total_cards": 20,
    "is_landlord": true,
    "bid_score": 2
  }
}
```

### 8.8 叫地主

**接口地址**: `GameStar/BidLandlord`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1,
  "room": 1,
  "round": 1,
  "game": 1,
  "point": 2            // 叫牌分数（1-3分，0表示不叫）
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "叫地主成功",
  "data": {
    "current_bid_score": 2,
    "next_player": 2,
    "is_bid_finished": false,
    "landlord_id": null
  }
}
```

### 8.9 出牌

**接口地址**: `GameStar/GamePlay`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1,
  "room": 1,
  "round": 1,
  "game": 1,
  "pokers": "1,2,3"     // 出牌数据（逗号分隔的牌值）
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "出牌成功",
  "data": {
    "card_type": "Pair",
    "next_player": 2,
    "is_game_over": false,
    "winner": null
  }
}
```

### 8.10 过牌（不出）

**接口地址**: `GameStar/PassCards`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1,
  "room": 1,
  "round": 1,
  "game": 1
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "过牌成功",
  "data": {
    "next_player": 2
  }
}
```

### 8.11 托管

**接口地址**: `GameStar/TrusteeShip`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1,
  "room": 1,
  "round": 1,
  "game": 1
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "托管成功"
}
```

### 8.12 取消托管

**接口地址**: `GameStar/CancelTrusteeShip`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1,
  "room": 1,
  "round": 1,
  "game": 1
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "取消托管成功"
}
```

### 8.13 确定继续下一局

**接口地址**: `GameStar/ConfirmToStart`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1,
  "room": 1,
  "round": 1
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "确认成功",
  "data": {
    "ready": true,
    "can_start": false
  }
}
```

### 8.14 获取排名

**接口地址**: `GameStar/GetRanking`  
**请求方式**: POST

**请求参数**:

```json
{
  "gcid": 1
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "data": [
    {
      "rank": 1,
      "uid": 1,
      "username": "玩家名称",
      "total_score": 100,
      "first_count": 5,
      "top_two_count": 8,
      "small_score_sum": 150
    }
  ]
}
```

### 8.15 获取WebSocket连接信息

**接口地址**: `GameStar/GetHubs`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "data": {
    "hub_url": "/signalrhub",
    "connection_id": "abc123",
    "user_id": 1,
    "match_id": 123,
    "supported_events": [
      "PlayerJoined",
      "PlayerLeft",
      "RoomAssigned",
      "PlayerReady",
      "PlayerBid",
      "PlayerNoBid",
      "BidFinished",
      "PlayerPlay",
      "PlayerPass",
      "GameFinished",
      "RoundFinished",
      "MatchFinished",
      "PlayerAutoPlay",
      "PlayerReconnect",
      "PlayerTimeout",
      "SystemMessage"
    ]
  }
}
```

### 8.16 获取玩家手牌

**接口地址**: `GameStar/GetHandCards`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1,
  "room": 1,
  "round": 1,
  "game": 1
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "data": {
    "hand_cards": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17],
    "card_count": 17,
    "is_landlord": false,
    "position": "West",
    "is_auto": false,
    "can_bid": false,
    "can_play": true
  }
}
```

### 8.17 获取游戏状态

**接口地址**: `GameStar/GetGameState`  
**请求方式**: POST

**请求参数**:

```json
{
  "uid": 1,
  "gcid": 1,
  "room": 1,
  "round": 1,
  "game": 1
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "data": {
    "match_id": "123",
    "table_number": 1,
    "round": 1,
    "game": 1,
    "status": "Playing",
    "current_bidder": null,
    "current_player": 1,
    "landlord_id": 2,
    "current_bid_score": 2,
    "bomb_multiplier": 1,
    "bid_time_limit": 15,
    "play_time_limit": 20,
    "last_played_cards": {
      "player_id": 3,
      "cards": [1, 2],
      "card_type": "Pair",
      "is_pass": false
    },
    "players": [
      {
        "uid": 1,
        "username": "玩家1",
        "position": "West",
        "card_count": 15,
        "bid_score": 0,
        "has_bid": true,
        "is_auto": false,
        "game_score": 0
      }
    ]
  }
}
```

## 9. 系统配置接口

### 9.1 获取系统配置

**接口地址**: `System/GetSystemConfig`  
**请求方式**: POST

**请求参数**:

```json
{
  "type": "配置类型"
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "result": "配置内容"
}
```

### 9.2 保存系统配置

**接口地址**: `System/SaveSystemConfig`  
**请求方式**: POST  
**需要认证**: 是

**请求参数**:

```json
{
  "type": "配置类型",
  "result": "配置内容"
}
```

### 9.3 保存日志

**接口地址**: `System/Log`  
**请求方式**: POST

### 9.4 文件上传

**接口地址**: `System/FileUpload`  
**请求方式**: POST  
**Content-Type**: multipart/form-data

**返回示例**:

```json
{
  "code": 0,
  "msg": "请求成功",
  "result": "File/********/********120000.jpg"
}
```

## 10. 数据模型

### 10.1 用户模型 (user)

```json
{
  "u_id": 1,                           // 用户ID
  "u_openid": "wx_openid_123",         // 微信OpenID
  "u_unionid": "wx_unionid_123",       // 微信UnionID
  "u_nickname": "用户昵称",             // 微信昵称
  "u_headerimg": "头像URL",             // 微信头像
  "u_time": "2024-01-01T10:00:00"      // 进入系统时间
}
```

### 10.2 账号模型 (account)

```json
{
  "a_id": 1,                           // 账号ID
  "a_accountid": "admin",              // 登录账号
  "a_password": "加密密码",             // 密码（加密）
  "a_name": "管理员",                   // 账号名称
  "a_time": "2024-01-01T10:00:00"      // 创建时间
}
```

### 10.3 比赛配置模型 (game_config)

```json
{
  "gc_id": 1,                          // 比赛ID
  "gc_name": "春季斗地主大赛",           // 比赛名称
  "gc_time": "2024-01-01T10:00:00",    // 比赛创建时间
  "gc_bmstartime": "2024-01-01T09:00:00", // 报名开始时间
  "gc_bmendtime": "2024-01-01T18:00:00",  // 报名结束时间
  "gc_qdstartime": "2024-01-01T19:00:00", // 签到开始时间
  "gc_qdendtime": "2024-01-01T19:30:00",  // 签到结束时间
  "gc_startime": "2024-01-01T20:00:00"    // 游戏开始时间
}
```

### 10.4 报名模型 (game_sign)

```json
{
  "s_id": 1,                           // 报名ID
  "s_uid": 1,                          // 用户ID
  "s_gcid": 1,                         // 比赛ID
  "s_time": "2024-01-01T15:00:00",     // 报名时间
  "s_qdtime": "2024-01-01T19:15:00"    // 签到时间
}
```

### 10.5 系统配置模型 (system)

```json
{
  "s_id": 1,                           // 配置ID
  "s_type": "系统配置",              // 配置类型
  "s_result": "{"brandLogo": "http://localhost:5000/File/20250618/20250618191105.jpg", "brandName": "斗地主游戏41", "pokerBackground": "http://localhost:5000/File/20250618/20250618191110.jpg", "landlordWaitTime": 15, "playCardWaitTime": 30}",           // 配置内容
  "s_time": "2024-01-01T10:00:00"      // 创建时间
}
```

## 错误处理

### 常见错误码

- `0`: 成功
- `1`: 业务逻辑错误（如：用户不存在、密码错误等）
- `-1`: 系统异常（如：数据库连接失败、服务器内部错误等）

### 错误响应示例

```json
{
  "code": 1,
  "msg": "用户不存在"
}
```

```json
{
  "code": -1,
  "msg": "系统异常，请稍后重试"
}
```

## 注意事项

1. **时间格式**: 所有时间字段使用ISO 8601格式：`YYYY-MM-DDTHH:mm:ss`
2. **分页**: 分页参数PageNo从1开始，PageSize默认为10
3. **认证**: 需要认证的接口必须在请求头中携带有效的JWT Token
4. **文件上传**: 文件上传接口返回的是相对路径，需要拼接服务器域名使用
5. **缓存**: 部分接口使用了Redis缓存，数据可能有延迟
6. **WebSocket**: 游戏实时功能使用SignalR，需要建立WebSocket连接

## 联系方式

如有疑问，请联系开发团队。

---

*文档版本: v1.0*  
*最后更新: 2024-01-01*
