<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .failure {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-details {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🤖 AI修复测试</h1>
    
    <div class="test-container">
        <div class="test-title">测试1：AI叫牌测试 - 弱牌</div>
        <div class="test-details">
            <strong>测试描述：</strong>测试AI在拿到弱牌时的叫牌决策<br>
            <strong>AI手牌：</strong>3♠ 4♥ 5♠ 6♥ 7♠ 8♥ 9♠ 10♥ J♠ Q♥ K♠ A♥ 2♠<br>
            <strong>预期行为：</strong>应该不叫（分数低于35分阈值）
        </div>
        <button onclick="testWeakHandBidding()">运行测试</button>
        <div id="test1-result"></div>
    </div>

    <div class="test-container">
        <div class="test-title">测试2：AI出牌测试 - 无法压过</div>
        <div class="test-details">
            <strong>测试描述：</strong>测试AI在无法压过时的决策<br>
            <strong>上家出牌：</strong>2♠<br>
            <strong>AI手牌：</strong>3♠ 4♥ 5♠ 6♥ 7♠<br>
            <strong>预期行为：</strong>应该选择过牌
        </div>
        <button onclick="testCannotBeatPlay()">运行测试</button>
        <div id="test2-result"></div>
    </div>

    <div class="test-container">
        <div class="test-title">测试3：AI叫牌测试 - 强牌</div>
        <div class="test-details">
            <strong>测试描述：</strong>测试AI在拿到强牌时的叫牌决策<br>
            <strong>AI手牌：</strong>A♠ A♥ A♣ K♠ K♥ Q♠ Q♥ 2♠ 2♥ J♠ 10♠ 9♠ 8♠<br>
            <strong>预期行为：</strong>应该叫地主（分数高于35分阈值）
        </div>
        <button onclick="testStrongHandBidding()">运行测试</button>
        <div id="test3-result"></div>
    </div>

    <script type="module">
        // 模拟卡牌类
        class Card {
            constructor(rank, suit) {
                this.rank = rank;
                this.suit = suit;
            }
            
            getDisplayName() {
                return `${this.rank}${this.suit}`;
            }
        }

        // 模拟AI叫牌逻辑（从GameScene.js复制）
        function shouldAIBid(cards) {
            console.log('🤖 AI叫牌分析开始:');
            console.log(`📋 AI手牌: ${cards.map(c => c.getDisplayName()).join(' ')}`);

            const analysis = {
                details: [],
                totalScore: 0
            };

            // 统计牌型
            const cardCounts = {};
            cards.forEach(card => {
                cardCounts[card.rank] = (cardCounts[card.rank] || 0) + 1;
            });

            let score = 0;

            // 对子加分
            Object.entries(cardCounts).forEach(([rank, count]) => {
                if (count >= 2) {
                    const pairScore = (count - 1) * 5;
                    score += pairScore;
                    analysis.details.push(`${rank}×${count}: +${pairScore}分`);
                }
            });

            // 三张加分
            Object.entries(cardCounts).forEach(([rank, count]) => {
                if (count >= 3) {
                    const tripleScore = (count - 2) * 8;
                    score += tripleScore;
                    analysis.details.push(`三张${rank}: +${tripleScore}分`);
                }
            });

            // 炸弹加分
            Object.entries(cardCounts).forEach(([rank, count]) => {
                if (count >= 4) {
                    const bombScore = 20;
                    score += bombScore;
                    analysis.details.push(`炸弹${rank}: +${bombScore}分`);
                }
            });

            // 大牌加分（A, K, Q, J, 2）
            const bigCards = ['A', 'K', 'Q', 'J', '2'];
            let bigCardScore = 0;
            bigCards.forEach(rank => {
                if (cardCounts[rank]) {
                    const cardScore = cardCounts[rank] * 3;
                    bigCardScore += cardScore;
                    analysis.details.push(`大牌${rank}×${cardCounts[rank]}: +${cardScore}分`);
                }
            });
            score += bigCardScore;

            // 调整叫牌阈值 - 更严格的标准
            analysis.totalScore = score;
            const shouldBid = score >= 35; // 提高阈值到35分，避免弱牌叫地主

            console.log('📊 分析详情:');
            analysis.details.forEach(detail => console.log(`  ${detail}`));
            console.log(`🎯 总分: ${score}分 (阈值: 35分)`);
            console.log(`🎲 决策: ${shouldBid ? '叫地主' : '不叫'}`);

            return { shouldBid, score, analysis };
        }

        // 模拟AI出牌逻辑
        function findBestAIPlay(cards, lastPlayedCards) {
            console.log(`🤖 AI出牌分析开始:`);
            console.log(`📋 AI手牌: ${cards.map(c => c.getDisplayName()).join(' ')}`);

            // 如果是首出，选择最小的单牌
            if (!lastPlayedCards || lastPlayedCards.length === 0) {
                console.log(`🎯 首次出牌，选择最小单牌`);
                const result = findSmallestSingle(cards);
                console.log(`✅ 选择出牌: ${result ? result.map(c => c.getDisplayName()).join(' ') : '无'}`);
                return result;
            }

            // 尝试找到能够压过上家的牌
            console.log(`📤 上家出牌: ${lastPlayedCards.map(c => c.getDisplayName()).join(' ')}`);
            
            // 简化：只处理单牌情况
            if (lastPlayedCards.length === 1) {
                console.log(`🔍 寻找更大的单牌...`);
                const result = findBiggerSingle(cards, lastPlayedCards[0]);
                if (result) {
                    console.log(`✅ 找到可出牌: ${result.map(c => c.getDisplayName()).join(' ')}`);
                } else {
                    console.log(`❌ 无法压过，选择过牌`);
                }
                return result;
            }

            console.log(`⚠️ 复杂牌型暂不支持，选择过牌`);
            return null;
        }

        function findSmallestSingle(cards) {
            const sortedCards = [...cards].sort((a, b) => getCardValue(a) - getCardValue(b));
            return [sortedCards[0]];
        }

        function findBiggerSingle(cards, lastCard) {
            const lastValue = getCardValue(lastCard);
            const biggerCards = cards.filter(card => getCardValue(card) > lastValue);

            if (biggerCards.length > 0) {
                const sortedBigger = biggerCards.sort((a, b) => getCardValue(a) - getCardValue(b));
                return [sortedBigger[0]];
            }
            return null;
        }

        function getCardValue(card) {
            const rankValues = {
                '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
                'J': 11, 'Q': 12, 'K': 13, 'A': 14, '2': 15,
                'small_joker': 16, 'big_joker': 17
            };
            return rankValues[card.rank] || 0;
        }

        // 测试函数
        window.testWeakHandBidding = function() {
            const cards = [
                new Card('3', '♠'), new Card('4', '♥'), new Card('5', '♠'), 
                new Card('6', '♥'), new Card('7', '♠'), new Card('8', '♥'), 
                new Card('9', '♠'), new Card('10', '♥'), new Card('J', '♠'), 
                new Card('Q', '♥'), new Card('K', '♠'), new Card('A', '♥'), 
                new Card('2', '♠')
            ];

            const result = shouldAIBid(cards);
            const resultDiv = document.getElementById('test1-result');
            
            if (!result.shouldBid) {
                resultDiv.innerHTML = `
                    <div class="test-result success">
                        ✅ 测试通过！AI正确选择不叫地主
                        <div class="test-details">
                            总分: ${result.score}分 (阈值: 35分)<br>
                            决策: 不叫地主
                        </div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="test-result failure">
                        ❌ 测试失败！AI错误地叫了地主
                        <div class="test-details">
                            总分: ${result.score}分 (阈值: 35分)<br>
                            决策: 叫地主（应该不叫）
                        </div>
                    </div>
                `;
            }
        };

        window.testCannotBeatPlay = function() {
            const aiCards = [
                new Card('3', '♠'), new Card('4', '♥'), new Card('5', '♠'), 
                new Card('6', '♥'), new Card('7', '♠')
            ];
            const lastCard = new Card('2', '♠');

            const result = findBestAIPlay(aiCards, [lastCard]);
            const resultDiv = document.getElementById('test2-result');
            
            if (!result) {
                resultDiv.innerHTML = `
                    <div class="test-result success">
                        ✅ 测试通过！AI正确选择过牌
                        <div class="test-details">
                            AI无法压过2♠，正确选择过牌
                        </div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="test-result failure">
                        ❌ 测试失败！AI错误地尝试出牌
                        <div class="test-details">
                            AI出牌: ${result.map(c => c.getDisplayName()).join(' ')}<br>
                            应该选择过牌
                        </div>
                    </div>
                `;
            }
        };

        window.testStrongHandBidding = function() {
            const cards = [
                new Card('A', '♠'), new Card('A', '♥'), new Card('A', '♣'), 
                new Card('K', '♠'), new Card('K', '♥'), new Card('Q', '♠'), 
                new Card('Q', '♥'), new Card('2', '♠'), new Card('2', '♥'), 
                new Card('J', '♠'), new Card('10', '♠'), new Card('9', '♠'), 
                new Card('8', '♠')
            ];

            const result = shouldAIBid(cards);
            const resultDiv = document.getElementById('test3-result');
            
            if (result.shouldBid) {
                resultDiv.innerHTML = `
                    <div class="test-result success">
                        ✅ 测试通过！AI正确选择叫地主
                        <div class="test-details">
                            总分: ${result.score}分 (阈值: 35分)<br>
                            决策: 叫地主
                        </div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="test-result failure">
                        ❌ 测试失败！AI错误地不叫地主
                        <div class="test-details">
                            总分: ${result.score}分 (阈值: 35分)<br>
                            决策: 不叫（应该叫地主）
                        </div>
                    </div>
                `;
            }
        };
    </script>
</body>
</html>
