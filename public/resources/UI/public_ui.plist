<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>head_img_female.png</key>
            <dict>
                <key>frame</key>
                <string>{{642,117},{102,102}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{102,102}}</string>
                <key>sourceSize</key>
                <string>{102,102}</string>
            </dict>
            <key>head_img_male.png</key>
            <dict>
                <key>frame</key>
                <string>{{538,117},{102,102}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{102,102}}</string>
                <key>sourceSize</key>
                <string>{102,102}</string>
            </dict>
            <key>head_mask.png</key>
            <dict>
                <key>frame</key>
                <string>{{746,155},{64,64}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{64,64}}</string>
                <key>sourceSize</key>
                <string>{64,64}</string>
            </dict>
            <key>player_info_bg.png</key>
            <dict>
                <key>frame</key>
                <string>{{146,2},{208,209}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{208,209}}</string>
                <key>sourceSize</key>
                <string>{208,209}</string>
            </dict>
            <key>public_bar_btm.png</key>
            <dict>
                <key>frame</key>
                <string>{{746,105},{1274,48}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{1274,48}}</string>
                <key>sourceSize</key>
                <string>{1274,48}</string>
            </dict>
            <key>public_bar_top.png</key>
            <dict>
                <key>frame</key>
                <string>{{746,2},{1280,101}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{1280,101}}</string>
                <key>sourceSize</key>
                <string>{1280,101}</string>
            </dict>
            <key>public_btn_back.png</key>
            <dict>
                <key>frame</key>
                <string>{{812,155},{78,56}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{78,56}}</string>
                <key>sourceSize</key>
                <string>{78,56}</string>
            </dict>
            <key>public_btn_cancel.png</key>
            <dict>
                <key>frame</key>
                <string>{{74,2},{221,70}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{221,70}}</string>
                <key>sourceSize</key>
                <string>{221,70}</string>
            </dict>
            <key>public_btn_close.png</key>
            <dict>
                <key>frame</key>
                <string>{{653,2},{78,78}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{78,78}}</string>
                <key>sourceSize</key>
                <string>{78,78}</string>
            </dict>
            <key>public_btn_ok.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{221,70}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{221,70}}</string>
                <key>sourceSize</key>
                <string>{221,70}</string>
            </dict>
            <key>public_frame_a.png</key>
            <dict>
                <key>frame</key>
                <string>{{146,2},{208,209}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{208,209}}</string>
                <key>sourceSize</key>
                <string>{208,209}</string>
            </dict>
            <key>public_frame_b.png</key>
            <dict>
                <key>frame</key>
                <string>{{356,2},{180,180}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{180,180}}</string>
                <key>sourceSize</key>
                <string>{180,180}</string>
            </dict>
            <key>public_frame_head.png</key>
            <dict>
                <key>frame</key>
                <string>{{538,2},{113,113}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{113,113}}</string>
                <key>sourceSize</key>
                <string>{113,113}</string>
            </dict>
            <key>sex_female.png</key>
            <dict>
                <key>frame</key>
                <string>{{939,155},{45,46}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{45,46}}</string>
                <key>sourceSize</key>
                <string>{45,46}</string>
            </dict>
            <key>sex_male.png</key>
            <dict>
                <key>frame</key>
                <string>{{892,155},{45,46}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{45,46}}</string>
                <key>sourceSize</key>
                <string>{45,46}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>public_ui.png</string>
            <key>size</key>
            <string>{2028,225}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:aa848dcfde04174972b1051b88a6043a:1/1$</string>
            <key>textureFileName</key>
            <string>public_ui.png</string>
        </dict>
    </dict>
</plist>
